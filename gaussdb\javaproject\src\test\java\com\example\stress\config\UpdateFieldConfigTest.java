package com.example.stress.config;

import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * UpdateFieldConfig测试类
 */
public class UpdateFieldConfigTest {
    private static final Logger logger = LoggerFactory.getLogger(UpdateFieldConfigTest.class);
    
    @Test
    public void testGetUpdateFields() {
        List<String> updateFields = UpdateFieldConfig.getUpdateFields();
        logger.info("Update fields: {}", updateFields);
        
        // 验证配置是否正确加载
        assertNotNull(updateFields);
        assertTrue(updateFields.contains("c_km_code"));
        assertTrue(updateFields.contains("c_km_name"));
    }
    
    @Test
    public void testIsUpdateField() {
        assertTrue(UpdateFieldConfig.isUpdateField("c_km_code"));
        assertTrue(UpdateFieldConfig.isUpdateField("c_km_name"));
        assertFalse(UpdateFieldConfig.isUpdateField("c_port_code"));
    }
    
    @Test
    public void testGetUpdateValue() {
        String kmCodeValue = UpdateFieldConfig.getUpdateValue("c_km_code");
        String kmNameValue = UpdateFieldConfig.getUpdateValue("c_km_name");
        
        logger.info("c_km_code value: {}", kmCodeValue);
        logger.info("c_km_name value: {}", kmNameValue);
        
        assertEquals("4103.02.02.007491", kmCodeValue);
        assertEquals("交易性股票投资公允价值变动损益", kmNameValue);
    }
    
    @Test
    public void testGetUpdateValueType() {
        String kmCodeType = UpdateFieldConfig.getUpdateValueType("c_km_code");
        String kmNameType = UpdateFieldConfig.getUpdateValueType("c_km_name");
        
        assertEquals("string", kmCodeType);
        assertEquals("string", kmNameType);
    }
}
