package com.example.stress.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 压力测试配置类
 * 管理压力测试的各种参数
 */
public class StressTestConfig {
    private static final Logger logger = LoggerFactory.getLogger(StressTestConfig.class);
    
    // 默认配置值
    private static final int DEFAULT_CONCURRENCY = 10;
    private static final int DEFAULT_PORT_CODE_COUNT = 50;
    private static final int DEFAULT_RECORDS_PER_PORT_CODE = 100;
    private static final int DEFAULT_TEST_ROUNDS = 1;
    private static final long DEFAULT_ROUND_INTERVAL_MS = 1000;
    private static final boolean DEFAULT_INIT_DATA = true;
    private static final boolean DEFAULT_PRINT_DETAILED_RESULTS = false;
    private static final int DEFAULT_BATCH_SIZE = 2000;
    private static final String DEFAULT_PLAN_CODE = "jk1000Fa42";
    
    // 配置参数
    private int concurrency;
    private int portCodeCount;
    private int recordsPerPortCode;
    private int testRounds;
    private long roundIntervalMs;
    private boolean initData;
    private boolean printDetailedResults;
    private int batchSize;
    private String planCode;
    
    public StressTestConfig() {
        // 使用默认值初始化
        this.concurrency = DEFAULT_CONCURRENCY;
        this.portCodeCount = DEFAULT_PORT_CODE_COUNT;
        this.recordsPerPortCode = DEFAULT_RECORDS_PER_PORT_CODE;
        this.testRounds = DEFAULT_TEST_ROUNDS;
        this.roundIntervalMs = DEFAULT_ROUND_INTERVAL_MS;
        this.initData = DEFAULT_INIT_DATA;
        this.printDetailedResults = DEFAULT_PRINT_DETAILED_RESULTS;
        this.batchSize = DEFAULT_BATCH_SIZE;
        this.planCode = DEFAULT_PLAN_CODE;
    }
    
    /**
     * 从命令行参数解析配置
     */
    public static StressTestConfig fromArgs(String[] args) {
        StressTestConfig config = new StressTestConfig();
        
        for (int i = 0; i < args.length; i++) {
            String arg = args[i];
            
            try {
                switch (arg) {
                    case "--concurrency":
                    case "-c":
                        if (i + 1 < args.length) {
                            config.concurrency = Integer.parseInt(args[++i]);
                        }
                        break;
                    case "--port-codes":
                    case "-p":
                        if (i + 1 < args.length) {
                            config.portCodeCount = Integer.parseInt(args[++i]);
                        }
                        break;
                    case "--records-per-port":
                    case "-r":
                        if (i + 1 < args.length) {
                            config.recordsPerPortCode = Integer.parseInt(args[++i]);
                        }
                        break;
                    case "--rounds":
                        if (i + 1 < args.length) {
                            config.testRounds = Integer.parseInt(args[++i]);
                        }
                        break;
                    case "--interval":
                        if (i + 1 < args.length) {
                            config.roundIntervalMs = Long.parseLong(args[++i]);
                        }
                        break;
                    case "--no-init":
                        config.initData = false;
                        break;
                    case "--detailed":
                        config.printDetailedResults = true;
                        break;
                    case "--batch-size":
                    case "-b":
                        if (i + 1 < args.length) {
                            config.batchSize = Integer.parseInt(args[++i]);
                        }
                        break;
                    case "--plan-code":
                    case "-f":
                        if (i + 1 < args.length) {
                            config.planCode = args[++i];
                        }
                        break;    
                    case "--help":
                    case "-h":
                        printUsage();
                        System.exit(0);
                        break;
                    default:
                        if (arg.startsWith("-")) {
                            logger.warn("Unknown argument: {}", arg);
                        }
                        break;
                }
            } catch (NumberFormatException e) {
                logger.error("Invalid number format for argument {}: {}", arg, args[i]);
                System.exit(1);
            }
        }
        
        // 验证配置
        config.validate();
        
        return config;
    }
    
    /**
     * 验证配置参数
     */
    private void validate() {
        if (concurrency <= 0) {
            throw new IllegalArgumentException("Concurrency must be positive, got: " + concurrency);
        }
        if (concurrency > 1000) {
            logger.warn("High concurrency level: {}. This may cause resource issues.", concurrency);
        }
        
        if (portCodeCount <= 0) {
            throw new IllegalArgumentException("Port code count must be positive, got: " + portCodeCount);
        }
        
        if (recordsPerPortCode <= 0) {
            throw new IllegalArgumentException("Records per port code must be positive, got: " + recordsPerPortCode);
        }
        
        if (testRounds <= 0) {
            throw new IllegalArgumentException("Test rounds must be positive, got: " + testRounds);
        }
        
        if (roundIntervalMs < 0) {
            throw new IllegalArgumentException("Round interval must be non-negative, got: " + roundIntervalMs);
        }

        if (batchSize <= 0) {
            throw new IllegalArgumentException("Batch size must be positive, got: " + batchSize);
        }

        if (planCode == null || "".equals(planCode) ) {
            throw new IllegalArgumentException("Plan code must be given, got: " + planCode);
        }
        
        // 计算总记录数并警告如果太大
        long totalRecords = (long) portCodeCount * recordsPerPortCode;
        if (totalRecords > 1_000_000) {
            logger.warn("Large dataset: {} total records. This may take significant time and resources.", totalRecords);
        }
    }
    
    /**
     * 打印使用说明
     */
    public static void printUsage() {
        System.out.println("GaussDB Stress Test Tool");
        System.out.println("Usage: java -jar stress-test.jar [options]");
        System.out.println();
        System.out.println("Options:");
        System.out.println("  -c, --concurrency <num>      Number of concurrent threads (default: " + DEFAULT_CONCURRENCY + ")");
        System.out.println("  -p, --port-codes <num>       Number of different port codes (default: " + DEFAULT_PORT_CODE_COUNT + ")");
        System.out.println("  -r, --records-per-port <num> Records per port code (default: " + DEFAULT_RECORDS_PER_PORT_CODE + ")");
        System.out.println("  -b, --batch-size <num>       Batch size for processing (default: " + DEFAULT_BATCH_SIZE + ")");
        System.out.println("  -f, --plan-code <planName>   Plan which gives the specific portfolio list for processing (default: " + DEFAULT_BATCH_SIZE + ")");
        System.out.println("  --rounds <num>               Number of test rounds (default: " + DEFAULT_TEST_ROUNDS + ")");
        System.out.println("  --interval <ms>              Interval between rounds in ms (default: " + DEFAULT_ROUND_INTERVAL_MS + ")");
        System.out.println("  --no-init                    Skip data initialization");
        System.out.println("  --detailed                   Print detailed results for each worker");
        System.out.println("  -h, --help                   Show this help message");
        System.out.println();
        System.out.println("Examples:");
        System.out.println("  java -jar stress-test.jar -c 20 -p 100 -r 50");
        System.out.println("  java -jar stress-test.jar --concurrency 50 --rounds 5 --interval 2000");
        System.out.println("  java -jar stress-test.jar --no-init --detailed");
    }
    
    /**
     * 打印当前配置
     */
    public void printConfig() {
        logger.info("=== Stress Test Configuration ===");
        logger.info("Concurrency: {} threads", concurrency);
        logger.info("Port codes: {}", portCodeCount);
        logger.info("Records per port code: {}", recordsPerPortCode);
        logger.info("Batch size: {}", batchSize);
        logger.info("Plan code: {}", planCode);
        logger.info("Total records: {}", (long) portCodeCount * recordsPerPortCode);
        logger.info("Test rounds: {}", testRounds);
        logger.info("Round interval: {} ms", roundIntervalMs);
        logger.info("Initialize data: {}", initData);
        logger.info("Print detailed results: {}", printDetailedResults);
    }
    
    // Getters and Setters
    public int getConcurrency() {
        return concurrency;
    }
    
    public void setConcurrency(int concurrency) {
        this.concurrency = concurrency;
    }
    
    public int getPortCodeCount() {
        return portCodeCount;
    }
    
    public void setPortCodeCount(int portCodeCount) {
        this.portCodeCount = portCodeCount;
    }
    
    public int getRecordsPerPortCode() {
        return recordsPerPortCode;
    }
    
    public void setRecordsPerPortCode(int recordsPerPortCode) {
        this.recordsPerPortCode = recordsPerPortCode;
    }
    
    public int getTestRounds() {
        return testRounds;
    }
    
    public void setTestRounds(int testRounds) {
        this.testRounds = testRounds;
    }
    
    public long getRoundIntervalMs() {
        return roundIntervalMs;
    }
    
    public void setRoundIntervalMs(long roundIntervalMs) {
        this.roundIntervalMs = roundIntervalMs;
    }
    
    public boolean isInitData() {
        return initData;
    }
    
    public void setInitData(boolean initData) {
        this.initData = initData;
    }
    
    public boolean isPrintDetailedResults() {
        return printDetailedResults;
    }
    
    public void setPrintDetailedResults(boolean printDetailedResults) {
        this.printDetailedResults = printDetailedResults;
    }
    
    public int getBatchSize() {
        return batchSize;
    }

    public void setBatchSize(int batchSize) {
        this.batchSize = batchSize;
    }

    public String getPlanCode() {
        return planCode;
    }

    public void setPlanCode(String planCode) {
        this.planCode = planCode;
    }

    public long getTotalRecords() {
        return (long) portCodeCount * recordsPerPortCode;
    }
}
