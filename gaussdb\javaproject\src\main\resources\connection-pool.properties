# 数据库连接池配置文件
# Database Connection Pool Configuration

# ============================================================================
# 数据库连接配置 Database Connection Configuration
# ============================================================================

# 默认数据库连接配置
# Default Database Connection Settings
database.default.url=*******************************************************************************************************************************************************************************************************************************************************************
database.default.username=wbgz
database.default.password=Wbgz@1234
database.default.type=GAUSSDB

# 源数据库连接配置（用于数据迁移）
# Source Database Connection Settings (for data migration)
database.source.url=*******************************************************************************************************************************************************************************************************************************************************************
database.source.username=wbgz
database.source.password=Wbgz@1234
database.source.type=GAUSSDB

# GaussDB数据库连接配置
# GaussDB Database Connection Settings
database.starrocks.url=**********************************************************************************************
database.starrocks.username=jxcs
database.starrocks.password=Jxcs!23ukd
database.starrocks.type=STARROCKS

# MySQL数据库连接配置
# MySQL Database Connection Settings
database.mysql.url=************************************
database.mysql.username=mysqluser
database.mysql.password=mysqlpass
database.mysql.type=MYSQL

# PostgreSQL数据库连接配置
# PostgreSQL Database Connection Settings
database.postgresql.url=*****************************************
database.postgresql.username=pguser
database.postgresql.password=pgpass
database.postgresql.type=POSTGRESQL

# Oracle数据库连接配置
# Oracle Database Connection Settings
database.oracle.url=*****************************************
database.oracle.username=oracleuser
database.oracle.password=oraclepass
database.oracle.type=ORACLE

# ============================================================================
# 元数据视图配置 Metadata View Configuration
# ============================================================================

# 默认元数据视图配置
# Default Metadata View Settings
metadata.default.view=information_schema.columns
metadata.default.schema=public

# GaussDB元数据视图配置
# GaussDB Metadata View Configuration
metadata.gaussdb.view=information_schema.columns
metadata.gaussdb.schema=
metadata.gaussdb.table_name_column=table_name
metadata.gaussdb.column_name_column=column_name
metadata.gaussdb.data_type_column=data_type
metadata.gaussdb.ordinal_position_column=ordinal_position

# StarRocks元数据视图配置
# StarRocks Metadata View Configuration
metadata.starrocks.view=information_schema.columns
metadata.starrocks.schema=
metadata.starrocks.table_name_column=table_name
metadata.starrocks.column_name_column=column_name
metadata.starrocks.data_type_column=data_type
metadata.starrocks.ordinal_position_column=ordinal_position

# MySQL元数据视图配置
# MySQL Metadata View Configuration
metadata.mysql.view=information_schema.columns
metadata.mysql.schema=information_schema
metadata.mysql.table_name_column=table_name
metadata.mysql.column_name_column=column_name
metadata.mysql.data_type_column=data_type
metadata.mysql.ordinal_position_column=ordinal_position

# PostgreSQL元数据视图配置
# PostgreSQL Metadata View Configuration
metadata.postgresql.view=information_schema.columns
metadata.postgresql.schema=information_schema
metadata.postgresql.table_name_column=table_name
metadata.postgresql.column_name_column=column_name
metadata.postgresql.data_type_column=data_type
metadata.postgresql.ordinal_position_column=ordinal_position

# Oracle元数据视图配置
# Oracle Metadata View Configuration
metadata.oracle.view=user_tab_columns
metadata.oracle.schema=
metadata.oracle.table_name_column=table_name
metadata.oracle.column_name_column=column_name
metadata.oracle.data_type_column=data_type
metadata.oracle.ordinal_position_column=column_id

# ============================================================================
# 连接池配置 Connection Pool Configuration
# ============================================================================

# 是否启用连接池 (true/false)
connection.pool.enabled=true

# 默认连接池配置
# Default Connection Pool Settings
connection.pool.default.maximumPoolSize=1000
connection.pool.default.minimumIdle=600
connection.pool.default.connectionTimeout=30000
connection.pool.default.idleTimeout=600000
connection.pool.default.maxLifetime=1800000
connection.pool.default.autoCommit=false
connection.pool.default.leakDetectionThreshold=1800000

# GaussDB连接池配置
# GaussDB Connection Pool Settings
connection.pool.gaussdb.maximumPoolSize=1000
connection.pool.gaussdb.minimumIdle=600
connection.pool.gaussdb.connectionTimeout=30000
connection.pool.gaussdb.idleTimeout=600000
connection.pool.gaussdb.maxLifetime=1800000
connection.pool.gaussdb.autoCommit=false
connection.pool.gaussdb.leakDetectionThreshold=1800000

# StarRocks连接池配置
# StarRocks Connection Pool Settings
connection.pool.starrocks.maximumPoolSize=100
connection.pool.starrocks.minimumIdle=5
connection.pool.starrocks.connectionTimeout=45000
connection.pool.starrocks.idleTimeout=300000
connection.pool.starrocks.maxLifetime=1200000
connection.pool.starrocks.autoCommit=false
connection.pool.starrocks.leakDetectionThreshold=60000

# MySQL连接池配置
# MySQL Connection Pool Settings
connection.pool.mysql.maximumPoolSize=20
connection.pool.mysql.minimumIdle=5
connection.pool.mysql.connectionTimeout=30000
connection.pool.mysql.idleTimeout=600000
connection.pool.mysql.maxLifetime=1800000
connection.pool.mysql.autoCommit=false
connection.pool.mysql.leakDetectionThreshold=60000

# PostgreSQL连接池配置
# PostgreSQL Connection Pool Settings
connection.pool.postgresql.maximumPoolSize=20
connection.pool.postgresql.minimumIdle=5
connection.pool.postgresql.connectionTimeout=30000
connection.pool.postgresql.idleTimeout=600000
connection.pool.postgresql.maxLifetime=1800000
connection.pool.postgresql.autoCommit=false
connection.pool.postgresql.leakDetectionThreshold=60000

# Oracle连接池配置
# Oracle Connection Pool Settings
connection.pool.oracle.maximumPoolSize=15
connection.pool.oracle.minimumIdle=3
connection.pool.oracle.connectionTimeout=45000
connection.pool.oracle.idleTimeout=900000
connection.pool.oracle.maxLifetime=2700000
connection.pool.oracle.autoCommit=false
connection.pool.oracle.leakDetectionThreshold=60000

# ============================================================================
# 压力测试表名配置 Stress Test Table Configuration
# ============================================================================

# 测试数据表名（只读，用于提供测试数据）
# Test Data Table Name (Read-only, provides test data)
stress.test.table.testData=t_d_ai_act_val_test

# 临时表名（分批次处理的中间存储）
# Temporary Table Name (Intermediate storage for batch processing)
stress.test.table.temp=r_d_ai_act_val_test

# 执行操作表名（用于实际的增删改操作）
# Execution Table Name (For actual CRUD operations)
stress.test.table.execution=t_d_ai_act_val_exec

# 方案表名（用于获取c_portfolio组）
# Plan Table Name (For actual CRUD operations)
stress.test.table.plan=t_p_ab_group_rela

# 连接池监控配置
# Connection Pool Monitoring Settings
connection.pool.monitoring.enabled=true
connection.pool.monitoring.interval=60000

# 数据源特定配置
# DataSource Specific Settings
connection.pool.cachePrepStmts=true
connection.pool.prepStmtCacheSize=250
connection.pool.prepStmtCacheSqlLimit=2048
connection.pool.useServerPrepStmts=true
connection.pool.useLocalSessionState=true
connection.pool.rewriteBatchedStatements=true
connection.pool.cacheResultSetMetadata=true
connection.pool.cacheServerConfiguration=true
connection.pool.maintainTimeStats=false
