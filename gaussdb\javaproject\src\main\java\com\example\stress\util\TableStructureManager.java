package com.example.stress.util;

import com.example.config.DatabaseConnectionConfig;
import com.example.stress.config.TableNameConfig;
import com.example.stress.model.TableStructure;
import com.example.stress.model.TableStructure.ColumnInfo;
import com.example.util.ConnectionPoolManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 表结构管理器
 * 通过数据库元数据视图动态获取表结构信息
 */
public class TableStructureManager {
    private static final Logger logger = LoggerFactory.getLogger(TableStructureManager.class);
    
    // 缓存表结构信息
    private static final ConcurrentHashMap<String, TableStructure> tableStructureCache = new ConcurrentHashMap<>();
    
    /**
     * 获取测试数据表结构
     */
    public static TableStructure getTestDataTableStructure(Connection connection) throws SQLException {
        String tableName = TableNameConfig.getTestDataTable();
        return getTableStructure(connection, tableName);
    }
    
    /**
     * 获取临时表结构
     */
    public static TableStructure getTempTableStructure(Connection connection) throws SQLException {
        String tableName = TableNameConfig.getTempTable();
        return getTableStructure(connection, tableName);
    }
    
    /**
     * 获取执行表结构
     */
    public static TableStructure getExecutionTableStructure(Connection connection) throws SQLException {
        String tableName = TableNameConfig.getExecutionTable();
        return getTableStructure(connection, tableName);
    }
    
    /**
     * 获取指定表的结构信息（带缓存）
     */
    public static TableStructure getTableStructure(Connection connection, String tableName) throws SQLException {
        // 先从缓存中查找
        TableStructure cached = tableStructureCache.get(tableName.toUpperCase());
        if (cached != null) {
            logger.debug("Retrieved table structure for {} from cache", tableName);
            return cached;
        }
        
        // 从数据库获取
        TableStructure structure = loadTableStructureFromDatabase(connection, tableName);
        
        // 缓存结果
        tableStructureCache.put(tableName.toUpperCase(), structure);
        logger.info("Loaded and cached table structure for {}: {} columns", tableName, structure.getColumnCount());
        
        return structure;
    }
    
    /**
     * 从数据库元数据视图加载表结构
     */
    private static TableStructure loadTableStructureFromDatabase(Connection connection, String tableName) throws SQLException {
        // 获取数据库类型
        ConnectionPoolManager.DatabaseType databaseType = getDatabaseType(connection);

        // 获取元数据配置
        DatabaseConnectionConfig.MetadataConfig metaConfig =
            DatabaseConnectionConfig.getMetadataConfig(databaseType);
        
        if (metaConfig == null) {
            throw new SQLException("No metadata configuration found for database type: " + databaseType);
        }
        
        // 构建查询SQL
        String sql = String.format("SELECT %s, %s, %s FROM %s WHERE UPPER(%s) = ? ORDER BY %s",
                                  metaConfig.getColumnNameColumn(),
                                  metaConfig.getDataTypeColumn(),
                                  metaConfig.getOrdinalPositionColumn(),
                                  metaConfig.getFullViewName(),
                                  metaConfig.getTableNameColumn(),
                                  metaConfig.getOrdinalPositionColumn());
        
        List<ColumnInfo> columns = new ArrayList<>();
        
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, tableName.toUpperCase());
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    String columnName = rs.getString(metaConfig.getColumnNameColumn());
                    String dataType = rs.getString(metaConfig.getDataTypeColumn());
                    int ordinalPosition = rs.getInt(metaConfig.getOrdinalPositionColumn());
                    
                    columns.add(new ColumnInfo(columnName, dataType, ordinalPosition));
                }
            }
        }
        
        if (columns.isEmpty()) {
            throw new SQLException("No columns found for table: " + tableName + 
                                 ". Please check if the table exists and the metadata view is accessible.");
        }
        
        logger.debug("Retrieved {} columns for table {} from metadata view {}", 
                    columns.size(), tableName, metaConfig.getFullViewName());
        
        return new TableStructure(tableName, columns);
    }
    
    /**
     * 获取数据库类型
     */
    private static ConnectionPoolManager.DatabaseType getDatabaseType(Connection connection) throws SQLException {
        String databaseProductName = connection.getMetaData().getDatabaseProductName().toUpperCase();

        if (databaseProductName.contains("GAUSSDB") || databaseProductName.contains("OPENGAUSS")) {
            return ConnectionPoolManager.DatabaseType.GAUSSDB;
        } else if (databaseProductName.contains("ORACLE")) {
            return ConnectionPoolManager.DatabaseType.ORACLE;
        } else if (databaseProductName.contains("MYSQL")) {
            return ConnectionPoolManager.DatabaseType.MYSQL;
        } else if (databaseProductName.contains("POSTGRESQL")) {
            return ConnectionPoolManager.DatabaseType.POSTGRESQL;
        } else {
            logger.warn("Unknown database type: {}, defaulting to GAUSSDB", databaseProductName);
            return ConnectionPoolManager.DatabaseType.GAUSSDB;
        }
    }
    
    /**
     * 验证所有压力测试相关表的结构是否一致
     */
    public static boolean validateTableStructuresConsistent(Connection connection) throws SQLException {
        logger.info("Validating table structures consistency...");
        
        TableStructure testDataStructure = getTestDataTableStructure(connection);
        TableStructure tempStructure = getTempTableStructure(connection);
        TableStructure executionStructure = getExecutionTableStructure(connection);
        
        // 检查列数量是否一致
        if (testDataStructure.getColumnCount() != tempStructure.getColumnCount() ||
            testDataStructure.getColumnCount() != executionStructure.getColumnCount()) {
            logger.error("Table column counts are inconsistent: TestData={}, Temp={}, Execution={}", 
                        testDataStructure.getColumnCount(), 
                        tempStructure.getColumnCount(), 
                        executionStructure.getColumnCount());
            return false;
        }
        
        // 检查列名和类型是否一致
        List<ColumnInfo> testColumns = testDataStructure.getColumns();
        List<ColumnInfo> tempColumns = tempStructure.getColumns();
        List<ColumnInfo> executionColumns = executionStructure.getColumns();
        
        for (int i = 0; i < testColumns.size(); i++) {
            ColumnInfo testCol = testColumns.get(i);
            ColumnInfo tempCol = tempColumns.get(i);
            ColumnInfo execCol = executionColumns.get(i);
            
            if (!testCol.getColumnName().equalsIgnoreCase(tempCol.getColumnName()) ||
                !testCol.getColumnName().equalsIgnoreCase(execCol.getColumnName())) {
                logger.error("Column names are inconsistent at position {}: TestData={}, Temp={}, Execution={}", 
                            i + 1, testCol.getColumnName(), tempCol.getColumnName(), execCol.getColumnName());
                return false;
            }
            
            if (!testCol.getDataType().equalsIgnoreCase(tempCol.getDataType()) ||
                !testCol.getDataType().equalsIgnoreCase(execCol.getDataType())) {
                logger.warn("Column data types are different at position {} ({}): TestData={}, Temp={}, Execution={}", 
                           i + 1, testCol.getColumnName(), testCol.getDataType(), tempCol.getDataType(), execCol.getDataType());
                // 数据类型不一致只是警告，不阻止程序运行
            }
        }
        
        logger.info("Table structures validation passed: {} columns in each table", testDataStructure.getColumnCount());
        return true;
    }
    
    /**
     * 清空缓存
     */
    public static void clearCache() {
        tableStructureCache.clear();
        logger.debug("Table structure cache cleared");
    }
    
    /**
     * 获取缓存统计信息
     */
    public static String getCacheStats() {
        return String.format("Table structure cache: %d entries", tableStructureCache.size());
    }
    
    /**
     * 检查表是否包含指定的分组字段
     */
    public static boolean hasGroupingColumn(Connection connection, String tableName, String columnName) throws SQLException {
        TableStructure structure = getTableStructure(connection, tableName);
        return structure.hasColumn(columnName);
    }
    
    /**
     * 打印表结构信息
     */
    public static void printTableStructure(TableStructure structure) {
        logger.info("=== Table Structure: {} ===", structure.getTableName());
        logger.info("Total columns: {}", structure.getColumnCount());
        
        for (ColumnInfo column : structure.getColumns()) {
            logger.info("  {}: {} (order: {})", 
                       column.getColumnName(), 
                       column.getDataType(), 
                       column.getColumnOrder());
        }
    }
}
