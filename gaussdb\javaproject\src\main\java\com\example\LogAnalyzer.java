package com.example;

import java.io.*;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class LogAnalyzer {
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
    private static final Pattern LOG_PATTERN = Pattern.compile(
        "(\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}\\.\\d{3}).*?traceId[:\\s]+([a-fA-F0-9]{32})"
    );
    
    // 存储每个请求的信息
    static class RequestInfo {
        String traceId;
        Date startTime;
        Date endTime;
        List<StepInfo> steps = new ArrayList<>();
        
        long getTotalDuration() {
            return endTime != null && startTime != null ? 
                   endTime.getTime() - startTime.getTime() : 0;
        }
    }
    
    // 存储每个步骤的信息
    static class StepInfo {
        Date startTime;
        Date endTime;
        String stepType;
        
        long getDuration() {
            return endTime != null && startTime != null ? 
                   endTime.getTime() - startTime.getTime() : 0;
        }
    }
    
    public static void main(String[] args) {
        if (args.length < 1) {
            System.err.println("Usage: java LogAnalyzer <logFilePath>");
            System.err.println("Example: java LogAnalyzer logs/gsjdbc.log");
            System.exit(1);
        }
        
        String logFilePath = args[0];
        LogAnalyzer analyzer = new LogAnalyzer();
        analyzer.analyzeLog(logFilePath);
    }
    
    public void analyzeLog(String logFilePath) {
        Map<String, RequestInfo> requests = new HashMap<>();
        Map<String, Long> stepDurations = new HashMap<>();
        
        try (BufferedReader reader = new BufferedReader(new FileReader(logFilePath))) {
            String line;
            while ((line = reader.readLine()) != null) {
                processLogLine(line, requests);
            }
            
            // 计算统计信息
            calculateStatistics(requests, stepDurations);
            
            // 输出结果
            printResults(requests, stepDurations);
            
        } catch (IOException e) {
            System.err.println("Error reading log file: " + e.getMessage());
        }
    }
    
    private void processLogLine(String line, Map<String, RequestInfo> requests) {
        Matcher matcher = LOG_PATTERN.matcher(line);
        if (!matcher.find()) {
            return;
        }
        
        String timeStr = matcher.group(1);
        String traceId = matcher.group(2);
        
        try {
            Date logTime = DATE_FORMAT.parse(timeStr);
            RequestInfo request = requests.computeIfAbsent(traceId, k -> {
                RequestInfo req = new RequestInfo();
                req.traceId = k;
                return req;
            });
            
            // 判断是否是步骤开始（只有traceId的日志）
            if (isStepStart(line)) {
                // 结束上一个步骤
                if (!request.steps.isEmpty()) {
                    StepInfo lastStep = request.steps.get(request.steps.size() - 1);
                    if (lastStep.endTime == null) {
                        lastStep.endTime = logTime;
                    }
                }
                
                // 开始新步骤
                StepInfo step = new StepInfo();
                step.startTime = logTime;
                step.stepType = "Step-" + (request.steps.size() + 1); // 简单编号
                request.steps.add(step);
                
                // 设置请求开始时间
                if (request.startTime == null) {
                    request.startTime = logTime;
                }
            }
            
            // 更新请求结束时间
            request.endTime = logTime;
            
            // 如果当前有活跃步骤，更新其结束时间
            if (!request.steps.isEmpty()) {
                StepInfo lastStep = request.steps.get(request.steps.size() - 1);
                lastStep.endTime = logTime;
            }
            
        } catch (ParseException e) {
            System.err.println("Error parsing date: " + timeStr);
        }
    }
    
    private boolean isStepStart(String line) {
        // 精确匹配：traceId :32位十六进制UUID.
        return line.matches(".*traceId\\s*:[a-fA-F0-9]{32}\\s*\\.$");
    }
    
    private void calculateStatistics(Map<String, RequestInfo> requests, 
                                   Map<String, Long> stepDurations) {
        for (RequestInfo request : requests.values()) {
            for (StepInfo step : request.steps) {
                if (step.endTime != null) {
                    long duration = step.getDuration();
                    stepDurations.merge(step.stepType, duration, Long::sum);
                }
            }
        }
    }
    
    private void printResults(Map<String, RequestInfo> requests, 
                            Map<String, Long> stepDurations) {
        System.out.println("=== 日志分析结果 ===");
        System.out.println();
        
        // 输出每个请求的详细信息
        System.out.println("=== 请求详细信息 ===");
        long totalRequestTime = 0;
        int validRequests = 0;
        
        for (RequestInfo request : requests.values()) {
            long duration = request.getTotalDuration();
            if (duration > 0) {
                System.out.printf("TraceId: %s, 总耗时: %d ms, 步骤数: %d%n", 
                    request.traceId, duration, request.steps.size());
                
                // 输出步骤详情
                for (int i = 0; i < request.steps.size(); i++) {
                    StepInfo step = request.steps.get(i);
                    long stepDuration = step.getDuration();
                    System.out.printf("  步骤%d [%s]: %d ms%n", 
                        i + 1, step.stepType, stepDuration);
                }
                
                totalRequestTime += duration;
                validRequests++;
                System.out.println();
            }
        }
        
        // 输出总体统计
        System.out.println("=== 总体统计 ===");
        System.out.printf("有效请求数: %d%n", validRequests);
        System.out.printf("所有请求总耗时: %d ms%n", totalRequestTime);
        if (validRequests > 0) {
            String formattedAverageTime = String.format("%.2f", (double) totalRequestTime / validRequests);
            System.out.printf("平均请求耗时: %s ms%n", formattedAverageTime);
        }
        System.out.println();
        
        // 输出按步骤类型统计
        System.out.println("=== 按步骤类型统计 ===");
        long totalStepTime = 0;
        for (Map.Entry<String, Long> entry : stepDurations.entrySet()) {
            System.out.printf("%s: %d ms%n", entry.getKey(), entry.getValue());
            totalStepTime += entry.getValue();
        }
        System.out.printf("所有步骤总耗时: %d ms%n", totalStepTime);
        
        // 输出步骤耗时占比
        System.out.println();
        System.out.println("=== 步骤耗时占比 ===");
        for (Map.Entry<String, Long> entry : stepDurations.entrySet()) {
            double percentage = totalStepTime > 0 ? 
                (double) entry.getValue() / totalStepTime * 100 : 0;
            String formattedPercentage = String.format("%.2f", percentage);
            System.out.printf("%s: %s%%(%d ms)%n", 
                entry.getKey(), formattedPercentage, entry.getValue());
        }
    }
}






