import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.example.DataMigrationExample;

public class GaussDBSqlFormatter {
    private static final Logger logger = LoggerFactory.getLogger(GaussDBSqlFormatter.class);
    public static void main(String[] args) {
        String orgSql = " SELECT 0 AS ID, A.*,B.C_IDEN AS C_PERHUNDERD_ID,B.N_INCOME_PT,B.N_INCOME_AT,B.N_INCOME_PT_DUE,B.N_INCOME_AT_DUE,B.N_INCOME_DAYS FROM ( SELECT C_SEC_CODE,C_SEC_VAR_CODE,C_MKT_CODE,C_DC_CODE,C_DV_INVEST_CLS,C_DV_ISSUE_MODE,C_DTA_CODE ,SUM(N_AMOUNT) AS N_AMOUNT FROM   ( SELECT A.N_AMOUNT * B.N_FUND_WAY AS N_AMOUNT,A.N_ORIG_MONEY * B.N_FUND_WAY AS N_ORIG_MONEY,A.N_PORT_MONEY * B.N_FUND_WAY AS N_PORT_MONEY,A.C_SEC_CODE,A.C_SEC_VAR_CODE,A.C_MKT_CODE,A.C_DC_CODE,A.C_DV_INVEST_CLS,A.C_DV_ISSUE_MODE,A.C_DTA_CODE  FROM T_D_AI_STOCK A,T_S_DAI_ITEM B  WHERE a.c_year_month = $1 and A.C_DAI_CODE = B.C_DAI_CODE AND A.C_PORT_CODE = $2 AND A.D_STOCK = $3 AND A.C_DAI_CODE <> 'WTZC'  AND A.C_DAI_CODE = 'ZQTZ_CB' AND A.C_DTA_CODE <> 'WY'  and a.c_dta_code <> 'DWY'  UNION ALL SELECT A.N_AMOUNT * A.N_WAY * B.N_FUND_WAY AS N_AMOUNT,A.N_ORIG_MONEY * A.N_WAY * B.N_FUND_WAY AS N_ORIG_MONEY,A.N_PORT_MONEY * A.N_WAY * B.N_FUND_WAY AS N_PORT_MONEY,A.C_SEC_CODE,A.C_SEC_VAR_CODE,A.C_MKT_CODE,A.C_DC_CODE,A.C_DV_INVEST_CLS,A.C_DV_ISSUE_MODE,A.C_DTA_CODE  FROM T_D_AI_ACT_VAL A,T_S_DAI_ITEM B  WHERE A.C_DAI_CODE = B.C_DAI_CODE AND A.C_PORT_CODE = $4 AND (A.D_CHK_ACC > $5 AND A.D_CHK_ACC <= $6) AND A.N_CHECK_STATE = 1 AND A.C_DAI_CODE <> 'WTZC'  AND A.C_DAI_CODE = 'ZQTZ_CB' AND A.C_DTA_CODE <> 'WY'  and a.c_dta_code <> 'DWY'  UNION ALL SELECT A.N_AMOUNT * A.N_WAY * B.N_FUND_WAY AS N_AMOUNT,A.N_ORIG_MONEY * A.N_WAY * B.N_FUND_WAY AS N_ORIG_MONEY,A.N_PORT_MONEY * A.N_WAY * B.N_FUND_WAY AS N_PORT_MONEY,A.C_SEC_CODE,A.C_SEC_VAR_CODE,A.C_MKT_CODE,A.C_DC_CODE,A.C_DV_INVEST_CLS,A.C_DV_ISSUE_MODE,A.C_DTA_CODE  FROM R_D_AI_ACT_VAL A,T_S_DAI_ITEM B  WHERE A.C_DAI_CODE = B.C_DAI_CODE AND A.C_PORT_CODE = $7 AND A.D_CHK_ACC > $8 AND A.D_CHK_ACC <= $9  AND A.C_DAI_CODE <> 'WTZC'  AND A.N_USEVAT <> 1  AND A.C_DAI_CODE = 'ZQTZ_CB' AND A.C_DTA_CODE <> 'WY'  and a.c_dta_code <> 'DWY'  UNION ALL SELECT 0 N_AMOUNT,0 N_ORIG_MONEY,0 N_PORT_MONEY, case $10 when 1 then A.C_SEC_CODE when 0 then ' ' end C_SEC_CODE, case $11 when 1 then SEC.C_SEC_VAR_CODE when 0 then ' ' end C_SEC_VAR_CODE, case $12 when 1 then SEC.C_MKT_CODE when 0 then ' ' end C_MKT_CODE, C_DC_CODE, case $13 when 1 then A.C_DV_INVEST_CLS when 0 then ' ' end C_DV_INVEST_CLS, case $14 when 1 then A.C_DV_ISSUE_MODE when 0 then ' ' end C_DV_ISSUE_MODE, case $15 when 1 then A.C_DTA_CODE when 0 then ' ' end C_DTA_CODE FROM T_D_AC_TRADE_IVT A  LEFT JOIN (SELECT C_SEC_CODE, C_SEC_VAR_CODE, C_MKT_CODE FROM T_P_SV_SEC_BASE WHERE N_CHECK_STATE = 1) SEC     ON A.C_SEC_CODE = SEC.C_SEC_CODE WHERE A.N_CHECK_STATE = 1 AND A.C_PORT_CODE = $16 AND A.D_TRADE <= $17 AND D_SETT_FACT >= $18 AND A.C_TD_TYPE = 'ZQJY' and a.c_dta_code <> 'MK' AND a.c_dta_code NOT IN ('WY','DWY')   ) A GROUP BY C_SEC_CODE,C_SEC_VAR_CODE,C_MKT_CODE,C_DC_CODE,C_DV_INVEST_CLS,C_DV_ISSUE_MODE,C_DTA_CODE  ) A JOIN ( SELECT C_SEC_VAR_CODE,C_DA_CODE FROM T_S_DA_SEC_VAR WHERE C_DA_CODE LIKE 'ZQ%') A1 ON A.C_SEC_VAR_CODE = A1.C_SEC_VAR_CODE LEFT JOIN (SELECT C_IDEN,C_SEC_CODE,N_INCOME_PT,N_INCOME_AT,N_INCOME_PT_DUE,N_INCOME_AT_DUE,N_INCOME_DAYS FROM T_D_SV_FI_INCOME WHERE N_CHECK_STATE = 1 AND D_INCOME = $19)B ON A.C_SEC_CODE = B.C_SEC_CODE WHERE  A.C_DV_INVEST_CLS = $20  ; parameters: $1 = '202401', $2 = '007490D19949', $3 = '2024-01-30 00:00:00', $4 = '007490D19949', $5 = '2024-01-30 00:00:00', $6 = '2024-01-31 00:00:00', $7 = '007490D19949', $8 = '2024-01-30 00:00:00', $9 = '2024-01-31 00:00:00', $10 = '1', $11 = '1', $12 = '1', $13 = '1', $14 = '1', $15 = '1', $16 = '007490D19949', $17 = '2024-01-31 00:00:00', $18 = '2024-01-31 00:00:00', $19 = '2024-01-31 00:00:00', $20 = 'IC_TD'";
        
        String querySqlWithParameters = orgSql.replace("\n", "");
        String[] parts = querySqlWithParameters.split("parameters:");
        
        if (parts.length != 2) {
            System.err.println("Invalid format: SQL and parameters should be separated by 'parameters:'");
            return;
        }
        
        String sql = parts[0];
        String para = parts[1];
        
        formatSql(sql, para);
    }
    
    public static void formatSql(String sqlStr, String parameterStr) {
        sqlStr = sqlStr.replace("\t", "    ");
        
        String[] listDic = parameterStr.split(",");
        Map<String, String> dic1 = new HashMap<>();
        
        for (String pa : listDic) {
            String[] keyValue = pa.split("=", 2);
            if (keyValue.length == 2) {
                dic1.put(keyValue[0].trim(), keyValue[1].trim());
            }
        }
        
        for (String key1 : dic1.keySet()) {
            sqlStr = sqlStr.replaceFirst("\\" + key1, dic1.get(key1));
        }
        logger.info("explain analyse " + sqlStr);
        System.out.println("explain analyse " + sqlStr);
    }
}