@echo off
REM GaussDB数据迁移工具启动脚本
REM GaussDB Data Migration Tool Startup Script

setlocal enabledelayedexpansion

REM 设置Java选项
set JAVA_OPTS=-Xms512m -Xmx2g -XX:+UseG1GC

REM JAR文件路径
set JAR_FILE=target\gaussdb-data-migration-1.0.0-fat.jar

REM 检查JAR文件是否存在
if not exist "%JAR_FILE%" (
    echo Error: JAR file not found: %JAR_FILE%
    echo Please run 'mvn clean package' first to build the project.
    exit /b 1
)

REM 显示帮助信息
if "%1"=="help" goto show_help
if "%1"=="--help" goto show_help
if "%1"=="-h" goto show_help

REM 显示配置信息
if "%1"=="config" goto show_config
if "%1"=="--config" goto show_config
if "%1"=="-c" goto show_config

REM 显示使用示例
if "%1"=="examples" goto show_examples

REM 运行标准迁移
if "%1"=="migrate" goto run_migrate

REM 运行日期分批迁移
if "%1"=="date-migrate" goto run_date_migrate

REM 如果没有参数，显示帮助
if "%1"=="" goto show_brief_help

REM 直接传递所有参数给Java程序
java %JAVA_OPTS% -jar "%JAR_FILE%" %*
goto end

:show_brief_help
echo GaussDB Data Migration Tool Startup Script
echo ===========================================
echo.
echo Usage: %0 [COMMAND] [OPTIONS]
echo.
echo Commands:
echo   help                    Show help information
echo   config                  Show current configuration
echo   migrate                 Run standard migration
echo   date-migrate            Run date-based migration
echo   examples                Show usage examples
echo.
echo For detailed options, run: %0 help
echo.
goto end

:show_help
java %JAVA_OPTS% -jar "%JAR_FILE%" --help
goto end

:show_config
echo Showing current configuration...
java %JAVA_OPTS% -jar "%JAR_FILE%" --config
goto end

:show_examples
echo Usage Examples
echo ==============
echo.
echo 1. Basic Migration:
echo    %0 migrate --source-table source_table --target-table target_table --plan-code plan001
echo.
echo 2. Migration with Custom Parameters:
echo    %0 migrate -st source_table -tt target_table -p plan001 -bs 5000 -tp 8
echo.
echo 3. Migration from StarRocks:
echo    %0 migrate -st analytics_table -tt target_table -p plan001 -sdb starrocks
echo.
echo 4. Date-based Migration:
echo    %0 date-migrate -st log_table -tt target_table -p plan001 ^
echo       -dc log_timestamp -sd 2024-01-01 -ed 2024-01-31 -bd 7
echo.
echo 5. Date-based Migration from StarRocks:
echo    %0 date-migrate -st analytics_fact -tt target_table -p plan001 ^
echo       -sdb starrocks -dc dt -sd 2024-01-01 -ed 2024-01-31 -bd 1
echo.
echo 6. Legacy Positional Arguments (still supported):
echo    java %JAVA_OPTS% -jar "%JAR_FILE%" source_table target_table plan001 2000 4
echo.
goto end

:run_migrate
echo Running standard migration...
shift
java %JAVA_OPTS% -jar "%JAR_FILE%" --mode migrate %*
goto end

:run_date_migrate
echo Running date-based migration...
shift
java %JAVA_OPTS% -jar "%JAR_FILE%" --mode date-migrate %*
goto end

:end
endlocal
