import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.example.util.DatabaseUtil;

import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.delete.Delete;
import net.sf.jsqlparser.statement.insert.Insert;
import net.sf.jsqlparser.statement.select.Select;
import net.sf.jsqlparser.statement.update.Update;
import net.sf.jsqlparser.util.TablesNamesFinder;

public class GaussDBStatementAnalyzer {
    private static final String DB_URL = "**********************************************************";
    private static final String DB_USER = "root";
    private static final String DB_PASSWORD = "Tcdn@2007";

    public static void main(String[] args) {
        if (args.length == 0) {
            System.err.println("Usage: java GaussDBStatementAnalyzer <table1> [table2] [table3] ... [--compare-only]");
            System.err.println("Example: java GaussDBStatementAnalyzer summary_statement_20250728 summary_statement_20250729");
            System.err.println("Example (compare only): java GaussDBStatementAnalyzer summary_statement_20250728 summary_statement_20250729 --compare-only");
            System.exit(1);
        }
        
        // 检查是否有--compare-only参数
        boolean compareOnly = false;
        String[] statementTableNames = args;
        
        if (args[args.length - 1].equals("--compare-only")) {
            compareOnly = true;
            statementTableNames = new String[args.length - 1];
            System.arraycopy(args, 0, statementTableNames, 0, args.length - 1);
        }
        
        // 存储每个表的分析结果 - 使用LinkedHashMap保持插入顺序
        Map<String, Map<String, Map<String, StatementStats>>> allTableResults = new LinkedHashMap<>();
        Map<String, Map<String, Map<String, StatementStats>>> allSingleTableResults = new LinkedHashMap<>();
        
        // 处理每个输入表
        for (String statementTableName : statementTableNames) {
            System.out.println("Processing table: " + statementTableName);
            
            Map<String, Map<String, StatementStats>> analysisResults = new HashMap<>();
            Map<String, Map<String, StatementStats>> singleTableResults = new HashMap<>();
            
            String timeSuffix = processTable(statementTableName, analysisResults, singleTableResults);
            
            allTableResults.put(statementTableName, analysisResults);
            allSingleTableResults.put(statementTableName, singleTableResults);
            
            // 只有在非compare-only模式下才输出单个表的结果
            if (!compareOnly) {
                exportResults(analysisResults, statementTableName, timeSuffix);
                exportResults(singleTableResults, statementTableName, "_single_table" + timeSuffix);
            }
        }
        
        // 如果有多个表，生成比较分析
        if (statementTableNames.length > 1) {
            generateComparisonAnalysis(allTableResults, statementTableNames, "_combined_comparison");
            generateComparisonAnalysis(allSingleTableResults, statementTableNames, "_single_table_comparison");
        } else if (compareOnly) {
            System.err.println("Warning: --compare-only requires at least 2 tables for comparison");
        }
    }
    
    private static String processTable(String statementTableName, 
                                   Map<String, Map<String, StatementStats>> analysisResults,
                                   Map<String, Map<String, StatementStats>> singleTableResults) {
        Connection connection = null;
        Statement stmt = null;
        ResultSet rs = null;
        String timeSuffix = "";
        
        try {
            connection = DatabaseUtil.getConnection(DB_URL, DB_USER, DB_PASSWORD);
            stmt = connection.createStatement();

            // 先获取最大的last_updated时间
            String maxTimeSql = "SELECT MAX(last_updated) as max_time FROM " + statementTableName;
            rs = stmt.executeQuery(maxTimeSql);
            if (rs.next()) {
                java.sql.Timestamp maxLastUpdated = rs.getTimestamp("max_time");
                if (maxLastUpdated != null) {
                    java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyyMMdd_HH");
                    timeSuffix = "_" + sdf.format(maxLastUpdated);
                }
            }
            rs.close();

            String sql = "SELECT unique_sql_id, query, n_calls, total_elapse_time, n_returned_rows, n_tuples_fetched, n_tuples_returned, n_tuples_inserted, n_tuples_updated, n_tuples_deleted FROM " + statementTableName +
                         " WHERE query ~* '(INSERT\\s+INTO|SELECT\\s+.+\\s+FROM|UPDATE\\s+|DELETE\\s+FROM|COMMIT|ROLLBACK|BEGIN|START\\s+TRANSACTION)\\s*' " +
                         " ORDER BY unique_sql_id;";
            
            System.out.println("Executing query for " + statementTableName + "...");
            rs = stmt.executeQuery(sql);

            while (rs.next()) {
                String uniqueSqlId = rs.getString("unique_sql_id");
                String query = rs.getString("query");
                double elapsedTime = rs.getDouble("total_elapse_time");
                int nCalls = rs.getInt("n_calls");
                long nReturnedRows = rs.getLong("n_returned_rows");
                long tuplesFetched = rs.getLong("n_tuples_fetched");
                long tuplesReturned = rs.getLong("n_tuples_returned");
                long tuplesInserted = rs.getLong("n_tuples_inserted");
                long tuplesUpdated = rs.getLong("n_tuples_updated");
                long tuplesDeleted = rs.getLong("n_tuples_deleted");

                String operationType = "UNKNOWN";
                String tableName = "UNKNOWN";
                Set<String> allTables = new HashSet<>();

                try {
                    net.sf.jsqlparser.statement.Statement statement = CCJSqlParserUtil.parse(query);
                    TablesNamesFinder tablesNamesFinder = new TablesNamesFinder();
                    
                    if (statement instanceof Select) {
                        operationType = "SELECT";
                        Set<String> tableNames = new HashSet<>();
                        for (String table : tablesNamesFinder.getTableList(statement)) {
                            String cleanTable = table.toLowerCase();
                            if (cleanTable.contains(".")) {
                                cleanTable = cleanTable.substring(cleanTable.lastIndexOf(".") + 1);
                            }
                            cleanTable = cleanTable.replaceAll("[`\"\\[\\]]", "");
                            tableNames.add(cleanTable);
                        }
                        
                        System.out.printf("SQL ID: %s, 解析出的表: %s%n", uniqueSqlId, tableNames);
                        
                        allTables.addAll(tableNames);
                        tableName = tableNames.isEmpty() ? query.toLowerCase() : tableNames.stream().sorted().collect(Collectors.joining(","));
                        
                    } else if (statement instanceof Insert) {
                        operationType = "INSERT";
                        Insert insert = (Insert) statement;
                        String singleTable = insert.getTable().getName().toLowerCase();
                        tableName = singleTable;
                        allTables.add(singleTable);
                        
                    } else if (statement instanceof Update) {
                        operationType = "UPDATE";
                        Update update = (Update) statement;
                        String singleTable = update.getTable().getName().toLowerCase();
                        tableName = singleTable;
                        allTables.add(singleTable);
                        
                    } else if (statement instanceof Delete) {
                        operationType = "DELETE";
                        Delete delete = (Delete) statement;
                        String singleTable = delete.getTable().getName().toLowerCase();
                        tableName = singleTable;
                        allTables.add(singleTable);
                    }
                    
                } catch (JSQLParserException e) {
                    System.err.println("JSqlParser failed, using regex fallback for: " + query.substring(0, Math.min(100, query.length())) + "...");
                    
                    String lowerQuery = query.toLowerCase();
                    
                    if (lowerQuery.contains("select")) {
                        operationType = "SELECT";
                        java.util.regex.Pattern fromPattern = java.util.regex.Pattern.compile("from\\s+([\\w\\.]+)", java.util.regex.Pattern.CASE_INSENSITIVE);
                        java.util.regex.Matcher matcher = fromPattern.matcher(query);
                        Set<String> tableNames = new HashSet<>();
                        while (matcher.find()) {
                            String table = matcher.group(1).toLowerCase();
                            if (table.contains(".")) {
                                table = table.substring(table.lastIndexOf(".") + 1);
                            }
                            tableNames.add(table);
                        }
                        
                        System.out.printf("SQL ID: %s, 正则解析出的表: %s%n", uniqueSqlId, tableNames);
                        
                        allTables.addAll(tableNames);
                        tableName = tableNames.isEmpty() ? query.toLowerCase() : tableNames.stream().sorted().collect(Collectors.joining(","));
                        
                    } else if (lowerQuery.contains("insert")) {
                        operationType = "INSERT";
                        java.util.regex.Pattern insertPattern = java.util.regex.Pattern.compile("insert\\s+into\\s+([\\w\\.]+)", java.util.regex.Pattern.CASE_INSENSITIVE);
                        java.util.regex.Matcher matcher = insertPattern.matcher(query);
                        if (matcher.find()) {
                            String table = matcher.group(1).toLowerCase();
                            if (table.contains(".")) {
                                table = table.substring(table.lastIndexOf(".") + 1);
                            }
                            tableName = table;
                            allTables.add(table);
                        }
                        
                    } else if (lowerQuery.contains("update")) {
                        operationType = "UPDATE";
                        java.util.regex.Pattern updatePattern = java.util.regex.Pattern.compile("update\\s+([\\w\\.]+)", java.util.regex.Pattern.CASE_INSENSITIVE);
                        java.util.regex.Matcher matcher = updatePattern.matcher(query);
                        if (matcher.find()) {
                            String table = matcher.group(1).toLowerCase();
                            if (table.contains(".")) {
                                table = table.substring(table.lastIndexOf(".") + 1);
                            }
                            tableName = table;
                            allTables.add(table);
                        }
                        
                    } else if (lowerQuery.contains("delete")) {
                        operationType = "DELETE";
                        java.util.regex.Pattern deletePattern = java.util.regex.Pattern.compile("delete\\s+from\\s+([\\w\\.]+)", java.util.regex.Pattern.CASE_INSENSITIVE);
                        java.util.regex.Matcher matcher = deletePattern.matcher(query);
                        if (matcher.find()) {
                            String table = matcher.group(1).toLowerCase();
                            if (table.contains(".")) {
                                table = table.substring(table.lastIndexOf(".") + 1);
                            }
                            tableName = table;
                            allTables.add(table);
                        }
                    } else {
                        // 兜底处理：其他类型SQL（如COMMIT、ROLLBACK、BEGIN等）
                        operationType = "OTHER";
                        tableName = query.toLowerCase();
                        System.out.printf("SQL ID: %s, 兜底处理，使用完整query作为分组: %s%n", uniqueSqlId, query.substring(0, Math.min(50, query.length())) + "...");
                    }
                }

                if (!"UNKNOWN".equals(operationType) && !"UNKNOWN".equals(tableName)) {
                    analysisResults
                            .computeIfAbsent(tableName, k -> new HashMap<>())
                            .computeIfAbsent(operationType, k -> new StatementStats(statementTableName))
                            .addExecution(elapsedTime, uniqueSqlId, nCalls, nReturnedRows, tuplesFetched, tuplesReturned, tuplesInserted, tuplesUpdated, tuplesDeleted);
                    
                    for (String singleTable : allTables) {
                        singleTableResults
                                .computeIfAbsent(singleTable, k -> new HashMap<>())
                                .computeIfAbsent(operationType, k -> new StatementStats(statementTableName))
                                .addExecution(elapsedTime, uniqueSqlId, nCalls, nReturnedRows, tuplesFetched, tuplesReturned, tuplesInserted, tuplesUpdated, tuplesDeleted);
                    }
                }
            }
            
        } catch (SQLException e) {
            System.err.println("Error processing table " + statementTableName + ": " + e.getMessage());
            e.printStackTrace();
        } finally {
            try {
                if (rs != null) rs.close();
                if (stmt != null) stmt.close();
                if (connection != null) connection.close();
            } catch (SQLException e) {
                System.err.println("Error closing database resources: " + e.getMessage());
            }
        }
        
        System.out.println("Analysis completed for " + statementTableName);
        
        return timeSuffix;
    }
    
    private static void generateComparisonAnalysis(Map<String, Map<String, Map<String, StatementStats>>> allResults, 
                                                 String[] statementTableNames, String suffix) {
        // 找到总执行时间最少的表作为基准
        String baselineTable = null;
        double minTotalTime = Double.MAX_VALUE;
        
        for (Map.Entry<String, Map<String, Map<String, StatementStats>>> entry : allResults.entrySet()) {
            double totalTime = entry.getValue().values().stream()
                    .flatMap(tableMap -> tableMap.values().stream())
                    .mapToDouble(StatementStats::getTotalTime)
                    .sum();
            
            if (totalTime < minTotalTime) {
                minTotalTime = totalTime;
                baselineTable = entry.getKey();
            }
        }
        
        System.out.printf("Baseline table: %s with total time: %.2f ms%n", baselineTable, minTotalTime);
        
        // 生成比较CSV
        java.io.File outputDir = new java.io.File("output");
        if (!outputDir.exists()) {
            outputDir.mkdirs();
        }
        String csvFileName = "output/comparison" + suffix + ".csv";
        
        try (PrintWriter writer = new PrintWriter(new FileWriter(csvFileName))) {
            // 写入CSV头部
            writer.print("Table_Group,Operation");
            
            // 先输出基准表的列
            String baselineDisplayName = baselineTable.replace("summary_statement_", "");
            writer.printf(",%s_Total_Time_s,%s_Cumulative_Time_s", baselineDisplayName, baselineDisplayName);
            
            // 再按总时间从小到大排序输出其他表的列
            List<String> otherTables = new ArrayList<>();
            Map<String, Double> tableTotalTimes = new HashMap<>();
            
            // 计算每个表的总时间
            for (String tableName : statementTableNames) {
                if (!tableName.equals(baselineTable)) {
                    double totalTime = allResults.get(tableName).values().stream()
                            .flatMap(tableMap -> tableMap.values().stream())
                            .mapToDouble(StatementStats::getTotalTime)
                            .sum();
                    tableTotalTimes.put(tableName, totalTime);
                    otherTables.add(tableName);
                }
            }
            
            // 按总时间从小到大排序
            otherTables.sort((a, b) -> Double.compare(tableTotalTimes.get(a), tableTotalTimes.get(b)));
            
            // 输出排序后的其他表的列
            for (String tableName : otherTables) {
                String displayName = tableName.replace("summary_statement_", "");
                writer.printf(",%s_Total_Time_s,%s_Cumulative_Time_s,%s_vs_%s_Percentage,%s_vs_%s_Cumulative_Percentage", 
                    displayName, displayName, displayName, baselineDisplayName, displayName, baselineDisplayName);
            }
            writer.println();
            
            // 收集所有唯一的表组合和操作
            Set<String> allTableGroups = new HashSet<>();
            Set<String> allOperations = new HashSet<>();
            
            for (Map<String, Map<String, StatementStats>> results : allResults.values()) {
                allTableGroups.addAll(results.keySet());
                for (Map<String, StatementStats> opMap : results.values()) {
                    allOperations.addAll(opMap.keySet());
                }
            }
            
            // 收集所有数据并按基准表的total time降序排序
            List<ComparisonRow> comparisonRows = new ArrayList<>();
            
            for (String tableGroup : allTableGroups) {
                for (String operation : allOperations) {
                    Map<String, Double> allTimes = new HashMap<>();
                    Double baselineTime = null;
                    
                    // 收集所有表的时间数据
                    for (String tableName : allResults.keySet()) {
                        Map<String, Map<String, StatementStats>> results = allResults.get(tableName);
                        double time = 0.0;
                        
                        if (results.containsKey(tableGroup) && results.get(tableGroup).containsKey(operation)) {
                            time = results.get(tableGroup).get(operation).getTotalTime();
                        }
                        
                        allTimes.put(tableName, time);
                        if (tableName.equals(baselineTable)) {
                            baselineTime = time;
                        }
                    }
                    
                    // 只有基准表有数据时才添加到比较行
                    if (baselineTime != null && baselineTime > 0) {
                        comparisonRows.add(new ComparisonRow(tableGroup, operation, allTimes, baselineTable, baselineTime));
                    }
                }
            }
            
            // 按基准表的total time降序排序
            comparisonRows.sort((a, b) -> Double.compare(b.baselineTime, a.baselineTime));
            
            // 累计变量
            Map<String, Double> cumulativeTimes = new HashMap<>();
            for (String tableName : allResults.keySet()) {
                cumulativeTimes.put(tableName, 0.0);
            }
            
            // 写入排序后的数据
            for (ComparisonRow row : comparisonRows) {
                writer.printf("%s,%s", escapeCsvField(row.tableGroup), escapeCsvField(row.operation));
                
                // 更新累计时间
                for (String tableName : statementTableNames) {
                    double time = row.allTimes.get(tableName);
                    cumulativeTimes.put(tableName, cumulativeTimes.get(tableName) + time);
                }
                
                // 先输出基准表的数据
                double baselineTime = row.allTimes.get(baselineTable) / 1000000.0; // 转换为秒
                double baselineCumulative = cumulativeTimes.get(baselineTable) / 1000000.0; // 转换为秒
                writer.printf(",%.2f,%.2f", baselineTime, baselineCumulative);
                
                // 再按排序后的顺序输出其他表的数据和百分比
                for (String tableName : otherTables) {
                    double time = row.allTimes.get(tableName) / 1000000.0; // 转换为秒
                    double cumulativeTime = cumulativeTimes.get(tableName) / 1000000.0; // 转换为秒
                    
                    writer.printf(",%.2f,%.2f", time, cumulativeTime);
                    
                    if (row.baselineTime > 0) {
                        double percentage = ((row.allTimes.get(tableName) - row.baselineTime) / row.baselineTime) * 100;
                        double cumulativePercentage = cumulativeTimes.get(baselineTable) > 0 ? 
                            ((cumulativeTimes.get(tableName) - cumulativeTimes.get(baselineTable)) / cumulativeTimes.get(baselineTable)) * 100 : 0;
                        
                        writer.printf(",%.2f%%,%.2f%%", percentage, cumulativePercentage);
                    }
                }
                writer.println();
            }
            
            System.out.println("Comparison analysis exported to: " + csvFileName);
            
        } catch (IOException e) {
            System.err.println("Error writing comparison CSV file: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    // 辅助类用于排序
    private static class ComparisonRow {
        String tableGroup;
        String operation;
        Map<String, Double> allTimes;
        String baselineTable;
        double baselineTime;
        
        ComparisonRow(String tableGroup, String operation, Map<String, Double> allTimes, String baselineTable, double baselineTime) {
            this.tableGroup = tableGroup;
            this.operation = operation;
            this.allTimes = allTimes;
            this.baselineTable = baselineTable;
            this.baselineTime = baselineTime;
        }
    }
    
    // 提取输出逻辑为独立方法
    private static void exportResults(Map<String, Map<String, StatementStats>> results, 
                                    String statementTableName, String suffix) {
        java.io.File outputDir = new java.io.File("output");
        if (!outputDir.exists()) {
            outputDir.mkdirs();
        }
        String csvFileName = "output/SQL_ANL" + suffix + ".csv";
        
        try (PrintWriter writer = new PrintWriter(new FileWriter(csvFileName))) {
            // 计算总时间用于百分比计算
            double grandTotalTime = results.values().stream()
                    .flatMap(tableMap -> tableMap.values().stream())
                    .mapToDouble(StatementStats::getTotalTime)
                    .sum();
            
            // 写入CSV头部
            writer.println("Table,Operation,Total_Time_s,Average_Time_s,Count,Unique_SQL_IDs,All_Unique_SQL_IDs,Total_Returned_Rows,Total_Tuples_Fetched,Total_Tuples_Returned,Total_Tuples_Inserted,Total_Tuples_Updated,Total_Tuples_Deleted,Time_Percentage");
            
            // 写入数据行 - 按总时间降序排序
            results.entrySet().stream()
                .flatMap(tableEntry -> 
                    tableEntry.getValue().entrySet().stream()
                        .map(opEntry -> new Object[]{
                            tableEntry.getKey(), 
                            opEntry.getKey(), 
                            opEntry.getValue()
                        }))
                .sorted((a, b) -> Double.compare(
                    ((StatementStats)b[2]).getTotalTime(), 
                    ((StatementStats)a[2]).getTotalTime()))
                .forEach(entry -> {
                    String tableName = (String) entry[0];
                    String operation = (String) entry[1];
                    StatementStats stats = (StatementStats) entry[2];
                    double timePercentage = grandTotalTime > 0 ? (stats.getTotalTime() / grandTotalTime) * 100 : 0;
                    
                    writer.printf("%s,%s,%.2f,%.2f,%d,%s,%s,%d,%d,%d,%d,%d,%d,%.2f%%%n", 
                        escapeCsvField(tableName), 
                        escapeCsvField(operation), 
                        stats.getTotalTime() / 1000000.0, // 转换为秒
                        stats.getAverageTime() / 1000000.0, // 转换为秒
                        stats.getCount(),
                        escapeCsvField(stats.getUniqueSqlIdsString()),
                        escapeCsvField(stats.getAllUniqueSqlIdsString()),
                        stats.getTotalReturnedRows(),
                        stats.getTotalTuplesFetched(),
                        stats.getTotalTuplesReturned(),
                        stats.getTotalTuplesInserted(),
                        stats.getTotalTuplesUpdated(),
                        stats.getTotalTuplesDeleted(),
                        timePercentage);
                });
            
            // 在文件末尾添加总计行
            writer.printf("TOTAL,ALL,%.2f,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,N/A,100.00%%%n", grandTotalTime / 1000000.0);
            
            System.out.println("Analysis results exported to: " + csvFileName);
            System.out.printf("Grand total time: %.2f ms%n", grandTotalTime);
            
        } catch (IOException e) {
            System.err.println("Error writing CSV file: " + e.getMessage());
            e.printStackTrace();
        }
    }

    // 辅助类：用于存储每个操作的统计信息
    static class StatementStats {
        private double totalTime;
        private int count;
        private Map<String, Integer> uniqueSqlIdCalls; // 改为Map存储calls用于排序
        private Set<String> allUniqueSqlIds; // 新增：存储所有unique_sql_id
        private long totalReturnedRows;
        private long totalTuplesFetched;
        private long totalTuplesReturned;
        private long totalTuplesInserted;
        private long totalTuplesUpdated;
        private long totalTuplesDeleted;
        private String statementTableName; // 新增

        public StatementStats(String statementTableName) {
            this.totalTime = 0.0;
            this.count = 0;
            this.uniqueSqlIdCalls = new HashMap<>();
            this.allUniqueSqlIds = new HashSet<>();
            this.totalReturnedRows = 0;
            this.totalTuplesFetched = 0;
            this.totalTuplesReturned = 0;
            this.totalTuplesInserted = 0;
            this.totalTuplesUpdated = 0;
            this.totalTuplesDeleted = 0;
            this.statementTableName = statementTableName;
        }

        public void addExecution(double time, String uniqueSqlId, int nCalls, long returnedRows, long tuplesFetched, long tuplesReturned, long tuplesInserted, long tuplesUpdated, long tuplesDeleted) {
            this.totalTime += time;
            this.count++;
            this.uniqueSqlIdCalls.put(uniqueSqlId, nCalls);
            this.allUniqueSqlIds.add(uniqueSqlId); // 添加到集合中
            this.totalReturnedRows += returnedRows;
            this.totalTuplesFetched += tuplesFetched;
            this.totalTuplesReturned += tuplesReturned;
            this.totalTuplesInserted += tuplesInserted;
            this.totalTuplesUpdated += tuplesUpdated;
            this.totalTuplesDeleted += tuplesDeleted;
        }

        public double getTotalTime() {
            return this.totalTime;
        }

        public double getAverageTime() {
            return count == 0 ? 0 : totalTime / count;
        }

        public int getCount() {
            return count;
        }
        
        public String getUniqueSqlIdsString() {
            return uniqueSqlIdCalls.entrySet().stream()
                    .sorted((e1, e2) -> Integer.compare(e2.getValue(), e1.getValue())) // 按nCalls降序
                    .map(e -> e.getKey() + ":" + e.getValue())
                    .collect(Collectors.joining(","));
        }
        
        public String getAllUniqueSqlIdsString() {
            String uniqueIds = allUniqueSqlIds.stream().sorted().collect(Collectors.joining(","));
            return "SELECT unique_sql_id, query, n_calls, total_elapse_time, n_returned_rows, n_tuples_fetched, n_tuples_returned FROM " + 
                   statementTableName + " WHERE unique_sql_id IN (" + uniqueIds + ") ORDER BY total_elapse_time desc;";
        }
        
        public long getTotalReturnedRows() {
            return totalReturnedRows;
        }
        
        public long getTotalTuplesFetched() {
            return totalTuplesFetched;
        }
        
        public long getTotalTuplesReturned() {
            return totalTuplesReturned;
        }
        
        public long getTotalTuplesInserted() {
            return totalTuplesInserted;
        }
        
        public long getTotalTuplesUpdated() {
            return totalTuplesUpdated;
        }
        
        public long getTotalTuplesDeleted() {
            return totalTuplesDeleted;
        }
    }

    // CSV字段转义方法
    private static String escapeCsvField(String field) {
        if (field == null) {
            return "";
        }
        
        // 如果包含逗号、双引号或换行符，需要用双引号包围并转义内部双引号
        if (field.contains(",") || field.contains("\"") || field.contains("\n") || field.contains("\r")) {
            // 将双引号转义为两个双引号
            String escaped = field.replace("\"", "\"\"");
            return "\"" + escaped + "\"";
        }
        
        return field;
    }
}
