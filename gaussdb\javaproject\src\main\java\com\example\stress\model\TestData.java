package com.example.stress.model;

import java.sql.Timestamp;
import java.util.Objects;

/**
 * 测试数据模型类
 * 用于压力测试的数据结构
 */
public class TestData {
    private Long id;
    private String cPortCode;  // 分组字段
    private String dataValue;
    private String description;
    private Integer status;
    private Timestamp createTime;
    private Timestamp updateTime;
    
    public TestData() {
    }
    
    public TestData(Long id, String cPortCode, String dataValue, String description, Integer status) {
        this.id = id;
        this.cPortCode = cPortCode;
        this.dataValue = dataValue;
        this.description = description;
        this.status = status;
        this.createTime = new Timestamp(System.currentTimeMillis());
        this.updateTime = new Timestamp(System.currentTimeMillis());
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getCPortCode() {
        return cPortCode;
    }
    
    public void setCPortCode(String cPortCode) {
        this.cPortCode = cPortCode;
    }
    
    public String getDataValue() {
        return dataValue;
    }
    
    public void setDataValue(String dataValue) {
        this.dataValue = dataValue;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public Timestamp getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }
    
    public Timestamp getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TestData testData = (TestData) o;
        return Objects.equals(id, testData.id) &&
               Objects.equals(cPortCode, testData.cPortCode);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id, cPortCode);
    }
    
    @Override
    public String toString() {
        return "TestData{" +
                "id=" + id +
                ", cPortCode='" + cPortCode + '\'' +
                ", dataValue='" + dataValue + '\'' +
                ", description='" + description + '\'' +
                ", status=" + status +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
