package com.example.stress.monitor;

/**
 * 操作统计信息
 * 封装单个操作类型的性能统计数据
 */
public class OperationStats {
    private final String operationType;
    private final long successCount;
    private final long failureCount;
    private final long totalTimeNanos;
    private final long minTimeNanos;
    private final long maxTimeNanos;
    
    public OperationStats(String operationType, long successCount, long failureCount, 
                         long totalTimeNanos, long minTimeNanos, long maxTimeNanos) {
        this.operationType = operationType;
        this.successCount = successCount;
        this.failureCount = failureCount;
        this.totalTimeNanos = totalTimeNanos;
        this.minTimeNanos = minTimeNanos;
        this.maxTimeNanos = maxTimeNanos;
    }
    
    public String getOperationType() {
        return operationType;
    }
    
    public long getSuccessCount() {
        return successCount;
    }
    
    public long getFailureCount() {
        return failureCount;
    }
    
    public long getTotalOperations() {
        return successCount + failureCount;
    }
    
    public long getTotalTimeNanos() {
        return totalTimeNanos;
    }
    
    public long getMinTimeNanos() {
        return minTimeNanos;
    }
    
    public long getMaxTimeNanos() {
        return maxTimeNanos;
    }
    
    /**
     * 获取平均时间（毫秒）
     */
    public double getAverageTimeMs() {
        if (getTotalOperations() == 0) {
            return 0.0;
        }
        return (double) totalTimeNanos / getTotalOperations() / 1_000_000.0;
    }
    
    /**
     * 获取最小时间（毫秒）
     */
    public double getMinTimeMs() {
        return (double) minTimeNanos / 1_000_000.0;
    }
    
    /**
     * 获取最大时间（毫秒）
     */
    public double getMaxTimeMs() {
        return (double) maxTimeNanos / 1_000_000.0;
    }
    
    /**
     * 获取成功率（百分比）
     */
    public double getSuccessRate() {
        if (getTotalOperations() == 0) {
            return 0.0;
        }
        return (double) successCount / getTotalOperations() * 100.0;
    }
    
    /**
     * 获取失败率（百分比）
     */
    public double getFailureRate() {
        return 100.0 - getSuccessRate();
    }
    
    /**
     * 获取吞吐量（操作/秒）
     */
    public double getThroughputPerSecond() {
        if (totalTimeNanos == 0) {
            return 0.0;
        }
        return (double) getTotalOperations() / (totalTimeNanos / 1_000_000_000.0);
    }
    
    @Override
    public String toString() {
        return String.format("%s Stats: %d ops (%.2f%% success), Avg: %.2fms, Min: %.2fms, Max: %.2fms, Throughput: %.2f ops/sec",
                operationType, getTotalOperations(), getSuccessRate(), 
                getAverageTimeMs(), getMinTimeMs(), getMaxTimeMs(), getThroughputPerSecond());
    }
}
