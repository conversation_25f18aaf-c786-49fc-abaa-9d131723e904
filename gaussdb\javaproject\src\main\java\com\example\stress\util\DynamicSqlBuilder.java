package com.example.stress.util;

import com.example.stress.model.TableStructure;
import com.example.stress.config.UpdateFieldConfig;
import com.example.stress.model.TableStructure.ColumnInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.*;
import java.util.HashMap;
import java.util.Map;

/**
 * 动态SQL构建器
 * 根据表结构动态构建SQL语句和设置参数
 */
public class DynamicSqlBuilder {
    private static final Logger logger = LoggerFactory.getLogger(DynamicSqlBuilder.class);
    
    /**
     * 构建INSERT语句
     */
    public static String buildInsertSql(TableStructure tableStructure) {
        String tableName = tableStructure.getTableName();
        String columnList = tableStructure.getInsertColumnList();
        String valuesList = tableStructure.getInsertValuesList();
        
        return String.format("INSERT INTO %s (%s) VALUES (%s)", tableName, columnList, valuesList);
    }
    
    /**
     * 构建SELECT语句
     */
    public static String buildSelectSql(TableStructure tableStructure, String whereClause) {
        String tableName = tableStructure.getTableName();
        String columnList = tableStructure.getSelectColumnList();
        
        if (whereClause != null && !whereClause.trim().isEmpty()) {
            return String.format("SELECT %s FROM %s WHERE %s", columnList, tableName, whereClause);
        } else {
            return String.format("SELECT %s FROM %s", columnList, tableName);
        }
    }
    
    /**
     * 构建带分页的SELECT语句
     */
    public static String buildSelectSqlWithPaging(TableStructure tableStructure, String whereClause,
                                                  String orderByClause, int limit, int offset) {
        String baseSql = buildSelectSql(tableStructure, whereClause);

        StringBuilder sql = new StringBuilder(baseSql);

        if (orderByClause != null && !orderByClause.trim().isEmpty()) {
            sql.append(" ORDER BY ").append(orderByClause);
        }

        sql.append(" LIMIT ").append(limit);
        if (offset > 0) {
            sql.append(" OFFSET ").append(offset);
        }

        return sql.toString();
    }

    /**
     * 构建带分页的SELECT语句，支持将配置的更新字段设置为null
     */
    public static String buildSelectSqlWithPagingAndUpdateFields(TableStructure tableStructure, String whereClause,
                                                                 String orderByClause, int limit, int offset) {
        String tableName = tableStructure.getTableName();

        // 构建列列表，对于更新字段使用null
        StringBuilder columnList = new StringBuilder();
        for (int i = 0; i < tableStructure.getColumns().size(); i++) {
            if (i > 0) {
                columnList.append(", ");
            }

            ColumnInfo column = tableStructure.getColumns().get(i);
            if (UpdateFieldConfig.isUpdateField(column.getColumnName())) {
                columnList.append("NULL AS ").append(column.getColumnName());
            } else {
                columnList.append(column.getColumnName());
            }
        }

        StringBuilder sql = new StringBuilder("SELECT ");
        sql.append(columnList.toString()).append(" FROM ").append(tableName);

        if (whereClause != null && !whereClause.trim().isEmpty()) {
            sql.append(" WHERE ").append(whereClause);
        }

        if (orderByClause != null && !orderByClause.trim().isEmpty()) {
            sql.append(" ORDER BY ").append(orderByClause);
        }

        sql.append(" LIMIT ").append(limit);
        if (offset > 0) {
            sql.append(" OFFSET ").append(offset);
        }

        return sql.toString();
    }
    
    /**
     * 从ResultSet动态设置PreparedStatement参数
     */
    public static void setParametersFromResultSet(PreparedStatement stmt, ResultSet rs,
                                                  TableStructure tableStructure) throws SQLException {
        for (int i = 0; i < tableStructure.getColumns().size(); i++) {
            ColumnInfo column = tableStructure.getColumns().get(i);
            int paramIndex = i + 1;

            setParameterByType(stmt, rs, paramIndex, column);
        }
    }

    /**
     * 从ResultSet动态设置PreparedStatement参数，支持更新字段配置
     * 对于配置为更新字段的列，将设置为null
     */
    public static void setParametersFromResultSetWithUpdateFields(PreparedStatement stmt, ResultSet rs,
                                                                  TableStructure tableStructure) throws SQLException {
        for (int i = 0; i < tableStructure.getColumns().size(); i++) {
            ColumnInfo column = tableStructure.getColumns().get(i);
            int paramIndex = i + 1;

            // 检查是否为配置的更新字段
            if (UpdateFieldConfig.isUpdateField(column.getColumnName())) {
                // 设置为null
                stmt.setNull(paramIndex, getSqlType(column.getDataType()));
                // logger.debug("Set update field {} to null at parameter index {}", column.getColumnName(), paramIndex);
            } else {
                // 正常设置参数
                setParameterByType(stmt, rs, paramIndex, column);
            }
        }
    }
    
    /**
     * 根据列类型设置参数
     */
    private static void setParameterByType(PreparedStatement stmt, ResultSet rs, 
                                          int paramIndex, ColumnInfo column) throws SQLException {
        String columnName = column.getColumnName();
        String dataType = column.getDataType().toUpperCase();
        
        try {
            // 检查值是否为null
            Object value = rs.getObject(columnName);
            if (value == null) {
                stmt.setNull(paramIndex, getSqlType(dataType));
                return;
            }
            
            // 根据数据类型设置参数
            if (column.isDateTimeType()) {
                if (dataType.contains("TIMESTAMP")) {
                    stmt.setTimestamp(paramIndex, rs.getTimestamp(columnName));
                } else if (dataType.contains("DATE")) {
                    stmt.setDate(paramIndex, rs.getDate(columnName));
                } else if (dataType.contains("TIME")) {
                    stmt.setTime(paramIndex, rs.getTime(columnName));
                } else {
                    stmt.setTimestamp(paramIndex, rs.getTimestamp(columnName));
                }
            } else if (column.isBigIntType()) {
                stmt.setLong(paramIndex, rs.getLong(columnName));
            } else if (column.isDecimalType()) {
                stmt.setBigDecimal(paramIndex, rs.getBigDecimal(columnName));
            } else if (column.isFloatType()) {
                if (dataType.contains("DOUBLE")) {
                    stmt.setDouble(paramIndex, rs.getDouble(columnName));
                } else {
                    stmt.setFloat(paramIndex, rs.getFloat(columnName));
                }
            } else if (column.isNumericType()) {
                if (dataType.contains("INT")) {
                    stmt.setInt(paramIndex, rs.getInt(columnName));
                } else {
                    stmt.setLong(paramIndex, rs.getLong(columnName));
                }
            } else {
                // 默认作为字符串处理
                stmt.setString(paramIndex, rs.getString(columnName));
            }
            
        } catch (SQLException e) {
            logger.warn("Failed to set parameter {} ({}) with specific type, falling back to string: {}", 
                       paramIndex, columnName, e.getMessage());
            // 回退到字符串类型
            stmt.setString(paramIndex, rs.getString(columnName));
        }
    }
    
    /**
     * 获取SQL类型常量
     */
    private static int getSqlType(String dataType) {
        dataType = dataType.toUpperCase();
        
        if (dataType.contains("VARCHAR") || dataType.contains("CHAR")) {
            return Types.VARCHAR;
        } else if (dataType.contains("TIMESTAMP")) {
            return Types.TIMESTAMP;
        } else if (dataType.contains("DATE")) {
            return Types.DATE;
        } else if (dataType.contains("TIME")) {
            return Types.TIME;
        } else if (dataType.contains("BIGINT")) {
            return Types.BIGINT;
        } else if (dataType.contains("INT")) {
            return Types.INTEGER;
        } else if (dataType.contains("DECIMAL") || dataType.contains("NUMERIC")) {
            return Types.DECIMAL;
        } else if (dataType.contains("DOUBLE")) {
            return Types.DOUBLE;
        } else if (dataType.contains("FLOAT")) {
            return Types.FLOAT;
        } else {
            return Types.VARCHAR; // 默认
        }
    }
    
    /**
     * 创建ResultSet到Map的转换器
     */
    public static Map<String, Object> resultSetToMap(ResultSet rs, TableStructure tableStructure) throws SQLException {
        Map<String, Object> row = new HashMap<>();
        
        for (ColumnInfo column : tableStructure.getColumns()) {
            String columnName = column.getColumnName();
            Object value = rs.getObject(columnName);
            row.put(columnName, value);
        }
        
        return row;
    }
    
    /**
     * 构建UPDATE语句
     */
    public static String buildUpdateSql(TableStructure tableStructure, String[] updateColumns, String whereClause) {
        String tableName = tableStructure.getTableName();
        
        StringBuilder sql = new StringBuilder("UPDATE ");
        sql.append(tableName).append(" SET ");
        
        for (int i = 0; i < updateColumns.length; i++) {
            if (i > 0) {
                sql.append(", ");
            }
            sql.append(updateColumns[i]).append(" = ?");
        }
        
        if (whereClause != null && !whereClause.trim().isEmpty()) {
            sql.append(" WHERE ").append(whereClause);
        }
        
        return sql.toString();
    }
    
    /**
     * 构建DELETE语句
     */
    public static String buildDeleteSql(String tableName, String whereClause) {
        StringBuilder sql = new StringBuilder("DELETE FROM ");
        sql.append(tableName);
        
        if (whereClause != null && !whereClause.trim().isEmpty()) {
            sql.append(" WHERE ").append(whereClause);
        }
        
        return sql.toString();
    }
    
    /**
     * 构建COUNT语句
     */
    public static String buildCountSql(String tableName, String whereClause) {
        StringBuilder sql = new StringBuilder("SELECT COUNT(*) FROM ");
        sql.append(tableName);
        
        if (whereClause != null && !whereClause.trim().isEmpty()) {
            sql.append(" WHERE ").append(whereClause);
        }
        
        return sql.toString();
    }
    
    /**
     * 获取主键列名（简单启发式方法）
     */
    public static String getPrimaryKeyColumn(TableStructure tableStructure) {
        // 查找名为 'id' 或包含 'id' 的列
        for (ColumnInfo column : tableStructure.getColumns()) {
            String columnName = column.getColumnName().toLowerCase();
            if (columnName.equals("id") || columnName.startsWith("id_") || columnName.endsWith("_id")) {
                return column.getColumnName();
            }
        }
        
        // 如果没找到，返回第一列
        if (!tableStructure.getColumns().isEmpty()) {
            return tableStructure.getColumns().get(0).getColumnName();
        }
        
        return "id"; // 默认值
    }
}
