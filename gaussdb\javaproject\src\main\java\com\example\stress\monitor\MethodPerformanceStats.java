package com.example.stress.monitor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.Map;

/**
 * 方法性能统计类
 * 用于统计各个方法中不同环节的耗时
 */
public class MethodPerformanceStats {
    private static final Logger logger = LoggerFactory.getLogger(MethodPerformanceStats.class);
    
    // 存储各个方法各个环节的累计耗时（纳秒）
    private static final Map<String, AtomicLong> cumulativeTimeMap = new ConcurrentHashMap<>();
    
    // 存储各个方法各个环节的调用次数
    private static final Map<String, AtomicLong> callCountMap = new ConcurrentHashMap<>();
    
    /**
     * 记录某个方法某个环节的耗时
     * @param methodName 方法名
     * @param stepName 环节名
     * @param timeNanos 耗时（纳秒）
     */
    public static void recordTime(String methodName, String stepName, long timeNanos) {
        String key = methodName + "." + stepName;
        cumulativeTimeMap.computeIfAbsent(key, k -> new AtomicLong(0)).addAndGet(timeNanos);
        callCountMap.computeIfAbsent(key, k -> new AtomicLong(0)).incrementAndGet();
    }
    
    /**
     * 获取某个环节的累计耗时（毫秒）
     */
    public static double getCumulativeTimeMs(String methodName, String stepName) {
        String key = methodName + "." + stepName;
        AtomicLong time = cumulativeTimeMap.get(key);
        return time != null ? time.get() / 1_000_000.0 : 0.0;
    }
    
    /**
     * 获取某个环节的调用次数
     */
    public static long getCallCount(String methodName, String stepName) {
        String key = methodName + "." + stepName;
        AtomicLong count = callCountMap.get(key);
        return count != null ? count.get() : 0;
    }
    
    /**
     * 获取某个环节的平均耗时（毫秒）
     */
    public static double getAverageTimeMs(String methodName, String stepName) {
        long count = getCallCount(methodName, stepName);
        if (count == 0) return 0.0;
        return getCumulativeTimeMs(methodName, stepName) / count;
    }
    
    /**
     * 打印所有统计信息
     */
    public static void printAllStats() {
        logger.info("=== Method Performance Statistics ===");
        
        // 按方法分组打印
        Map<String, Map<String, String>> methodStats = new ConcurrentHashMap<>();
        
        for (String key : cumulativeTimeMap.keySet()) {
            String[] parts = key.split("\\.", 2);
            if (parts.length == 2) {
                String methodName = parts[0];
                String stepName = parts[1];
                
                methodStats.computeIfAbsent(methodName, k -> new ConcurrentHashMap<>())
                          .put(stepName, String.format("%.2f ms (avg: %.2f ms, calls: %d)",
                              getCumulativeTimeMs(methodName, stepName),
                              getAverageTimeMs(methodName, stepName),
                              getCallCount(methodName, stepName)));
            }
        }
        
        for (Map.Entry<String, Map<String, String>> methodEntry : methodStats.entrySet()) {
            logger.info("Method: {}", methodEntry.getKey());
            for (Map.Entry<String, String> stepEntry : methodEntry.getValue().entrySet()) {
                logger.info("  {}: {}", stepEntry.getKey(), stepEntry.getValue());
            }
        }
    }
    
    /**
     * 清空所有统计信息
     */
    public static void clear() {
        cumulativeTimeMap.clear();
        callCountMap.clear();
    }
    
    /**
     * 打印某个方法的统计信息
     */
    public static void printMethodStats(String methodName) {
        logger.info("=== {} Performance Statistics ===", methodName);
        
        for (String key : cumulativeTimeMap.keySet()) {
            if (key.startsWith(methodName + ".")) {
                String stepName = key.substring(methodName.length() + 1);
                logger.info("  {}: {} ms (avg: {} ms, calls: {})",
                           stepName,
                           String.format("%.2f", getCumulativeTimeMs(methodName, stepName)),
                           String.format("%.2f", getAverageTimeMs(methodName, stepName)),
                           getCallCount(methodName, stepName));
            }
        }
    }
}
