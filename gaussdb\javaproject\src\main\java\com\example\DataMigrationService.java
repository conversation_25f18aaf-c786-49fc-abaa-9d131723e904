package com.example;

import com.example.util.DatabaseUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;

public class DataMigrationService {
    private static final Logger logger = LoggerFactory.getLogger(DataMigrationService.class);

    private final String sourceTable;
    private final String targetTable;
    private final int batchSize;
    private final int threadPoolSize;
    private final ExecutorService executorService;

    // 分组字段名
    private final String groupByColumnName;

    // 日期分批相关字段
    private final String dateColumnName;        // 日期字段名
    private final boolean useDateBatch;         // 是否使用日期分批
    private final int batchDays;               // 每批处理的天数

    // 目标数据库配置
    private final DatabaseConfig targetDbConfig;

    // 缓存表字段信息
    private volatile List<ColumnInfo> sourceTableColumns;
    private volatile List<ColumnInfo> targetTableColumns;

    // 列信息类
    public static class ColumnInfo {
        private final String columnName;
        private final String dataType;
        private final int columnOrder;

        public ColumnInfo(String columnName, String dataType, int columnOrder) {
            this.columnName = columnName;
            this.dataType = dataType;
            this.columnOrder = columnOrder;
        }

        public String getColumnName() { return columnName; }
        public String getDataType() { return dataType; }
        public int getColumnOrder() { return columnOrder; }

        /**
         * 判断是否为日期时间类型
         */
        public boolean isDateTimeType() {
            String type = dataType.toUpperCase();
            return type.contains("DATE") || type.contains("TIME") || type.contains("TIMESTAMP");
        }

        /**
         * 判断是否为字符串类型
         */
        public boolean isStringType() {
            String type = dataType.toUpperCase();
            return type.contains("CHAR") || type.contains("TEXT") || type.contains("STRING");
        }

        /**
         * 判断是否为数值类型
         */
        public boolean isNumericType() {
            String type = dataType.toUpperCase();
            return type.contains("INT") || type.contains("DECIMAL") || type.contains("NUMERIC") ||
                   type.contains("FLOAT") || type.contains("DOUBLE") || type.contains("NUMBER");
        }

        @Override
        public String toString() {
            return String.format("ColumnInfo{name='%s', type='%s', order=%d}",
                               columnName, dataType, columnOrder);
        }
    }

    // 数据库配置类（委托给配置文件管理）
    public static class DatabaseConfig {
        private final com.example.config.DatabaseConnectionConfig.DatabaseConfig config;

        public DatabaseConfig(String url, String username, String password) {
            com.example.util.ConnectionPoolManager.DatabaseType type =
                com.example.util.ConnectionPoolManager.detectDatabaseType(url);
            this.config = new com.example.config.DatabaseConnectionConfig.DatabaseConfig(url, username, password, type);
        }

        public DatabaseConfig(String url, String username, String password,
                             com.example.util.ConnectionPoolManager.DatabaseType databaseType) {
            this.config = new com.example.config.DatabaseConnectionConfig.DatabaseConfig(url, username, password, databaseType);
        }

        // 从配置文件创建
        private DatabaseConfig(com.example.config.DatabaseConnectionConfig.DatabaseConfig config) {
            this.config = config;
        }

        public String getUrl() { return config.getUrl(); }
        public String getUsername() { return config.getUsername(); }
        public String getPassword() { return config.getPassword(); }
        public com.example.util.ConnectionPoolManager.DatabaseType getDatabaseType() { return config.getType(); }

        // 从配置文件获取默认数据库配置
        public static DatabaseConfig getDefault() {
            com.example.config.DatabaseConnectionConfig.DatabaseConfig defaultConfig =
                com.example.config.DatabaseConnectionConfig.getDefaultDatabaseConfig();
            return new DatabaseConfig(defaultConfig);
        }

        // 从配置文件获取源数据库配置
        public static DatabaseConfig getSource() {
            com.example.config.DatabaseConnectionConfig.DatabaseConfig sourceConfig =
                com.example.config.DatabaseConnectionConfig.getSourceDatabaseConfig();
            return new DatabaseConfig(sourceConfig);
        }

        // 根据数据库类型从配置文件获取配置
        public static DatabaseConfig getByType(com.example.util.ConnectionPoolManager.DatabaseType databaseType) {
            com.example.config.DatabaseConnectionConfig.DatabaseConfig dbConfig =
                com.example.config.DatabaseConnectionConfig.getDatabaseConfigByType(databaseType);
            return new DatabaseConfig(dbConfig);
        }

        // 创建StarRocks数据库配置
        public static DatabaseConfig createStarRocksConfig(String host, int port, String database,
                                                           String username, String password) {
            String url = String.format("jdbc:mysql://%s:%d/%s", host, port, database);
            return new DatabaseConfig(url, username, password,
                                     com.example.util.ConnectionPoolManager.DatabaseType.STARROCKS);
        }

        // 创建GaussDB数据库配置
        public static DatabaseConfig createGaussDBConfig(String host, int port, String database,
                                                         String username, String password) {
            String url = String.format("*********************************************************************************************",
                                      host, port, database);
            return new DatabaseConfig(url, username, password,
                                     com.example.util.ConnectionPoolManager.DatabaseType.GAUSSDB);
        }

        @Override
        public String toString() {
            return config.toString();
        }
    }
    
    public DataMigrationService(String sourceTable, String targetTable) {
        this(sourceTable, targetTable, 2000, 4);
    }

    public DataMigrationService(String sourceTable, String targetTable, int batchSize, int threadPoolSize) {
        this(sourceTable, targetTable, batchSize, threadPoolSize, DatabaseConfig.getDefault());
    }

    public DataMigrationService(String sourceTable, String targetTable, int batchSize, int threadPoolSize, DatabaseConfig targetDbConfig) {
        this(sourceTable, targetTable, batchSize, threadPoolSize, "c_port_code", false, null, 1, targetDbConfig);
    }

    public DataMigrationService(String sourceTable, String targetTable, int batchSize, int threadPoolSize,
                               String groupByColumnName) {
        this(sourceTable, targetTable, batchSize, threadPoolSize, groupByColumnName, false, null, 1, DatabaseConfig.getDefault());
    }

    // 主构造函数
    public DataMigrationService(String sourceTable, String targetTable, int batchSize, int threadPoolSize,
                               String groupByColumnName, boolean useDateBatch, String dateColumnName,
                               int batchDays, DatabaseConfig targetDbConfig) {
        this.sourceTable = sourceTable;
        this.targetTable = targetTable;
        this.batchSize = batchSize;
        this.threadPoolSize = threadPoolSize;
        this.groupByColumnName = groupByColumnName;
        this.useDateBatch = useDateBatch;
        this.dateColumnName = dateColumnName;
        this.batchDays = batchDays;
        this.targetDbConfig = targetDbConfig;
        this.executorService = Executors.newFixedThreadPool(threadPoolSize);
    }

    // 为了向后兼容，保留原有的构造函数
    public DataMigrationService(String sourceTable, String targetTable, int batchSize, int threadPoolSize,
                               String metadataViewName, String groupByColumnName) {
        this(sourceTable, targetTable, batchSize, threadPoolSize, groupByColumnName, false, null, 1, DatabaseConfig.getDefault());
    }

    // 日期分批构造函数
    public static DataMigrationService createWithDateBatch(String sourceTable, String targetTable,
                                                           String dateColumnName, int batchDays) {
        return new DataMigrationService(sourceTable, targetTable, 2000, 4,
                                      "c_port_code", true, dateColumnName, batchDays, DatabaseConfig.getDefault());
    }

    public static DataMigrationService createWithDateBatch(String sourceTable, String targetTable,
                                                           int batchSize, int threadPoolSize,
                                                           String groupByColumnName, String dateColumnName, int batchDays) {
        return new DataMigrationService(sourceTable, targetTable, batchSize, threadPoolSize,
                                      groupByColumnName, true, dateColumnName, batchDays, DatabaseConfig.getDefault());
    }

    // 支持自定义目标数据库的日期分批构造函数
    public static DataMigrationService createWithDateBatch(String sourceTable, String targetTable,
                                                           int batchSize, int threadPoolSize,
                                                           String groupByColumnName, String dateColumnName, int batchDays,
                                                           DatabaseConfig targetDbConfig) {
        return new DataMigrationService(sourceTable, targetTable, batchSize, threadPoolSize,
                                      groupByColumnName, true, dateColumnName, batchDays, targetDbConfig);
    }
    
    /**
     * 使用默认数据库配置进行数据迁移
     * @param planCode 计划代码
     */
    public void migrateData(String planCode) {
        migrateData(planCode, DatabaseConfig.getDefault());
    }

    /**
     * 使用指定数据库配置进行数据迁移
     * @param planCode 计划代码
     * @param sourceDbConfig 源数据库配置
     */
    public void migrateData(String planCode, DatabaseConfig sourceDbConfig) {
        long startTime = System.currentTimeMillis();
        logger.info("Starting migration with database: {}", sourceDbConfig.getUrl());

        try {
            // 获取所有不同的c_port_code值
            long getPortCodesStart = System.currentTimeMillis();
            List<String> portCodes = getDistinctPortCodes(planCode, sourceDbConfig);
            long getPortCodesEnd = System.currentTimeMillis();
            logger.info("Found {} distinct port codes, time cost: {} ms",
                       portCodes.size(), getPortCodesEnd - getPortCodesStart);

            List<Future<MigrationResult>> futures = new ArrayList<>();

            // 为每个port_code创建并行任务
            for (String portCode : portCodes) {
                Future<MigrationResult> future = executorService.submit(() ->
                    migrateDataByPortCode(portCode, sourceDbConfig));
                futures.add(future);
            }

            // 等待所有任务完成并统计结果
            int totalMigrated = 0;
            long totalReadTime = 0;
            long totalWriteTime = 0;

            for (Future<MigrationResult> future : futures) {
                MigrationResult result = future.get();
                totalMigrated += result.recordCount;
                totalReadTime += result.readTime;
                totalWriteTime += result.writeTime;
            }

            long endTime = System.currentTimeMillis();
            long totalTime = endTime - startTime;

            logger.info("=== Migration Summary ===");
            logger.info("Source Database: {}", sourceDbConfig.getUrl());
            logger.info("Total migrated records: {}", totalMigrated);
            logger.info("Total time: {} ms", totalTime);
            logger.info("Total read time: {} ms", totalReadTime);
            logger.info("Total write time: {} ms", totalWriteTime);
            String formattedAvgReadTime = String.format("%.2f",
                       totalMigrated > 0 ? (double)totalReadTime / totalMigrated : 0);
            logger.info("Average read time per record: {} ms", formattedAvgReadTime);
            String formattedAvgWriteTime = String.format("%.2f",
                       totalMigrated > 0 ? (double)totalWriteTime / totalMigrated : 0);
            logger.info("Average write time per record: {} ms", formattedAvgWriteTime);
            String formattedThroughput = String.format("%.2f",
                       totalTime > 0 ? (double)totalMigrated * 1000 / totalTime : 0);
            logger.info("Throughput: {} records/second", formattedThroughput);

        } catch (Exception e) {
            logger.error("Error during data migration from database: {}", sourceDbConfig.getUrl(), e);
        } finally {
            executorService.shutdown();
        }
    }

    /**
     * 基于日期范围进行数据迁移
     * @param planCode 计划代码
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     */
    public void migrateDataByDateRange(String planCode, String startDate, String endDate) {
        migrateDataByDateRange(planCode, startDate, endDate, DatabaseConfig.getDefault());
    }

    /**
     * 基于日期范围进行数据迁移（使用自定义源数据库配置）
     * @param planCode 计划代码
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     * @param sourceDbConfig 源数据库配置
     */
    public void migrateDataByDateRange(String planCode, String startDate, String endDate, DatabaseConfig sourceDbConfig) {
        if (!useDateBatch) {
            logger.warn("Date batch mode is not enabled, falling back to regular migration");
            migrateData(planCode, sourceDbConfig);
            return;
        }

        long startTime = System.currentTimeMillis();
        logger.info("Starting date-based migration from {} to {} with database: {}", startDate, endDate, sourceDbConfig.getUrl());

        try {
            LocalDate start = LocalDate.parse(startDate);
            LocalDate end = LocalDate.parse(endDate);

            List<Future<MigrationResult>> futures = new ArrayList<>();

            // 按日期范围分批处理
            LocalDate currentDate = start;
            while (!currentDate.isAfter(end)) {
                LocalDate batchEndDate = currentDate.plusDays(batchDays - 1);
                if (batchEndDate.isAfter(end)) {
                    batchEndDate = end;
                }

                final String batchStartStr = currentDate.format(DateTimeFormatter.ISO_LOCAL_DATE);
                final String batchEndStr = batchEndDate.format(DateTimeFormatter.ISO_LOCAL_DATE);

                Future<MigrationResult> future = executorService.submit(() ->
                    migrateDataByDateBatch(planCode, batchStartStr, batchEndStr, sourceDbConfig));
                futures.add(future);

                currentDate = batchEndDate.plusDays(1);
            }

            // 等待所有任务完成并统计结果
            int totalMigrated = 0;
            long totalReadTime = 0;
            long totalWriteTime = 0;

            for (Future<MigrationResult> future : futures) {
                MigrationResult result = future.get();
                totalMigrated += result.recordCount;
                totalReadTime += result.readTime;
                totalWriteTime += result.writeTime;
            }

            long endTime = System.currentTimeMillis();
            long totalTime = endTime - startTime;

            logger.info("=== Date-based Migration Summary ===");
            logger.info("Source Database: {}", sourceDbConfig.getUrl());
            logger.info("Date Range: {} to {}", startDate, endDate);
            logger.info("Batch Days: {}", batchDays);
            logger.info("Total migrated records: {}", totalMigrated);
            logger.info("Total time: {} ms", totalTime);
            logger.info("Total read time: {} ms", totalReadTime);
            logger.info("Total write time: {} ms", totalWriteTime);
            String formattedAvgReadTime = String.format("%.2f",
                       totalMigrated > 0 ? (double)totalReadTime / totalMigrated : 0);
            logger.info("Average read time per record: {} ms", formattedAvgReadTime);
            String formattedAvgWriteTime = String.format("%.2f",
                       totalMigrated > 0 ? (double)totalWriteTime / totalMigrated : 0);
            logger.info("Average write time per record: {} ms", formattedAvgWriteTime);
            String formattedThroughput = String.format("%.2f",
                       totalTime > 0 ? (double)totalMigrated * 1000 / totalTime : 0);
            logger.info("Throughput: {} records/second", formattedThroughput);

        } catch (Exception e) {
            logger.error("Error during date-based data migration from database: {}", sourceDbConfig.getUrl(), e);
        } finally {
            executorService.shutdown();
        }
    }
    
    private List<String> getDistinctPortCodes(String planCode) throws SQLException {
        return getDistinctPortCodes(planCode, DatabaseConfig.getDefault());
    }

    private List<String> getDistinctPortCodes(String planCode, DatabaseConfig dbConfig) throws SQLException {
        String sql = "select * from t_p_ab_group_rela t where t.c_group_code = ?";
        List<String> portCodes = new ArrayList<>();

        try (Connection conn = DatabaseUtil.getConnection(dbConfig.getUrl(), dbConfig.getUsername(), dbConfig.getPassword(), dbConfig.getDatabaseType());
             PreparedStatement stmt = conn.prepareStatement(sql)){
             stmt.setString(1, planCode);
             try(ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    portCodes.add(rs.getString(groupByColumnName));
                }
            }
        }

        return portCodes;
    }

    /**
     * 基于日期批次进行数据迁移
     * @param planCode 计划代码
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param sourceDbConfig 源数据库配置
     * @return 迁移结果
     */
    private MigrationResult migrateDataByDateBatch(String planCode, String startDate, String endDate, DatabaseConfig sourceDbConfig) {
        long batchStartTime = System.currentTimeMillis();
        logger.info("Thread {} starting date batch migration from {} to {} from database: {}",
                   Thread.currentThread().getName(), startDate, endDate, sourceDbConfig.getUrl());

        try {
            // 获取该日期范围内的所有分组值
            List<String> groupValues = getDistinctGroupValuesByDateRange(planCode, startDate, endDate, sourceDbConfig);
            logger.info("Date batch {} to {} has {} group values to migrate", startDate, endDate, groupValues.size());

            int totalMigrated = 0;
            long totalReadTime = 0;
            long totalWriteTime = 0;

            // 为每个分组值处理该日期范围的数据
            for (String groupValue : groupValues) {
                BatchResult batchResult = processBatchByDateRange(groupValue, startDate, endDate, sourceDbConfig);
                totalMigrated += batchResult.recordCount;
                totalReadTime += batchResult.readTime;
                totalWriteTime += batchResult.writeTime;
            }

            long batchEndTime = System.currentTimeMillis();
            long batchTotalTime = batchEndTime - batchStartTime;

            logger.info("Thread {} completed date batch migration from {} to {}",
                       Thread.currentThread().getName(), startDate, endDate);
            logger.info("  - Records migrated: {}", totalMigrated);
            logger.info("  - Total time: {} ms", batchTotalTime);
            logger.info("  - Read time: {} ms", totalReadTime);
            logger.info("  - Write time: {} ms", totalWriteTime);
            String formattedBatchThroughput = String.format("%.2f",
                       batchTotalTime > 0 ? (double)totalMigrated * 1000 / batchTotalTime : 0);
            logger.info("  - Throughput: {} records/second", formattedBatchThroughput);

            return new MigrationResult(totalMigrated, totalReadTime, totalWriteTime);

        } catch (Exception e) {
            logger.error("Error in date batch migration from {} to {} from database: {}",
                        startDate, endDate, sourceDbConfig.getUrl(), e);
            return new MigrationResult(0, 0, 0);
        }
    }

    /**
     * 获取指定日期范围内的所有分组值
     * @param planCode 计划代码
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param dbConfig 数据库配置
     * @return 分组值列表
     */
    private List<String> getDistinctGroupValuesByDateRange(String planCode, String startDate, String endDate, DatabaseConfig dbConfig) throws SQLException {
        // String sql = "SELECT DISTINCT " + groupByColumnName + " FROM t_p_ab_group_rela t " +
        //             "WHERE t.c_group_code = ? AND EXISTS (" +
        //             "SELECT 1 FROM " + sourceTable + " s " +
        //             "WHERE s." + groupByColumnName + " = t." + groupByColumnName + " " +
        //             "AND s." + dateColumnName + " >= ? AND s." + dateColumnName + " <= ?)";

        String sql = "select distinct t.f_code from gtja_jxfx_dw_cchz_byday t limit 10";

        List<String> groupValues = new ArrayList<>();

        try (Connection conn = DatabaseUtil.getConnection(dbConfig.getUrl(), dbConfig.getUsername(), dbConfig.getPassword(), dbConfig.getDatabaseType());
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            // stmt.setString(1, planCode);
            // stmt.setString(2, startDate);
            // stmt.setString(3, endDate);

            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    groupValues.add(rs.getString(groupByColumnName));
                }
            }
        }

        logger.debug("Found {} group values for date range {} to {}", groupValues.size(), startDate, endDate);
        return groupValues;
    }

    /**
     * 处理指定分组值和日期范围的数据批次
     * @param groupValue 分组值
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param sourceDbConfig 源数据库配置
     * @return 批次处理结果
     */
    private BatchResult processBatchByDateRange(String groupValue, String startDate, String endDate, DatabaseConfig sourceDbConfig) {
        String selectSql;
        String insertSql;

        try {
            selectSql = buildSelectSqlByDateRange(groupValue, startDate, endDate, sourceDbConfig);
            insertSql = buildInsertSql();
        } catch (SQLException e) {
            logger.error("Failed to build dynamic SQL for date range", e);
            throw new RuntimeException("Failed to build SQL statements", e);
        }

        long readStartTime = System.currentTimeMillis();
        long writeStartTime = 0;
        long writeEndTime = 0;

        // 使用源数据库配置进行读取，使用目标数据库配置进行写入
        try (Connection sourceConn = DatabaseUtil.getConnection(sourceDbConfig.getUrl(), sourceDbConfig.getUsername(), sourceDbConfig.getPassword(), sourceDbConfig.getDatabaseType());
             Connection targetConn = DatabaseUtil.getConnection(targetDbConfig.getUrl(), targetDbConfig.getUsername(), targetDbConfig.getPassword(), targetDbConfig.getDatabaseType());
             PreparedStatement selectStmt = sourceConn.prepareStatement(selectSql);
             PreparedStatement insertStmt = targetConn.prepareStatement(insertSql)) {

            targetConn.setAutoCommit(false);

            // 设置查询参数
            selectStmt.setString(1, groupValue);

            // 获取日期字段类型信息并正确设置日期参数
            ColumnInfo dateColumnInfo = null;
            try {
                dateColumnInfo = getColumnInfo(dateColumnName, sourceDbConfig);
            } catch (Exception e) {
                logger.warn("Failed to get date column info for '{}', using string type: {}", dateColumnName, e.getMessage());
            }

            // 设置开始日期参数
            setDateParameter(selectStmt, 2, startDate, dateColumnInfo, sourceDbConfig);

            // 设置结束日期参数
            setDateParameter(selectStmt, 3, endDate, dateColumnInfo, sourceDbConfig);

            logger.debug("Reading date range batch for {}: {} from {} to {} from database: {}",
                        groupByColumnName, groupValue, startDate, endDate, sourceDbConfig.getUrl());

            ResultSet rs = selectStmt.executeQuery();
            long readEndTime = System.currentTimeMillis();
            long readTime = readEndTime - readStartTime;

            // 准备批量插入
            writeStartTime = System.currentTimeMillis();
            int batchCount = 0;

            while (rs.next()) {
                setInsertParameters(insertStmt, rs);
                insertStmt.addBatch();
                batchCount++;
            }

            // 执行批量插入
            if (batchCount > 0) {
                logger.debug("Writing date range batch for {}: {}, records: {}", groupByColumnName, groupValue, batchCount);
                // GaussdbTracer tracer = GaussdbTracer.getInstance();
                // String traceId = UUID.randomUUID().toString().replaceAll("-","" );
                // tracer.setTraceId(traceId);
                int[] results = insertStmt.executeBatch();
                targetConn.commit();
                // tracer.reset();
                writeEndTime = System.currentTimeMillis();
                long writeTime = writeEndTime - writeStartTime;

                int successCount = 0;
                for (int result : results) {
                    if (result > 0) successCount++;
                }

                logger.debug("Date range batch completed for {}: {} - Read: {} ms, Write: {} ms, Records: {}",
                           groupByColumnName, groupValue, readTime, writeTime, successCount);

                return new BatchResult(successCount, readTime, writeTime);
            }

            writeEndTime = System.currentTimeMillis();
            return new BatchResult(0, readTime, writeEndTime - writeStartTime);

        } catch (SQLException e) {
            logger.error("Error processing date range batch for {}: {} from {} to {} from database: {}",
                        groupByColumnName, groupValue, startDate, endDate, sourceDbConfig.getUrl(), e);
            return new BatchResult(0, 0, 0);
        }
    }

    /**
     * 从元数据视图获取表的列信息（使用配置文件中的元数据视图配置）
     * @param tableName 表名
     * @param dbConfig 数据库配置
     * @return 列信息列表
     */
    private List<ColumnInfo> getTableColumns(String tableName, DatabaseConfig dbConfig) throws SQLException {
        // 从配置文件获取元数据视图配置
        com.example.config.DatabaseConnectionConfig.MetadataConfig metaConfig =
            com.example.config.DatabaseConnectionConfig.getMetadataConfig(dbConfig.getDatabaseType());

        String sql = String.format("SELECT %s, %s, %s FROM %s WHERE UPPER(%s) = ? ORDER BY %s",
                                  metaConfig.getColumnNameColumn(),
                                  metaConfig.getDataTypeColumn(),
                                  metaConfig.getOrdinalPositionColumn(),
                                  metaConfig.getFullViewName(),
                                  metaConfig.getTableNameColumn(),
                                  metaConfig.getOrdinalPositionColumn());

        List<ColumnInfo> columns = new ArrayList<>();

        try (Connection conn = DatabaseUtil.getConnection(dbConfig.getUrl(), dbConfig.getUsername(), dbConfig.getPassword(), dbConfig.getDatabaseType());
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, tableName.toUpperCase()); // 表名转大写

            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    String columnName = rs.getString(metaConfig.getColumnNameColumn());
                    String dataType = rs.getString(metaConfig.getDataTypeColumn());
                    int ordinalPosition = rs.getInt(metaConfig.getOrdinalPositionColumn());

                    columns.add(new ColumnInfo(columnName, dataType, ordinalPosition));
                }
            }
        }

        logger.debug("Retrieved {} columns for table {} from metadata view {}",
                    columns.size(), tableName, metaConfig.getFullViewName());
        return columns;
    }

    /**
     * 从元数据视图获取表的列信息（兼容旧版本，使用指定的元数据视图名）
     * @param tableName 表名
     * @param dbConfig 数据库配置
     * @param metadataViewName 元数据视图名
     * @return 列信息列表
     */
    @Deprecated
    private List<ColumnInfo> getTableColumns(String tableName, DatabaseConfig dbConfig, String metadataViewName) throws SQLException {
        String sql = "SELECT column_name, data_type, ordinal_position " +
                    "FROM " + metadataViewName + " " +
                    "WHERE table_name = ? " +
                    "ORDER BY ordinal_position";

        List<ColumnInfo> columns = new ArrayList<>();

        try (Connection conn = DatabaseUtil.getConnection(dbConfig.getUrl(), dbConfig.getUsername(), dbConfig.getPassword(), dbConfig.getDatabaseType());
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, tableName.toUpperCase()); // 通常数据库中表名是大写

            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    String columnName = rs.getString("column_name");
                    String dataType = rs.getString("data_type");
                    int ordinalPosition = rs.getInt("ordinal_position");

                    columns.add(new ColumnInfo(columnName, dataType, ordinalPosition));
                }
            }
        }

        logger.debug("Retrieved {} columns for table {} from metadata view {}", columns.size(), tableName, metadataViewName);
        return columns;
    }

    /**
     * 获取源表列信息（带缓存）
     */
    private List<ColumnInfo> getSourceTableColumns(DatabaseConfig sourceDbConfig) throws SQLException {
        if (sourceTableColumns == null) {
            synchronized (this) {
                if (sourceTableColumns == null) {
                    sourceTableColumns = getTableColumns(sourceTable, sourceDbConfig);
                }
            }
        }
        return sourceTableColumns;
    }

    /**
     * 获取指定字段的类型信息
     */
    private ColumnInfo getColumnInfo(String columnName, DatabaseConfig dbConfig) throws SQLException {
        List<ColumnInfo> columns = getTableColumns(sourceTable, dbConfig);
        for (ColumnInfo column : columns) {
            if (column.getColumnName().equalsIgnoreCase(columnName)) {
                return column;
            }
        }
        return null;
    }

    /**
     * 根据字段类型设置日期参数
     */
    private void setDateParameter(PreparedStatement stmt, int parameterIndex, String dateValue,
                                 ColumnInfo dateColumnInfo, DatabaseConfig dbConfig) throws SQLException {
        if (dateColumnInfo == null) {
            // 如果无法获取字段信息，默认使用字符串类型
            stmt.setString(parameterIndex, dateValue);
            logger.debug("Setting date parameter as string (no column info): {}", dateValue);
            return;
        }

        String dataType = dateColumnInfo.getDataType().toUpperCase();
        logger.debug("Setting date parameter for column '{}' with type '{}': {}",
                    dateColumnInfo.getColumnName(), dataType, dateValue);

        try {
            if (dateColumnInfo.isDateTimeType()) {
                // 日期时间类型
                if (dataType.contains("TIMESTAMP")) {
                    // TIMESTAMP类型
                    java.sql.Timestamp timestamp = java.sql.Timestamp.valueOf(dateValue + " 00:00:00");
                    stmt.setTimestamp(parameterIndex, timestamp);
                    logger.debug("Set as TIMESTAMP: {}", timestamp);
                } else if (dataType.contains("DATE")) {
                    // DATE类型
                    java.sql.Date date = java.sql.Date.valueOf(dateValue);
                    stmt.setDate(parameterIndex, date);
                    logger.debug("Set as DATE: {}", date);
                } else {
                    // 其他时间类型，尝试作为字符串
                    stmt.setString(parameterIndex, dateValue);
                    logger.debug("Set as STRING (datetime): {}", dateValue);
                }
            } else if (dateColumnInfo.isStringType()) {
                // 字符串类型
                stmt.setString(parameterIndex, dateValue);
                logger.debug("Set as STRING: {}", dateValue);
            } else if (dateColumnInfo.isNumericType()) {
                // 数值类型（可能是时间戳）
                try {
                    long numericValue = Long.parseLong(dateValue.replaceAll("-", ""));
                    stmt.setLong(parameterIndex, numericValue);
                    logger.debug("Set as NUMERIC: {}", numericValue);
                } catch (NumberFormatException e) {
                    // 如果无法转换为数值，使用字符串
                    stmt.setString(parameterIndex, dateValue);
                    logger.debug("Set as STRING (numeric conversion failed): {}", dateValue);
                }
            } else {
                // 未知类型，默认使用字符串
                stmt.setString(parameterIndex, dateValue);
                logger.debug("Set as STRING (unknown type): {}", dateValue);
            }
        } catch (Exception e) {
            // 如果类型转换失败，回退到字符串类型
            logger.warn("Failed to set date parameter with specific type, falling back to string: {}", e.getMessage());
            stmt.setString(parameterIndex, dateValue);
        }
    }

    /**
     * 获取目标表列信息（带缓存）
     */
    private List<ColumnInfo> getTargetTableColumns() throws SQLException {
        if (targetTableColumns == null) {
            synchronized (this) {
                if (targetTableColumns == null) {
                    targetTableColumns = getTableColumns(targetTable, targetDbConfig);
                }
            }
        }
        return targetTableColumns;
    }

    private MigrationResult migrateDataByPortCode(String portCode) {
        return migrateDataByPortCode(portCode, DatabaseConfig.getDefault());
    }

    private MigrationResult migrateDataByPortCode(String portCode, DatabaseConfig sourceDbConfig) {
        long threadStartTime = System.currentTimeMillis();
        logger.info("Thread {} starting migration for port_code: {} from database: {}",
                   Thread.currentThread().getName(), portCode, sourceDbConfig.getUrl());

        long totalRecords = getTotalRecordsByPortCode(portCode, sourceDbConfig);
        logger.info("Port code {} has {} records to migrate", portCode, totalRecords);

        int totalMigrated = 0;
        long totalReadTime = 0;
        long totalWriteTime = 0;

        // 分批处理该port_code的数据
        for (long offset = 0; offset < totalRecords; offset += batchSize) {
            long currentLimit = Math.min(batchSize, totalRecords - offset);
            BatchResult batchResult = processBatchByPortCode(portCode, offset, currentLimit, sourceDbConfig);
            totalMigrated += batchResult.recordCount;
            totalReadTime += batchResult.readTime;
            totalWriteTime += batchResult.writeTime;
        }

        long threadEndTime = System.currentTimeMillis();
        long threadTotalTime = threadEndTime - threadStartTime;

        logger.info("Thread {} completed migration for port_code: {}",
                   Thread.currentThread().getName(), portCode);
        logger.info("  - Records migrated: {}", totalMigrated);
        logger.info("  - Total time: {} ms", threadTotalTime);
        logger.info("  - Read time: {} ms", totalReadTime);
        logger.info("  - Write time: {} ms", totalWriteTime);
        String formattedThreadThroughput = String.format("%.2f",
                   threadTotalTime > 0 ? (double)totalMigrated * 1000 / threadTotalTime : 0);
        logger.info("  - Throughput: {} records/second", formattedThreadThroughput);

        return new MigrationResult(totalMigrated, totalReadTime, totalWriteTime);
    }
    
    private long getTotalRecordsByPortCode(String portCode) {
        return getTotalRecordsByPortCode(portCode, DatabaseConfig.getDefault());
    }

    private long getTotalRecordsByPortCode(String portCode, DatabaseConfig sourceDbConfig) {
        String sql = "SELECT COUNT(*) FROM " + sourceTable + " WHERE " + groupByColumnName + " = ?";
        try (Connection conn = DatabaseUtil.getConnection(sourceDbConfig.getUrl(), sourceDbConfig.getUsername(), sourceDbConfig.getPassword(), sourceDbConfig.getDatabaseType());
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, portCode);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getLong(1);
                }
            }
        } catch (SQLException e) {
            logger.error("Error getting total records for {}: {} from database: {}", groupByColumnName, portCode, sourceDbConfig.getUrl(), e);
        }
        return 0;
    }
    
    private BatchResult processBatchByPortCode(String portCode, long offset, long limit) {
        return processBatchByPortCode(portCode, offset, limit, DatabaseConfig.getDefault());
    }

    private BatchResult processBatchByPortCode(String portCode, long offset, long limit, DatabaseConfig sourceDbConfig) {
        String selectSql;
        String insertSql;

        try {
            selectSql = buildSelectSqlByPortCode(offset, limit, sourceDbConfig);
            insertSql = buildInsertSql();
        } catch (SQLException e) {
            logger.error("Failed to build dynamic SQL", e);
            throw new RuntimeException("Failed to build SQL statements", e);
        }

        long readStartTime = System.currentTimeMillis();
        long writeStartTime = 0;
        long writeEndTime = 0;

        // 使用源数据库配置进行读取，使用目标数据库配置进行写入
        try (Connection sourceConn = DatabaseUtil.getConnection(sourceDbConfig.getUrl(), sourceDbConfig.getUsername(), sourceDbConfig.getPassword(), sourceDbConfig.getDatabaseType());
             Connection targetConn = DatabaseUtil.getConnection(targetDbConfig.getUrl(), targetDbConfig.getUsername(), targetDbConfig.getPassword(), targetDbConfig.getDatabaseType());
             PreparedStatement selectStmt = sourceConn.prepareStatement(selectSql);
             PreparedStatement insertStmt = targetConn.prepareStatement(insertSql)) {

            targetConn.setAutoCommit(false);

            // 设置查询参数并执行查询
            selectStmt.setString(1, portCode);

            logger.debug("Reading batch for port_code: {}, offset: {}, limit: {} from database: {}",
                        portCode, offset, limit, sourceDbConfig.getUrl());

            ResultSet rs = selectStmt.executeQuery();
            long readEndTime = System.currentTimeMillis();
            long readTime = readEndTime - readStartTime;

            // 准备批量插入
            writeStartTime = System.currentTimeMillis();
            int batchCount = 0;

            while (rs.next()) {
                setInsertParameters(insertStmt, rs);
                insertStmt.addBatch();
                batchCount++;
            }

            // 执行批量插入
            if (batchCount > 0) {
                logger.debug("Writing batch for port_code: {}, records: {}", portCode, batchCount);
                // GaussdbTracer tracer = GaussdbTracer.getInstance();
                // String traceId = UUID.randomUUID().toString().replaceAll("-","" );
                // tracer.setTraceId(traceId);
                int[] results = insertStmt.executeBatch();
                targetConn.commit();
                // tracer.reset();
                writeEndTime = System.currentTimeMillis();
                long writeTime = writeEndTime - writeStartTime;

                int successCount = 0;
                for (int result : results) {
                    if (result > 0) successCount++;
                }

                logger.debug("Batch completed for port_code: {} - Read: {} ms, Write: {} ms, Records: {}",
                           portCode, readTime, writeTime, successCount);

                return new BatchResult(successCount, readTime, writeTime);
            }

            writeEndTime = System.currentTimeMillis();
            return new BatchResult(0, readTime, writeEndTime - writeStartTime);

        } catch (SQLException e) {
            logger.error("Error processing batch for port_code: {} at offset {} from database: {}",
                        portCode, offset, sourceDbConfig.getUrl(), e);
            return new BatchResult(0, 0, 0);
        }
    }
    
    private String buildSelectSqlByPortCode(long offset, long limit, DatabaseConfig sourceDbConfig) throws SQLException {
        List<ColumnInfo> columns = getSourceTableColumns(sourceDbConfig);

        StringBuilder columnList = new StringBuilder();
        for (int i = 0; i < columns.size(); i++) {
            if (i > 0) {
                columnList.append(", ");
            }
            columnList.append(columns.get(i).getColumnName());
        }

        // 找到主键列用于排序（假设第一个列是主键，或者查找名为 'id' 开头的列）
        String orderByColumn = "1"; // 默认按第一列排序
        for (ColumnInfo column : columns) {
            if (column.getColumnName().toLowerCase().startsWith("id") ||
                column.getColumnName().toLowerCase().contains("_id")) {
                orderByColumn = column.getColumnName();
                break;
            }
        }

        return "SELECT " + columnList.toString() +
               " FROM " + sourceTable +
               " WHERE " + groupByColumnName + " = ? " +
               " ORDER BY " + orderByColumn +
               " LIMIT " + limit + " OFFSET " + offset;
    }



    /**
     * 构建基于日期范围的SELECT SQL
     */
    private String buildSelectSqlByDateRange(String groupValue, String startDate, String endDate, DatabaseConfig sourceDbConfig) throws SQLException {
        List<ColumnInfo> columns = getSourceTableColumns(sourceDbConfig);

        StringBuilder columnList = new StringBuilder();
        for (int i = 0; i < columns.size(); i++) {
            if (i > 0) {
                columnList.append(", ");
            }
            columnList.append(columns.get(i).getColumnName());
        }

        return "SELECT " + columnList.toString() +
               " FROM " + sourceTable +
               " WHERE " + groupByColumnName + " = ? " +
               " AND " + dateColumnName + " >= ? " +
               " AND " + dateColumnName + " <= ?";
    }



    private String buildInsertSql() throws SQLException {
        List<ColumnInfo> columns = getTargetTableColumns();

        StringBuilder columnList = new StringBuilder();
        StringBuilder valuesList = new StringBuilder();

        for (int i = 0; i < columns.size(); i++) {
            if (i > 0) {
                columnList.append(", ");
                valuesList.append(", ");
            }
            columnList.append(columns.get(i).getColumnName());
            valuesList.append("?");
        }

        return "INSERT INTO " + targetTable + " (" + columnList.toString() + ") VALUES (" + valuesList.toString() + ")";
    }


    
    private void setInsertParameters(PreparedStatement stmt, ResultSet rs) throws SQLException {
        try {
            // 尝试使用动态方式设置参数
            setInsertParametersDynamic(stmt, rs);
        } catch (SQLException e) {
            logger.warn("Failed to set parameters dynamically, falling back to static method", e);
            // 回退到静态方式
            setInsertParametersStatic(stmt, rs);
        }
    }

    /**
     * 动态设置插入参数
     */
    private void setInsertParametersDynamic(PreparedStatement stmt, ResultSet rs) throws SQLException {
        List<ColumnInfo> columns = getTargetTableColumns();

        for (int i = 0; i < columns.size(); i++) {
            ColumnInfo column = columns.get(i);
            String columnName = column.getColumnName();
            String dataType = column.getDataType().toUpperCase();
            int paramIndex = i + 1;

            // 根据数据类型设置参数
            if (dataType.contains("VARCHAR") || dataType.contains("CHAR") || dataType.contains("TEXT")) {
                stmt.setString(paramIndex, rs.getString(columnName));
            } else if (dataType.contains("TIMESTAMP") || dataType.contains("DATETIME")) {
                stmt.setTimestamp(paramIndex, rs.getTimestamp(columnName));
            } else if (dataType.contains("DATE")) {
                stmt.setDate(paramIndex, rs.getDate(columnName));
            } else if (dataType.contains("DECIMAL") || dataType.contains("NUMERIC")) {
                stmt.setBigDecimal(paramIndex, rs.getBigDecimal(columnName));
            } else if (dataType.contains("INT")) {
                stmt.setInt(paramIndex, rs.getInt(columnName));
            } else if (dataType.contains("BIGINT")) {
                stmt.setLong(paramIndex, rs.getLong(columnName));
            } else if (dataType.contains("FLOAT") || dataType.contains("DOUBLE")) {
                stmt.setDouble(paramIndex, rs.getDouble(columnName));
            } else {
                // 默认作为字符串处理
                stmt.setString(paramIndex, rs.getString(columnName));
            }
        }
    }

    /**
     * 静态设置插入参数（向后兼容）
     */
    private void setInsertParametersStatic(PreparedStatement stmt, ResultSet rs) throws SQLException {
        stmt.setString(1, rs.getString("id_d_os_trade"));
        stmt.setString(2, rs.getString("c_port_code"));
        stmt.setString(3, rs.getString("c_cfg_code"));
        stmt.setTimestamp(4, rs.getTimestamp("d_date"));
        stmt.setTimestamp(5, rs.getTimestamp("d_trade"));
        stmt.setString(6, rs.getString("c_dt_type"));
        stmt.setString(7, rs.getString("c_sh_acc_code"));
        stmt.setString(8, rs.getString("c_sec_mkt_code"));
        stmt.setString(9, rs.getString("c_sec_code"));
        stmt.setString(10, rs.getString("c_dc_code"));
        stmt.setString(11, rs.getString("c_sec_code_old"));
        stmt.setString(12, rs.getString("c_mkt_code"));
        stmt.setString(13, rs.getString("c_td_type"));
        stmt.setString(14, rs.getString("c_td_chan_code"));
        stmt.setBigDecimal(15, rs.getBigDecimal("n_td_amount"));
        stmt.setBigDecimal(16, rs.getBigDecimal("n_td_price"));
        stmt.setBigDecimal(17, rs.getBigDecimal("n_td_money"));
        stmt.setString(18, rs.getString("c_app_no"));
        stmt.setString(19, rs.getString("c_sec_var_code"));
        stmt.setString(20, rs.getString("c_dta_code"));
        stmt.setString(21, rs.getString("c_dv_issue_mode"));
        stmt.setString(22, rs.getString("c_dv_invest_cls"));
        stmt.setString(23, rs.getString("c_aux_sign"));
        stmt.setTimestamp(24, rs.getTimestamp("d_sett"));
        stmt.setBigDecimal(25, rs.getBigDecimal("n_sett_money_t"));
        stmt.setString(26, rs.getString("c_dc_code_sett"));
        stmt.setBigDecimal(27, rs.getBigDecimal("n_sett_money_s"));
        stmt.setBigDecimal(28, rs.getBigDecimal("n_rate"));
        stmt.setString(29, rs.getString("c_td_no"));
        stmt.setBigDecimal(30, rs.getBigDecimal("n_jyfy"));
        stmt.setBigDecimal(31, rs.getBigDecimal("n_yj"));
        stmt.setBigDecimal(32, rs.getBigDecimal("n_lx"));
        stmt.setString(33, rs.getString("c_path"));
        stmt.setString(34, rs.getString("c_dv_qut_mod"));
        stmt.setString(35, rs.getString("c_dv_plat"));
        stmt.setString(36, rs.getString("c_da_code"));
        stmt.setString(37, rs.getString("c_dv_type_sub"));
        stmt.setTimestamp(38, rs.getTimestamp("d_sett_fact"));
        stmt.setString(39, rs.getString("c_busi_type"));
        stmt.setBigDecimal(40, rs.getBigDecimal("n_yhs"));
        stmt.setBigDecimal(41, rs.getBigDecimal("n_ghf"));
        stmt.setBigDecimal(42, rs.getBigDecimal("n_jsof"));
        stmt.setBigDecimal(43, rs.getBigDecimal("n_jsuf"));
        stmt.setBigDecimal(44, rs.getBigDecimal("n_fxj"));
        stmt.setBigDecimal(45, rs.getBigDecimal("n_zgf"));
        stmt.setBigDecimal(46, rs.getBigDecimal("n_qtf"));
        stmt.setBigDecimal(47, rs.getBigDecimal("n_dc_rate"));
        stmt.setBigDecimal(48, rs.getBigDecimal("n_jyj"));
        stmt.setString(49, rs.getString("id_d_ac_td_ivt"));
        stmt.setString(50, rs.getString("c_isenable"));
        stmt.setString(51, rs.getString("c_day_year"));
        stmt.setString(52, rs.getString("c_time"));
        stmt.setString(53, rs.getString("c_iden"));
    }
    
    // 内部类：批次处理结果
    private static class BatchResult {
        final int recordCount;
        final long readTime;
        final long writeTime;
        
        BatchResult(int recordCount, long readTime, long writeTime) {
            this.recordCount = recordCount;
            this.readTime = readTime;
            this.writeTime = writeTime;
        }
    }

    // 内部类：迁移结果
    private static class MigrationResult {
        final int recordCount;
        final long readTime;
        final long writeTime;
        
        MigrationResult(int recordCount, long readTime, long writeTime) {
            this.recordCount = recordCount;
            this.readTime = readTime;
            this.writeTime = writeTime;
        }
    }
    
    /**
     * 命令行参数解析结果
     */
    private static class CommandLineArgs {
        String mode = "migrate";           // 运行模式：migrate, date-migrate, config, help
        String sourceTable;                // 源表名
        String targetTable;                // 目标表名
        String planCode;                   // 计划代码
        int batchSize = 2000;             // 批次大小
        int threadPoolSize = 4;           // 线程池大小
        String sourceDbConfig = "default"; // 源数据库配置名
        String targetDbConfig = "default"; // 目标数据库配置名
        String dateColumn;                 // 日期字段名
        int batchDays = 1;                // 日期分批天数
        String startDate;                  // 开始日期
        String endDate;                    // 结束日期
        String groupByColumn = "c_port_code"; // 分组字段名
        boolean showConfig = false;        // 是否显示配置
        boolean showHelp = false;          // 是否显示帮助
    }

    public static void main(String[] args) {
        try {
            CommandLineArgs cmdArgs = parseCommandLineArgs(args);

            if (cmdArgs.showHelp) {
                printUsage();
                return;
            }

            if (cmdArgs.showConfig) {
                printConfiguration();
                return;
            }

            executeCommand(cmdArgs);

        } catch (Exception e) {
            logger.error("Application failed", e);
            System.err.println("Error: " + e.getMessage());
            System.exit(1);
        } finally {
            // 关闭连接池
            DatabaseUtil.shutdown();
        }
    }

    /**
     * 解析命令行参数
     */
    private static CommandLineArgs parseCommandLineArgs(String[] args) {
        CommandLineArgs cmdArgs = new CommandLineArgs();

        if (args.length == 0) {
            cmdArgs.showHelp = true;
            return cmdArgs;
        }

        // 解析命令行参数
        for (int i = 0; i < args.length; i++) {
            String arg = args[i];

            switch (arg) {
                case "--help":
                case "-h":
                    cmdArgs.showHelp = true;
                    return cmdArgs;

                case "--config":
                case "-c":
                    cmdArgs.showConfig = true;
                    return cmdArgs;

                case "--mode":
                case "-m":
                    if (i + 1 < args.length) {
                        cmdArgs.mode = args[++i];
                    }
                    break;

                case "--source-table":
                case "-st":
                    if (i + 1 < args.length) {
                        cmdArgs.sourceTable = args[++i];
                    }
                    break;

                case "--target-table":
                case "-tt":
                    if (i + 1 < args.length) {
                        cmdArgs.targetTable = args[++i];
                    }
                    break;

                case "--plan-code":
                case "-p":
                    if (i + 1 < args.length) {
                        cmdArgs.planCode = args[++i];
                    }
                    break;

                case "--batch-size":
                case "-bs":
                    if (i + 1 < args.length) {
                        cmdArgs.batchSize = Integer.parseInt(args[++i]);
                    }
                    break;

                case "--thread-pool":
                case "-tp":
                    if (i + 1 < args.length) {
                        cmdArgs.threadPoolSize = Integer.parseInt(args[++i]);
                    }
                    break;

                case "--source-db":
                case "-sdb":
                    if (i + 1 < args.length) {
                        cmdArgs.sourceDbConfig = args[++i];
                    }
                    break;

                case "--target-db":
                case "-tdb":
                    if (i + 1 < args.length) {
                        cmdArgs.targetDbConfig = args[++i];
                    }
                    break;

                case "--date-column":
                case "-dc":
                    if (i + 1 < args.length) {
                        cmdArgs.dateColumn = args[++i];
                    }
                    break;

                case "--batch-days":
                case "-bd":
                    if (i + 1 < args.length) {
                        cmdArgs.batchDays = Integer.parseInt(args[++i]);
                    }
                    break;

                case "--start-date":
                case "-sd":
                    if (i + 1 < args.length) {
                        cmdArgs.startDate = args[++i];
                    }
                    break;

                case "--end-date":
                case "-ed":
                    if (i + 1 < args.length) {
                        cmdArgs.endDate = args[++i];
                    }
                    break;

                case "--group-by":
                case "-gb":
                    if (i + 1 < args.length) {
                        cmdArgs.groupByColumn = args[++i];
                    }
                    break;

                default:
                    // 兼容旧版本的位置参数
                    if (i == 0 && cmdArgs.sourceTable == null) {
                        cmdArgs.sourceTable = arg;
                    } else if (i == 1 && cmdArgs.targetTable == null) {
                        cmdArgs.targetTable = arg;
                    } else if (i == 2 && cmdArgs.planCode == null) {
                        cmdArgs.planCode = arg;
                    } else if (i == 3) {
                        try {
                            cmdArgs.batchSize = Integer.parseInt(arg);
                        } catch (NumberFormatException e) {
                            // 忽略无效的数字参数
                        }
                    } else if (i == 4) {
                        try {
                            cmdArgs.threadPoolSize = Integer.parseInt(arg);
                        } catch (NumberFormatException e) {
                            // 忽略无效的数字参数
                        }
                    }
                    break;
            }
        }

        return cmdArgs;
    }

    /**
     * 执行命令
     */
    private static void executeCommand(CommandLineArgs cmdArgs) {
        // 验证必需参数
        if (cmdArgs.sourceTable == null || cmdArgs.targetTable == null || cmdArgs.planCode == null) {
            throw new IllegalArgumentException("Missing required parameters: sourceTable, targetTable, planCode");
        }

        logger.info("Starting data migration with mode: {}", cmdArgs.mode);
        logger.info("Source table: {}, Target table: {}, Plan code: {}",
                   cmdArgs.sourceTable, cmdArgs.targetTable, cmdArgs.planCode);

        // 获取数据库配置
        DatabaseConfig sourceDbConfig = getDatabaseConfigByName(cmdArgs.sourceDbConfig);
        DatabaseConfig targetDbConfig = getDatabaseConfigByName(cmdArgs.targetDbConfig);
        logger.info("Using source database config: {}", sourceDbConfig);
        logger.info("Using target database config: {}", targetDbConfig);

        DataMigrationService service;

        switch (cmdArgs.mode.toLowerCase()) {
            case "migrate":
                // 传统迁移模式
                service = new DataMigrationService(cmdArgs.sourceTable, cmdArgs.targetTable,
                                                 cmdArgs.batchSize, cmdArgs.threadPoolSize, targetDbConfig);
                service.migrateData(cmdArgs.planCode, sourceDbConfig);
                break;

            case "date-migrate":
                // 日期分批迁移模式
                if (cmdArgs.dateColumn == null || cmdArgs.startDate == null || cmdArgs.endDate == null) {
                    throw new IllegalArgumentException("Date migration mode requires: dateColumn, startDate, endDate");
                }

                service = DataMigrationService.createWithDateBatch(
                    cmdArgs.sourceTable, cmdArgs.targetTable,
                    cmdArgs.batchSize, cmdArgs.threadPoolSize,
                    cmdArgs.groupByColumn, cmdArgs.dateColumn, cmdArgs.batchDays, targetDbConfig);

                service.migrateDataByDateRange(cmdArgs.planCode, cmdArgs.startDate, cmdArgs.endDate, sourceDbConfig);
                break;

            default:
                throw new IllegalArgumentException("Unknown mode: " + cmdArgs.mode + ". Supported modes: migrate, date-migrate");
        }

        logger.info("Migration completed successfully");
    }

    /**
     * 根据配置名称获取数据库配置
     */
    private static DatabaseConfig getDatabaseConfigByName(String configName) {
        switch (configName.toLowerCase()) {
            case "default":
                return DatabaseConfig.getDefault();
            case "source":
                return DatabaseConfig.getSource();
            case "starrocks":
                return DatabaseConfig.getByType(com.example.util.ConnectionPoolManager.DatabaseType.STARROCKS);
            case "mysql":
                return DatabaseConfig.getByType(com.example.util.ConnectionPoolManager.DatabaseType.MYSQL);
            case "postgresql":
                return DatabaseConfig.getByType(com.example.util.ConnectionPoolManager.DatabaseType.POSTGRESQL);
            case "oracle":
                return DatabaseConfig.getByType(com.example.util.ConnectionPoolManager.DatabaseType.ORACLE);
            case "gaussdb":
                return DatabaseConfig.getByType(com.example.util.ConnectionPoolManager.DatabaseType.GAUSSDB);
            default:
                logger.warn("Unknown database config name: {}, using default", configName);
                return DatabaseConfig.getDefault();
        }
    }

    /**
     * 打印使用说明
     */
    private static void printUsage() {
        System.out.println("GaussDB Data Migration Tool");
        System.out.println("============================");
        System.out.println();
        System.out.println("Usage: java -jar gaussdb-data-migration.jar [OPTIONS]");
        System.out.println();
        System.out.println("Required Options:");
        System.out.println("  --source-table, -st <table>     Source table name");
        System.out.println("  --target-table, -tt <table>     Target table name");
        System.out.println("  --plan-code, -p <code>          Plan code for data filtering");
        System.out.println();
        System.out.println("Optional Options:");
        System.out.println("  --mode, -m <mode>               Migration mode: migrate, date-migrate (default: migrate)");
        System.out.println("  --batch-size, -bs <size>        Batch size for processing (default: 2000)");
        System.out.println("  --thread-pool, -tp <size>       Thread pool size (default: 4)");
        System.out.println("  --source-db, -sdb <config>      Source database config name (default: default)");
        System.out.println("  --target-db, -tdb <config>      Target database config name (default: default)");
        System.out.println("  --group-by, -gb <column>        Group by column name (default: c_port_code)");
        System.out.println();
        System.out.println("Date Migration Options:");
        System.out.println("  --date-column, -dc <column>     Date column name for date-based batching");
        System.out.println("  --batch-days, -bd <days>        Number of days per batch (default: 1)");
        System.out.println("  --start-date, -sd <date>        Start date (format: yyyy-MM-dd)");
        System.out.println("  --end-date, -ed <date>          End date (format: yyyy-MM-dd)");
        System.out.println();
        System.out.println("Utility Options:");
        System.out.println("  --config, -c                    Show current configuration");
        System.out.println("  --help, -h                      Show this help message");
        System.out.println();
        System.out.println("Database Config Names:");
        System.out.println("  default, source, gaussdb, starrocks, mysql, postgresql, oracle");
        System.out.println();
        System.out.println("Examples:");
        System.out.println("  # Basic migration");
        System.out.println("  java -jar gaussdb-data-migration.jar -st source_table -tt target_table -p plan001");
        System.out.println();
        System.out.println("  # Migration with custom parameters");
        System.out.println("  java -jar gaussdb-data-migration.jar -st source_table -tt target_table -p plan001 -bs 5000 -tp 8");
        System.out.println();
        System.out.println("  # Date-based migration from StarRocks");
        System.out.println("  java -jar gaussdb-data-migration.jar -m date-migrate -st analytics_table -tt target_table");
        System.out.println("       -p plan001 -sdb starrocks -dc dt -sd 2024-01-01 -ed 2024-01-31 -bd 7");
        System.out.println();
        System.out.println("  # Legacy positional arguments (still supported)");
        System.out.println("  java -jar gaussdb-data-migration.jar source_table target_table plan001 2000 4");
        System.out.println();
        System.out.println("  # Show configuration");
        System.out.println("  java -jar gaussdb-data-migration.jar --config");
    }

    /**
     * 打印当前配置
     */
    private static void printConfiguration() {
        System.out.println("Current Configuration");
        System.out.println("====================");
        System.out.println();

        // 打印连接池配置
        System.out.println("Connection Pool Configuration:");
        DatabaseUtil.printConnectionPoolConfig();
        System.out.println();

        // 打印数据库连接配置
        System.out.println("Database Connection Configuration:");
        com.example.config.DatabaseConnectionConfig.printConfiguration();
        System.out.println();

        // 打印连接池状态
        System.out.println("Connection Pool Status:");
        DatabaseUtil.printPoolStatus();
    }
}




