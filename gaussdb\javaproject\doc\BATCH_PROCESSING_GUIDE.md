# 分批次处理功能说明

## 概述

在压力测试程序中，我们实现了分批次处理功能，将原来的直接插入操作改为通过临时表的分批次处理模式。这种设计模式在处理大量数据时具有显著优势。

## 处理流程对比

### 原始流程
```
测试数据表 → 执行表
```

### 新的分批次流程
```
测试数据表 → 临时表（分批次） → 执行表（批量）
```

## 详细处理步骤

### 1. 数据准备阶段
- 创建三张表：`stress_test_data`（测试数据表）、`stress_temp_data`（临时表）、`stress_execution_data`（执行表）
- 向测试数据表和执行表灌入相同的初始数据

### 2. 并发执行阶段
每个工作线程按以下步骤处理：

1. **DELETE**: 删除执行表中指定`c_port_code`的数据
2. **流式分批次处理**:
   - 清空临时表中该`c_port_code`的数据
   - **循环处理每个批次**:
     - 从测试数据表查询一批数据（`batch_size`条记录）
     - 将该批数据插入临时表
     - **立即**从临时表查询该批数据并插入执行表
     - 清空临时表中该`c_port_code`的数据
     - 处理下一批次
   - 重复直到处理完所有数据
3. **UPDATE**: 更新执行表中数据的状态

## 分批次处理的优势

### 1. 内存管理优化
- **问题**: 一次性加载大量数据可能导致内存溢出
- **解决**: 分批次处理限制了单次内存使用量
- **效果**: 即使处理百万级数据也能保持稳定的内存占用

### 2. 事务管理改进
- **问题**: 大事务可能导致锁等待时间过长
- **解决**: 将大事务拆分为多个小事务
- **效果**: 减少锁竞争，提高并发性能

### 3. 错误恢复能力
- **问题**: 大批量操作失败时需要重新处理所有数据
- **解决**: 分批次处理可以精确定位失败的批次
- **效果**: 提高系统的容错能力和恢复效率

### 4. 数据库资源利用
- **问题**: 大批量操作可能占用过多数据库资源
- **解决**: 分批次处理平滑资源使用
- **效果**: 避免对数据库造成冲击，保持系统稳定

### 5. 流式处理优势
- **问题**: 传统批处理需要等待所有批次完成才能进行下一步
- **解决**: 流式处理每个批次立即转移，实现管道化操作
- **效果**: 提高处理效率，减少临时表空间占用，实现真正的流式数据处理

## 配置参数

### 批次大小（batch_size）
- **默认值**: 50
- **推荐范围**: 10-1000
- **调优建议**:
  - 小批次（10-50）: 适合高并发场景，减少锁竞争
  - 中批次（50-200）: 平衡性能和资源使用
  - 大批次（200-1000）: 适合低并发、大数据量场景

### 使用示例

```bash
# 小批次处理（适合高并发）
./run-stress-test.sh -c 50 -p 100 -r 1000 -b 20

# 中等批次处理（平衡模式）
./run-stress-test.sh -c 20 -p 50 -r 500 -b 100

# 大批次处理（适合大数据量）
./run-stress-test.sh -c 10 -p 20 -r 2000 -b 500
```

## 性能监控

分批次处理的性能指标包括：

1. **批次处理时间**: 每个批次的处理耗时
2. **批次数量**: 总共处理的批次数
3. **平均批次大小**: 实际处理的平均记录数
4. **临时表操作效率**: 临时表的插入和查询性能

## 最佳实践

### 1. 批次大小选择
- 根据可用内存调整批次大小
- 考虑数据库的最大连接数限制
- 平衡处理速度和资源消耗

### 2. 并发度配置
- 高并发 + 小批次: 适合CPU密集型场景
- 低并发 + 大批次: 适合IO密集型场景

### 3. 错误处理
- 监控批次处理的成功率
- 记录失败批次的详细信息
- 实现批次级别的重试机制

### 4. 性能调优
- 定期清理临时表数据
- 为临时表创建适当的索引
- 监控临时表的空间使用

## 技术实现细节

### 数据流控制
```java
// 流式分批次处理：每批次立即转移
int offset = 0;
int batchInserted;
int totalExecutionInserted = 0;

do {
    // 1. 从测试数据表分批次插入到临时表
    batchInserted = testDataDao.batchInsertToTempTable(
        connection, portCode, batchSize, offset);
    totalInserted += batchInserted;

    if (batchInserted > 0) {
        // 2. 立即将该批次从临时表转移到执行表
        int batchExecutionInserted = testDataDao.batchInsertFromTempToExecution(
            connection, portCode);
        totalExecutionInserted += batchExecutionInserted;

        // 3. 清空临时表为下一批次做准备
        testDataDao.clearTempTableByPortCode(connection, portCode);
    }

    offset += batchSize;
} while (batchInserted == batchSize);
```

### 事务边界
- 每个工作线程使用独立的数据库连接
- DELETE、分批次INSERT、批量INSERT、UPDATE在同一事务中
- 异常时自动回滚整个事务

### 临时表管理
- 每个工作线程开始时清空临时表
- 使用`c_port_code`进行数据隔离
- 处理完成后数据自动清理

## 监控和调试

### 日志输出
程序会输出详细的流式分批次处理日志：
```
Worker 1 processed batch: 50 rows temp → 50 rows execution (offset: 0)
Worker 1 processed batch: 50 rows temp → 50 rows execution (offset: 50)
Worker 1 processed batch: 30 rows temp → 30 rows execution (offset: 100)
Worker 1 completed batch insert: 130 rows processed, 130 rows inserted to execution table
```

### 性能指标
- 分批次处理总时间
- 每批次平均处理时间
- 临时表操作效率
- 整体吞吐量提升

## 总结

分批次处理功能显著提升了压力测试程序的稳定性和可扩展性，特别适合处理大规模数据的场景。通过合理配置批次大小和并发度，可以在不同的硬件环境和业务需求下获得最佳性能。
