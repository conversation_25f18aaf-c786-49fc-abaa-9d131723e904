--测试表
DROP TABLE wbgz.t_d_ai_act_val_test;
CREATE TABLE wbgz.t_d_ai_act_val_test (
	c_iden varchar(20) NULL,
	c_year_month varchar(10) NULL,
	n_vch_num numeric(10) NULL,
	n_fl_num numeric(5) NULL,
	c_port_code varchar(20) NULL,
	d_chk_acc timestamp(0) NULL,
	c_dta_code varchar(20) NULL,
	c_dv_issue_mode varchar(20) NULL,
	c_dv_var_dur varchar(20) NULL,
	c_dv_invest_cls varchar(20) NULL,
	c_port_cls_code varchar(20) NULL,
	c_dai_code varchar(20) NULL,
	c_ca_code varchar(50) NULL,
	c_sec_code varchar(200) NULL,
	c_fee_code varchar(20) NULL,
	c_net_code varchar(20) NULL,
	c_dva_item_code varchar(20) NULL,
	c_dt_code varchar(20) NULL,
	c_mkt_code varchar(20) NULL,
	c_sec_var_code varchar(20) NULL,
	c_dv_acc_type varchar(20) NULL,
	c_dv_fee_type varchar(20) NULL,
	c_ds_code varchar(20) NULL,
	c_td_chan_code varchar(50) NULL,
	n_way numeric(3) NULL,
	n_amount numeric(38, 16) NULL,
	n_orig_money numeric(18, 4) NULL,
	c_dc_code varchar(20) NULL,
	c_rate_expr varchar(200) NULL,
	n_port_money numeric(18, 4) NULL,
	c_rela_code varchar(30) NULL,
	c_rela_type varchar(20) NULL,
	c_data_idf varchar(10) NULL,
	c_km_code varchar(200) NULL,
	c_desc varchar(500) NULL,
	c_create_by varchar(20) NULL,
	c_create_time varchar(20) NULL,
	c_check_by varchar(20) NULL,
	c_check_time varchar(20) NULL,
	c_update_by varchar(20) NULL,
	c_update_time varchar(20) NULL,
	c_km_name varchar(200) NULL,
	n_check_state numeric(3) NULL,
	n_attach numeric(3) NULL,
	c_dv_km_cls varchar(20) NULL,
	c_km_code_t varchar(200) NULL,
	c_td_no varchar(200) NULL,
	d_trade timestamp(6) NULL,
	c_org_code varchar(30) NULL,
	c_sett_mode varchar(20) NULL,
	c_bank_code varchar(200) NULL,
	c_bm_code varchar(20) NULL,
	c_branch_bank_code varchar(200) NULL,
	c_btzbd_code varchar(200) NULL,
	c_cash_flow_code varchar(20) NULL,
	c_cp_code varchar(50) NULL,
	c_gqtzxm_code varchar(20) NULL,
	c_gys_code varchar(20) NULL,
	c_khfl_code varchar(20) NULL,
	c_kh_code varchar(20) NULL,
	c_ks_code varchar(20) NULL,
	c_qyxydm_code varchar(200) NULL,
	c_recheck_by varchar(20) NULL,
	c_recheck_time varchar(20) NULL,
	c_schd_code varchar(20) NULL,
	c_skzh_code varchar(20) NULL,
	c_xzzz_code varchar(20) NULL,
	c_yhzh_code varchar(25) NULL,
	c_yxhhr_code varchar(20) NULL,
	c_zy_code varchar(20) NULL,
	d_tz_acc timestamp(6) NULL,
	n_recheck_state numeric(3) NULL,
	c_plan_code varchar(20) NULL,
	c_khsx_code varchar(200) NULL,
	c_port_name varchar(200) NULL,
	c_recheck_a varchar(20) NULL
)
WITH (
	orientation=row,
	compression=no
);
--CREATE INDEX t_d_ai_act_val_test_c_port_code_idx ON t_d_ai_act_val_test USING btree (c_port_code) TABLESPACE pg_default;--索引统一在导入数据后建

DROP TABLE wbgz.r_d_ai_act_val_test;
CREATE GLOBAL TEMPORARY TABLE wbgz.r_d_ai_act_val_test (
	c_iden varchar(20) NOT NULL,
	c_year_month varchar(10) DEFAULT ' '::character varying NOT NULL,
	n_vch_num numeric(10) DEFAULT 0 NOT NULL,
	n_fl_num numeric(5) DEFAULT 0 NOT NULL,
	c_port_code varchar(20) DEFAULT ' '::character varying NOT NULL,
	d_chk_acc timestamp(0) DEFAULT '1900-01-01 00:00:00'::timestamp without time zone NOT NULL,
	c_dta_code varchar(20) DEFAULT ' '::character varying NOT NULL,
	c_dv_issue_mode varchar(20) DEFAULT ' '::character varying NOT NULL,
	c_dv_var_dur varchar(20) DEFAULT ' '::character varying NOT NULL,
	c_dv_invest_cls varchar(20) DEFAULT ' '::character varying NOT NULL,
	c_port_cls_code varchar(20) DEFAULT ' '::character varying NOT NULL,
	c_dai_code varchar(20) DEFAULT ' '::character varying NOT NULL,
	c_ca_code varchar(50) DEFAULT ' '::character varying NOT NULL,
	c_sec_code varchar(200) DEFAULT ' '::character varying NULL,
	c_fee_code varchar(20) DEFAULT ' '::character varying NOT NULL,
	c_net_code varchar(20) DEFAULT ' '::character varying NOT NULL,
	c_dva_item_code varchar(20) DEFAULT ' '::character varying NOT NULL,
	c_dt_code varchar(20) DEFAULT ' '::character varying NOT NULL,
	c_mkt_code varchar(20) DEFAULT ' '::character varying NOT NULL,
	c_sec_var_code varchar(20) DEFAULT ' '::character varying NOT NULL,
	c_dv_acc_type varchar(20) DEFAULT ' '::character varying NOT NULL,
	c_dv_fee_type varchar(20) DEFAULT ' '::character varying NOT NULL,
	c_ds_code varchar(20) DEFAULT ' '::character varying NOT NULL,
	c_td_chan_code varchar(50) DEFAULT ' '::character varying NOT NULL,
	n_way numeric(3) DEFAULT 0 NOT NULL,
	n_amount numeric(38, 16) DEFAULT 0 NOT NULL,
	n_orig_money numeric(18, 4) DEFAULT 0 NOT NULL,
	c_dc_code varchar(20) DEFAULT ' '::character varying NOT NULL,
	c_rate_expr varchar(200) DEFAULT ' '::character varying NOT NULL,
	n_port_money numeric(18, 4) DEFAULT 0 NOT NULL,
	c_rela_code varchar(30) DEFAULT ' '::character varying NOT NULL,
	c_rela_type varchar(20) DEFAULT ' '::character varying NOT NULL,
	c_data_idf varchar(10) DEFAULT ' '::character varying NOT NULL,
	c_km_code varchar(200) NULL,
	c_desc varchar(500) NULL,
	c_create_by varchar(20) DEFAULT ' '::character varying NOT NULL,
	c_create_time varchar(20) DEFAULT ' '::character varying NOT NULL,
	c_check_by varchar(20) NULL,
	c_check_time varchar(20) NULL,
	c_update_by varchar(20) NULL,
	c_update_time varchar(20) NULL,
	c_km_name varchar(200) NULL,
	n_check_state numeric(3) DEFAULT 0 NOT NULL,
	n_attach numeric(3) DEFAULT 0 NULL,
	c_dv_km_cls varchar(20) NULL,
	c_km_code_t varchar(200) DEFAULT ' '::character varying NULL,
	c_td_no varchar(200) NULL,
	d_trade timestamp(6) DEFAULT '1900-01-01 00:00:00'::timestamp without time zone NOT NULL,
	c_org_code varchar(30) DEFAULT NULL::character varying NULL,
	c_sett_mode varchar(20) DEFAULT ' '::character varying NULL,
	c_bank_code varchar(200) NULL,
	c_bm_code varchar(20) DEFAULT ' '::character varying NULL,
	c_branch_bank_code varchar(200) NULL,
	c_btzbd_code varchar(200) DEFAULT ' '::character varying NULL,
	c_cash_flow_code varchar(20) NULL,
	c_cp_code varchar(50) DEFAULT ' '::character varying NULL,
	c_gqtzxm_code varchar(20) DEFAULT ' '::character varying NULL,
	c_gys_code varchar(20) DEFAULT ' '::character varying NULL,
	c_khfl_code varchar(20) DEFAULT ' '::character varying NULL,
	c_kh_code varchar(20) DEFAULT ' '::character varying NULL,
	c_ks_code varchar(20) DEFAULT ' '::character varying NULL,
	c_qyxydm_code varchar(200) DEFAULT ' '::character varying NULL,
	c_recheck_by varchar(20) DEFAULT ' '::character varying NULL,
	c_recheck_time varchar(20) DEFAULT ' '::character varying NULL,
	c_schd_code varchar(20) DEFAULT ' '::character varying NULL,
	c_skzh_code varchar(20) DEFAULT ' '::character varying NULL,
	c_xzzz_code varchar(20) DEFAULT ' '::character varying NULL,
	c_yhzh_code varchar(25) DEFAULT ' '::character varying NULL,
	c_yxhhr_code varchar(20) DEFAULT ' '::character varying NULL,
	c_zy_code varchar(20) DEFAULT ' '::character varying NULL,
	d_tz_acc timestamp(6) DEFAULT '1900-01-01 00:00:00'::timestamp without time zone NOT NULL,
	n_recheck_state numeric(3) DEFAULT 0 NOT NULL,
	c_plan_code varchar(20) DEFAULT ' '::character varying NOT NULL,
	c_khsx_code varchar(200) DEFAULT ' '::character varying NOT NULL,
	c_port_name varchar(200) NULL,
	c_recheck_a varchar(20) NULL,
	CONSTRAINT pk_r_d_ai_act_val_test PRIMARY KEY (c_iden)
)
WITH (
	orientation=row,
	storage_type=astore,
	compression=no,
	on_commit_delete_rows=true
);
--CREATE INDEX idx_r_d_ai_act_val_test ON r_d_ai_act_val_test USING btree (c_port_code, c_dai_code) TABLESPACE pg_default;
set query_dop=4;
insert into t_d_ai_act_val_test select * from t_d_ai_act_val_1k;
ALTER TABLE t_d_ai_act_val_test SET (parallel_workers=32);
\parallel on 2
CREATE INDEX t_d_ai_act_val_test_c_port_code_idx ON t_d_ai_act_val_test USING btree (c_port_code);
\parallel off;
ALTER TABLE t_d_ai_act_val_test ADD CONSTRAINT pk_t_d_ai_act_val_test PRIMARY KEY (c_iden);
ALTER TABLE t_d_ai_act_val_test RESET (parallel_workers);
analyze t_d_ai_act_val_test;

--执行表
DROP TABLE wbgz.t_d_ai_act_val_exec;
CREATE TABLE wbgz.t_d_ai_act_val_exec (
	c_iden varchar(20) NULL,
	c_year_month varchar(10) NULL,
	n_vch_num numeric(10) NULL,
	n_fl_num numeric(5) NULL,
	c_port_code varchar(20) NULL,
	d_chk_acc timestamp(0) NULL,
	c_dta_code varchar(20) NULL,
	c_dv_issue_mode varchar(20) NULL,
	c_dv_var_dur varchar(20) NULL,
	c_dv_invest_cls varchar(20) NULL,
	c_port_cls_code varchar(20) NULL,
	c_dai_code varchar(20) NULL,
	c_ca_code varchar(50) NULL,
	c_sec_code varchar(200) NULL,
	c_fee_code varchar(20) NULL,
	c_net_code varchar(20) NULL,
	c_dva_item_code varchar(20) NULL,
	c_dt_code varchar(20) NULL,
	c_mkt_code varchar(20) NULL,
	c_sec_var_code varchar(20) NULL,
	c_dv_acc_type varchar(20) NULL,
	c_dv_fee_type varchar(20) NULL,
	c_ds_code varchar(20) NULL,
	c_td_chan_code varchar(50) NULL,
	n_way numeric(3) NULL,
	n_amount numeric(38, 16) NULL,
	n_orig_money numeric(18, 4) NULL,
	c_dc_code varchar(20) NULL,
	c_rate_expr varchar(200) NULL,
	n_port_money numeric(18, 4) NULL,
	c_rela_code varchar(30) NULL,
	c_rela_type varchar(20) NULL,
	c_data_idf varchar(10) NULL,
	c_km_code varchar(200) NULL,
	c_desc varchar(500) NULL,
	c_create_by varchar(20) NULL,
	c_create_time varchar(20) NULL,
	c_check_by varchar(20) NULL,
	c_check_time varchar(20) NULL,
	c_update_by varchar(20) NULL,
	c_update_time varchar(20) NULL,
	c_km_name varchar(200) NULL,
	n_check_state numeric(3) NULL,
	n_attach numeric(3) NULL,
	c_dv_km_cls varchar(20) NULL,
	c_km_code_t varchar(200) NULL,
	c_td_no varchar(200) NULL,
	d_trade timestamp(6) NULL,
	c_org_code varchar(30) NULL,
	c_sett_mode varchar(20) NULL,
	c_bank_code varchar(200) NULL,
	c_bm_code varchar(20) NULL,
	c_branch_bank_code varchar(200) NULL,
	c_btzbd_code varchar(200) NULL,
	c_cash_flow_code varchar(20) NULL,
	c_cp_code varchar(50) NULL,
	c_gqtzxm_code varchar(20) NULL,
	c_gys_code varchar(20) NULL,
	c_khfl_code varchar(20) NULL,
	c_kh_code varchar(20) NULL,
	c_ks_code varchar(20) NULL,
	c_qyxydm_code varchar(200) NULL,
	c_recheck_by varchar(20) NULL,
	c_recheck_time varchar(20) NULL,
	c_schd_code varchar(20) NULL,
	c_skzh_code varchar(20) NULL,
	c_xzzz_code varchar(20) NULL,
	c_yhzh_code varchar(25) NULL,
	c_yxhhr_code varchar(20) NULL,
	c_zy_code varchar(20) NULL,
	d_tz_acc timestamp(6) NULL,
	n_recheck_state numeric(3) NULL,
	c_plan_code varchar(20) NULL,
	c_khsx_code varchar(200) NULL,
	c_port_name varchar(200) NULL,
	c_recheck_a varchar(20) NULL
)
WITH (
	orientation=row,
	compression=no
);
--CREATE INDEX t_d_ai_act_val_exec_c_port_code_idx ON t_d_ai_act_val_exec USING btree (c_port_code) TABLESPACE pg_default; --索引统一在导入数据后建
set query_dop=4;
insert into t_d_ai_act_val_exec select * from t_d_ai_act_val_1k;
SET maintenance_work_mem = '200GB';
ALTER TABLE t_d_ai_act_val_exec SET (parallel_workers=32);
\parallel on 3
CREATE INDEX idx_t_d_ai_act_val_exec_001 ON t_d_ai_act_val_exec USING btree (c_port_code);
CREATE INDEX idx_t_d_ai_act_val_exec_003 ON t_d_ai_act_val_exec USING btree (c_port_code,d_chk_acc,c_dt_code,c_dva_item_code);
CREATE INDEX idx_t_d_ai_act_val_exec_004 ON t_d_ai_act_val_exec USING btree (c_port_code,c_dai_code,d_chk_acc,c_dta_code);
\parallel off
ALTER TABLE t_d_ai_act_val_exec ADD CONSTRAINT pk_t_d_ai_act_val_exec PRIMARY KEY (c_iden);
ALTER TABLE t_d_ai_act_val_exec RESET (parallel_workers);
analyze t_d_ai_act_val_exec;



--方案表中增加记录
delete t_p_ab_group_rela t where t.c_update_by = 'xutao';
insert into t_p_ab_group_rela(c_iden,c_group_code,c_port_code,c_update_by,n_check_state,c_check_by) values (1234567,'stress_java_test',	'C4F0E1A586','xutao',	1,'xutao');


