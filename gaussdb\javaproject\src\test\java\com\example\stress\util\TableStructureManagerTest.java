package com.example.stress.util;

import com.example.stress.model.TableStructure;
import com.example.util.DatabaseUtil;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;

/**
 * 表结构管理器测试
 */
public class TableStructureManagerTest {
    private static final Logger logger = LoggerFactory.getLogger(TableStructureManagerTest.class);
    
    @Test
    public void testTableStructureRetrieval() {
        logger.info("=== 测试动态表结构获取功能 ===");
        
        try (Connection connection = DatabaseUtil.getConnection()) {
            logger.info("数据库连接成功");
            
            // 测试获取测试数据表结构
            try {
                TableStructure testDataStructure = TableStructureManager.getTestDataTableStructure(connection);
                logger.info("测试数据表结构获取成功: {}", testDataStructure);
                TableStructureManager.printTableStructure(testDataStructure);
            } catch (Exception e) {
                logger.warn("测试数据表结构获取失败（可能表不存在）: {}", e.getMessage());
            }
            
            // 测试获取临时表结构
            try {
                TableStructure tempStructure = TableStructureManager.getTempTableStructure(connection);
                logger.info("临时表结构获取成功: {}", tempStructure);
                TableStructureManager.printTableStructure(tempStructure);
            } catch (Exception e) {
                logger.warn("临时表结构获取失败（可能表不存在）: {}", e.getMessage());
            }
            
            // 测试获取执行表结构
            try {
                TableStructure executionStructure = TableStructureManager.getExecutionTableStructure(connection);
                logger.info("执行表结构获取成功: {}", executionStructure);
                TableStructureManager.printTableStructure(executionStructure);
            } catch (Exception e) {
                logger.warn("执行表结构获取失败（可能表不存在）: {}", e.getMessage());
            }
            
            // 测试表结构一致性验证
            try {
                boolean consistent = TableStructureManager.validateTableStructuresConsistent(connection);
                logger.info("表结构一致性验证结果: {}", consistent);
            } catch (Exception e) {
                logger.warn("表结构一致性验证失败: {}", e.getMessage());
            }
            
            // 测试缓存功能
            logger.info("缓存统计: {}", TableStructureManager.getCacheStats());
            
        } catch (Exception e) {
            logger.error("测试失败", e);
        }
    }
    
    @Test
    public void testDynamicSqlBuilder() {
        logger.info("=== 测试动态SQL构建功能 ===");
        
        try (Connection connection = DatabaseUtil.getConnection()) {
            // 创建一个模拟的表结构用于测试
            java.util.List<TableStructure.ColumnInfo> columns = new java.util.ArrayList<>();
            columns.add(new TableStructure.ColumnInfo("id", "BIGINT", 1));
            columns.add(new TableStructure.ColumnInfo("c_port_code", "VARCHAR", 2));
            columns.add(new TableStructure.ColumnInfo("data_value", "VARCHAR", 3));
            columns.add(new TableStructure.ColumnInfo("description", "TEXT", 4));
            columns.add(new TableStructure.ColumnInfo("status", "INTEGER", 5));
            columns.add(new TableStructure.ColumnInfo("create_time", "TIMESTAMP", 6));
            columns.add(new TableStructure.ColumnInfo("update_time", "TIMESTAMP", 7));
            
            TableStructure mockStructure = new TableStructure("test_table", columns);
            
            // 测试INSERT SQL生成
            String insertSql = DynamicSqlBuilder.buildInsertSql(mockStructure);
            logger.info("生成的INSERT SQL: {}", insertSql);
            
            // 测试SELECT SQL生成
            String selectSql = DynamicSqlBuilder.buildSelectSql(mockStructure, "c_port_code = ?");
            logger.info("生成的SELECT SQL: {}", selectSql);
            
            // 测试分页SELECT SQL生成
            String pagedSelectSql = DynamicSqlBuilder.buildSelectSqlWithPaging(
                mockStructure, "c_port_code = ?", "id", 100, 0);
            logger.info("生成的分页SELECT SQL: {}", pagedSelectSql);
            
            // 测试UPDATE SQL生成
            String updateSql = DynamicSqlBuilder.buildUpdateSql(
                mockStructure, new String[]{"status", "update_time"}, "c_port_code = ?");
            logger.info("生成的UPDATE SQL: {}", updateSql);
            
            // 测试DELETE SQL生成
            String deleteSql = DynamicSqlBuilder.buildDeleteSql("test_table", "c_port_code = ?");
            logger.info("生成的DELETE SQL: {}", deleteSql);
            
            // 测试COUNT SQL生成
            String countSql = DynamicSqlBuilder.buildCountSql("test_table", "c_port_code = ?");
            logger.info("生成的COUNT SQL: {}", countSql);
            
            // 测试主键列识别
            String primaryKey = DynamicSqlBuilder.getPrimaryKeyColumn(mockStructure);
            logger.info("识别的主键列: {}", primaryKey);
            
        } catch (Exception e) {
            logger.error("动态SQL构建测试失败", e);
        }
    }
    
    @Test
    public void testDynamicDataGenerator() {
        logger.info("=== 测试动态数据生成功能 ===");
        
        try (Connection connection = DatabaseUtil.getConnection()) {
            // 创建一个模拟的表结构用于测试
            java.util.List<TableStructure.ColumnInfo> columns = new java.util.ArrayList<>();
            columns.add(new TableStructure.ColumnInfo("id", "BIGINT", 1));
            columns.add(new TableStructure.ColumnInfo("c_port_code", "VARCHAR", 2));
            columns.add(new TableStructure.ColumnInfo("data_value", "VARCHAR", 3));
            columns.add(new TableStructure.ColumnInfo("description", "TEXT", 4));
            columns.add(new TableStructure.ColumnInfo("status", "INTEGER", 5));
            columns.add(new TableStructure.ColumnInfo("create_time", "TIMESTAMP", 6));
            columns.add(new TableStructure.ColumnInfo("update_time", "TIMESTAMP", 7));
            
            TableStructure mockStructure = new TableStructure("test_table", columns);
            
            // 测试表结构验证
            boolean suitable = DynamicDataGenerator.validateTableForStressTesting(mockStructure);
            logger.info("表结构适合压力测试: {}", suitable);
            
            // 测试数据生成策略打印
            DynamicDataGenerator.printDataGenerationStrategy(mockStructure);
            
            // 测试端口代码生成
            String[] portCodes = DynamicDataGenerator.generatePortCodes(5);
            logger.info("生成的端口代码: {}", java.util.Arrays.toString(portCodes));
            
        } catch (Exception e) {
            logger.error("动态数据生成测试失败", e);
        }
    }
}
