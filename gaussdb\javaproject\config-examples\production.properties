# 生产环境配置示例
# Production Environment Configuration Example

# ============================================================================
# 数据库连接配置 Database Connection Configuration
# ============================================================================

# 默认数据库连接配置（生产GaussDB）
database.default.url=*************************************************************************************************************
database.default.username=prod_user
database.default.password=prod_password_encrypted
database.default.type=GAUSSDB

# 源数据库连接配置（用于数据迁移）
database.source.url=*****************************************************************************************************************
database.source.username=source_user
database.source.password=source_password_encrypted
database.source.type=GAUSSDB

# StarRocks数据库连接配置（分析数据库）
database.starrocks.url=*********************************************
database.starrocks.username=starrocks_user
database.starrocks.password=starrocks_password_encrypted
database.starrocks.type=STARROCKS

# ============================================================================
# 元数据视图配置 Metadata View Configuration
# ============================================================================

# GaussDB元数据视图配置
metadata.gaussdb.view=v_table_metadata
metadata.gaussdb.schema=public
metadata.gaussdb.table_name_column=table_name
metadata.gaussdb.column_name_column=column_name
metadata.gaussdb.data_type_column=data_type
metadata.gaussdb.ordinal_position_column=ordinal_position

# StarRocks元数据视图配置
metadata.starrocks.view=columns
metadata.starrocks.schema=information_schema
metadata.starrocks.table_name_column=table_name
metadata.starrocks.column_name_column=column_name
metadata.starrocks.data_type_column=data_type
metadata.starrocks.ordinal_position_column=ordinal_position

# ============================================================================
# 连接池配置 Connection Pool Configuration
# ============================================================================

# 启用连接池
connection.pool.enabled=true

# 生产环境连接池配置（较大的连接池）
connection.pool.default.maximumPoolSize=50
connection.pool.default.minimumIdle=10
connection.pool.default.connectionTimeout=30000
connection.pool.default.idleTimeout=600000
connection.pool.default.maxLifetime=1800000
connection.pool.default.autoCommit=false
connection.pool.default.leakDetectionThreshold=60000

# GaussDB连接池配置
connection.pool.gaussdb.maximumPoolSize=60
connection.pool.gaussdb.minimumIdle=15
connection.pool.gaussdb.connectionTimeout=30000
connection.pool.gaussdb.idleTimeout=600000
connection.pool.gaussdb.maxLifetime=1800000
connection.pool.gaussdb.autoCommit=false
connection.pool.gaussdb.leakDetectionThreshold=60000

# StarRocks连接池配置（分析查询，连接数较少）
connection.pool.starrocks.maximumPoolSize=20
connection.pool.starrocks.minimumIdle=5
connection.pool.starrocks.connectionTimeout=45000
connection.pool.starrocks.idleTimeout=300000
connection.pool.starrocks.maxLifetime=1200000
connection.pool.starrocks.autoCommit=false
connection.pool.starrocks.leakDetectionThreshold=60000

# 连接池监控配置
connection.pool.monitoring.enabled=true
connection.pool.monitoring.interval=30000

# 数据源特定配置（生产环境优化）
connection.pool.cachePrepStmts=true
connection.pool.prepStmtCacheSize=500
connection.pool.prepStmtCacheSqlLimit=4096
connection.pool.useServerPrepStmts=true
connection.pool.useLocalSessionState=true
connection.pool.rewriteBatchedStatements=true
connection.pool.cacheResultSetMetadata=true
connection.pool.cacheServerConfiguration=true
connection.pool.elideSetAutoCommits=true
connection.pool.maintainTimeStats=false
