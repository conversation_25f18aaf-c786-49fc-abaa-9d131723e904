-- ============================================================================
-- GaussDB 压力测试表创建脚本
-- Stress Test Tables Creation Script for GaussDB
-- ============================================================================
-- 
-- 使用说明 Usage Instructions:
-- 1. 根据需要修改表名（在 connection-pool.properties 中配置对应的表名）
-- 2. 在 GaussDB 中执行此脚本创建表
-- 3. 确保数据库用户有创建表和索引的权限
--
-- 注意事项 Notes:
-- - 表名可以通过配置文件 connection-pool.properties 自定义
-- - 默认表名: stress_test_data, stress_temp_data, stress_execution_data
-- - 所有表结构完全相同，只是用途不同
-- ============================================================================

-- 测试数据表（只读，用于提供测试数据）
-- Test Data Table (Read-only, provides test data)
CREATE TABLE stress_test_data (
    id BIGINT PRIMARY KEY,
    c_port_code VARCHAR(50) NOT NULL,  -- 分组字段 Grouping field
    data_value VARCHAR(200),           -- 数据值 Data value
    description VARCHAR(500),          -- 描述信息 Description
    status INTEGER DEFAULT 1,         -- 状态 Status (1=active, 0=inactive)
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,  -- 创建时间 Create time
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP   -- 更新时间 Update time
);

-- 临时表（分批次处理的中间存储）
-- Temporary Table (Intermediate storage for batch processing)
CREATE TABLE stress_temp_data (
    id BIGINT PRIMARY KEY,
    c_port_code VARCHAR(50) NOT NULL,  -- 分组字段 Grouping field
    data_value VARCHAR(200),           -- 数据值 Data value
    description VARCHAR(500),          -- 描述信息 Description
    status INTEGER DEFAULT 1,         -- 状态 Status (1=active, 0=inactive)
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,  -- 创建时间 Create time
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP   -- 更新时间 Update time
);

-- 执行操作表（用于实际的增删改操作）
-- Execution Table (For actual CRUD operations)
CREATE TABLE stress_execution_data (
    id BIGINT PRIMARY KEY,
    c_port_code VARCHAR(50) NOT NULL,  -- 分组字段 Grouping field
    data_value VARCHAR(200),           -- 数据值 Data value
    description VARCHAR(500),          -- 描述信息 Description
    status INTEGER DEFAULT 1,         -- 状态 Status (1=active, 0=inactive)
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,  -- 创建时间 Create time
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP   -- 更新时间 Update time
);

-- ============================================================================
-- 创建索引以提高查询性能
-- Create Indexes for Better Query Performance
-- ============================================================================

-- 测试数据表索引
CREATE INDEX idx_test_data_port_code ON stress_test_data(c_port_code);
CREATE INDEX idx_test_data_status ON stress_test_data(status);
CREATE INDEX idx_test_data_create_time ON stress_test_data(create_time);

-- 临时表索引
CREATE INDEX idx_temp_port_code ON stress_temp_data(c_port_code);
CREATE INDEX idx_temp_status ON stress_temp_data(status);

-- 执行表索引
CREATE INDEX idx_execution_port_code ON stress_execution_data(c_port_code);
CREATE INDEX idx_execution_status ON stress_execution_data(status);
CREATE INDEX idx_execution_update_time ON stress_execution_data(update_time);

-- ============================================================================
-- 验证表创建结果
-- Verify Table Creation
-- ============================================================================

-- 查看表结构
-- View table structure
-- \d stress_test_data;
-- \d stress_temp_data;
-- \d stress_execution_data;

-- 查看索引
-- View indexes
-- SELECT indexname, tablename FROM pg_indexes WHERE tablename LIKE 'stress_%';

-- 验证表是否为空
-- Verify tables are empty
SELECT 'stress_test_data' as table_name, COUNT(*) as record_count FROM stress_test_data
UNION ALL
SELECT 'stress_temp_data' as table_name, COUNT(*) as record_count FROM stress_temp_data
UNION ALL
SELECT 'stress_execution_data' as table_name, COUNT(*) as record_count FROM stress_execution_data;

-- ============================================================================
-- 表权限设置（可选）
-- Table Permissions (Optional)
-- ============================================================================

-- 如果需要为特定用户授权，请取消注释并修改用户名
-- If you need to grant permissions to specific users, uncomment and modify username

-- GRANT SELECT, INSERT, UPDATE, DELETE ON stress_test_data TO your_username;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON stress_temp_data TO your_username;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON stress_execution_data TO your_username;

-- ============================================================================
-- 清理脚本（谨慎使用）
-- Cleanup Script (Use with caution)
-- ============================================================================

-- 如果需要删除表，请取消注释以下语句
-- If you need to drop tables, uncomment the following statements

-- DROP INDEX IF EXISTS idx_execution_update_time;
-- DROP INDEX IF EXISTS idx_execution_status;
-- DROP INDEX IF EXISTS idx_execution_port_code;
-- DROP INDEX IF EXISTS idx_temp_status;
-- DROP INDEX IF EXISTS idx_temp_port_code;
-- DROP INDEX IF EXISTS idx_test_data_create_time;
-- DROP INDEX IF EXISTS idx_test_data_status;
-- DROP INDEX IF EXISTS idx_test_data_port_code;

-- DROP TABLE IF EXISTS stress_execution_data;
-- DROP TABLE IF EXISTS stress_temp_data;
-- DROP TABLE IF EXISTS stress_test_data;

-- ============================================================================
-- 脚本执行完成
-- Script Execution Complete
-- ============================================================================

COMMIT;
