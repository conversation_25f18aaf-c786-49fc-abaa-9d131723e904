package com.example;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 数据迁移服务使用示例
 * 展示如何使用不同的数据库配置进行数据迁移
 */
public class DataMigrationExample {
    private static final Logger logger = LoggerFactory.getLogger(DataMigrationExample.class);
    
    public static void main(String[] args) {
        // 示例1：使用默认数据库配置
        example1_DefaultDatabase();

        // 示例2：使用自定义源数据库配置
        example2_CustomSourceDatabase();

        // 示例3：从测试数据库迁移到生产数据库
        example3_TestToProduction();

        // 示例4：使用自定义元数据视图和分组字段
        example4_CustomMetadataAndGrouping();

        // 示例5：使用不同的分组字段进行迁移
        example5_DifferentGroupingField();

        // 示例6：使用日期分批进行迁移
        example6_DateBatchMigration();

        // 示例7：使用自定义日期分批配置
        example7_CustomDateBatchMigration();

        // 示例8：处理没有主键的表
        example8_NoPrimaryKeyTable();

        // 示例9：从StarRocks迁移数据
        example9_StarRocksMigration();

        // 示例10：连接池管理示例
        example10_ConnectionPoolManagement();

        // 示例11：配置文件管理示例
        example11_ConfigurationManagement();

        // 示例12：使用配置文件中的数据库连接
        example12_DatabaseConfigFromFile();

        // 示例13：跨数据库迁移
        example13_CrossDatabaseMigration();

        // 示例14：日期字段类型处理
        example14_DateFieldTypeHandling();
    }
    
    /**
     * 示例1：使用默认数据库配置进行迁移
     */
    public static void example1_DefaultDatabase() {
        logger.info("=== 示例1：使用默认数据库配置 ===");
        
        DataMigrationService service = new DataMigrationService(
            "t_d_os_trade_curr_test_src", 
            "t_d_os_trade_curr_test_dest1"
        );
        
        try {
            // 使用默认数据库配置
            service.migrateData("jk1000Fa42");
            logger.info("示例1完成");
        } catch (Exception e) {
            logger.error("示例1失败", e);
        }
    }
    
    /**
     * 示例2：使用自定义源数据库配置进行迁移
     */
    public static void example2_CustomSourceDatabase() {
        logger.info("=== 示例2：使用自定义源数据库配置 ===");
        
        // 创建自定义源数据库配置
        DataMigrationService.DatabaseConfig sourceDbConfig = new DataMigrationService.DatabaseConfig(
            "*************************************************************************************************************",
            "sourceuser",
            "sourcepass"
        );
        
        DataMigrationService service = new DataMigrationService(
            "t_d_os_trade_curr_test_src", 
            "t_d_os_trade_curr_test_dest2",
            3000,  // 批次大小
            8      // 线程池大小
        );
        
        try {
            // 使用自定义源数据库配置
            service.migrateData("jk1000Fa42", sourceDbConfig);
            logger.info("示例2完成");
        } catch (Exception e) {
            logger.error("示例2失败", e);
        }
    }
    
    /**
     * 示例3：从测试数据库迁移到生产数据库
     */
    public static void example3_TestToProduction() {
        logger.info("=== 示例3：从测试数据库迁移到生产数据库 ===");
        
        // 测试数据库配置
        DataMigrationService.DatabaseConfig testDbConfig = new DataMigrationService.DatabaseConfig(
            "***********************************************************************************************************",
            "testuser",
            "testpass"
        );
        
        DataMigrationService service = new DataMigrationService(
            "t_d_os_trade_test", 
            "t_d_os_trade_prod"
        );
        
        try {
            // 从测试数据库读取数据，写入到默认（生产）数据库
            service.migrateData("jk1000Fa42", testDbConfig);
            logger.info("示例3完成");
        } catch (Exception e) {
            logger.error("示例3失败", e);
        }
    }
    
    /**
     * 示例4：使用自定义元数据视图和分组字段
     */
    public static void example4_CustomMetadataAndGrouping() {
        logger.info("=== 示例4：使用自定义元数据视图和分组字段 ===");

        // 创建自定义源数据库配置
        DataMigrationService.DatabaseConfig sourceDbConfig = new DataMigrationService.DatabaseConfig(
            "*************************************************************************************************************",
            "customuser",
            "custompass"
        );

        // 使用自定义元数据视图和分组字段
        DataMigrationService service = new DataMigrationService(
            "t_custom_source_table",
            "t_custom_target_table",
            2000,  // 批次大小
            4,     // 线程池大小
            "v_custom_table_metadata",  // 自定义元数据视图名
            "c_custom_group_field"      // 自定义分组字段名
        );

        try {
            // 使用自定义配置进行迁移
            service.migrateData("customPlanCode", sourceDbConfig);
            logger.info("示例4完成");
        } catch (Exception e) {
            logger.error("示例4失败", e);
        }
    }

    /**
     * 示例5：使用不同的分组字段进行迁移
     */
    public static void example5_DifferentGroupingField() {
        logger.info("=== 示例5：使用不同的分组字段进行迁移 ===");

        // 使用 c_region_code 作为分组字段而不是 c_port_code
        DataMigrationService service = new DataMigrationService(
            "t_regional_data_source",
            "t_regional_data_target",
            1500,  // 批次大小
            6,     // 线程池大小
            "v_table_metadata",  // 元数据视图名
            "c_region_code"      // 使用区域代码作为分组字段
        );

        try {
            service.migrateData("regionPlan001");
            logger.info("示例5完成");
        } catch (Exception e) {
            logger.error("示例5失败", e);
        }
    }

    /**
     * 示例6：使用日期分批进行迁移
     */
    public static void example6_DateBatchMigration() {
        logger.info("=== 示例6：使用日期分批进行迁移 ===");

        // 创建支持日期分批的服务（每批处理7天的数据）
        DataMigrationService service = DataMigrationService.createWithDateBatch(
            "t_trade_history_source",
            "t_trade_history_target",
            "d_trade",  // 使用交易日期字段进行分批
            7           // 每批处理7天的数据
        );

        try {
            // 迁移2024年1月1日到2024年1月31日的数据
            service.migrateDataByDateRange("tradePlan2024", "2024-01-01", "2024-01-31");
            logger.info("示例6完成");
        } catch (Exception e) {
            logger.error("示例6失败", e);
        }
    }

    /**
     * 示例7：使用自定义日期分批配置
     */
    public static void example7_CustomDateBatchMigration() {
        logger.info("=== 示例7：使用自定义日期分批配置 ===");

        // 创建自定义源数据库配置
        DataMigrationService.DatabaseConfig sourceDbConfig = new DataMigrationService.DatabaseConfig(
            "**************************************************************************************************************",
            "historyuser",
            "historypass"
        );

        // 创建支持日期分批的服务，使用自定义配置
        DataMigrationService service = DataMigrationService.createWithDateBatch(
            "t_financial_data_source",
            "t_financial_data_target",
            3000,  // 批次大小
            8,     // 线程池大小
            "c_account_code",           // 使用账户代码作为分组字段
            "d_business_date",          // 使用业务日期字段进行分批
            3                           // 每批处理3天的数据
        );

        try {
            // 迁移指定日期范围的数据，使用自定义源数据库
            service.migrateDataByDateRange("financialPlan2024", "2024-01-01", "2024-12-31", sourceDbConfig);
            logger.info("示例7完成");
        } catch (Exception e) {
            logger.error("示例7失败", e);
        }
    }

    /**
     * 示例8：处理没有主键的表
     */
    public static void example8_NoPrimaryKeyTable() {
        logger.info("=== 示例8：处理没有主键的表 ===");

        // 对于没有主键的表，使用日期分批是最佳选择
        DataMigrationService service = DataMigrationService.createWithDateBatch(
            "t_log_data_no_pk",     // 没有主键的日志表
            "t_log_data_target",
            "log_timestamp",        // 使用时间戳字段进行分批
            1                       // 每批处理1天的数据
        );

        try {
            // 迁移最近30天的日志数据
            service.migrateDataByDateRange("logMigration", "2024-01-01", "2024-01-30");
            logger.info("示例8完成");
        } catch (Exception e) {
            logger.error("示例8失败", e);
        }
    }

    /**
     * 示例9：从StarRocks迁移数据
     */
    public static void example9_StarRocksMigration() {
        logger.info("=== 示例9：从StarRocks迁移数据 ===");

        // 创建StarRocks数据库配置
        DataMigrationService.DatabaseConfig starRocksConfig =
            DataMigrationService.DatabaseConfig.createStarRocksConfig(
                "***********",  // StarRocks FE节点IP
                9030,           // StarRocks查询端口
                "analytics_db", // 数据库名
                "staruser",     // 用户名
                "starpass"      // 密码
            );

        // 创建GaussDB目标数据库配置
        DataMigrationService.DatabaseConfig gaussdbConfig =
            DataMigrationService.DatabaseConfig.createGaussDBConfig(
                "**********",   // GaussDB IP
                8000,           // GaussDB端口
                "wbgzdb",       // 数据库名
                "wbgz",         // 用户名
                "Wbgz@1234"     // 密码
            );

        // 创建迁移服务，使用日期分批（StarRocks通常用于分析，数据量大）
        DataMigrationService service = DataMigrationService.createWithDateBatch(
            "analytics_fact_table",     // StarRocks源表
            "fact_table_target",        // GaussDB目标表
            3000,  // 批次大小
            6,     // 线程池大小
            "c_partition_key",             // 分组字段
            "dt",                          // 日期分区字段
            1                              // 每批处理1天数据
        );

        try {
            // 从StarRocks迁移最近7天的数据
            service.migrateDataByDateRange("starRocksPlan", "2024-01-01", "2024-01-07", starRocksConfig);
            logger.info("示例9完成");
        } catch (Exception e) {
            logger.error("示例9失败", e);
        }
    }

    /**
     * 示例10：连接池管理示例
     */
    public static void example10_ConnectionPoolManagement() {
        logger.info("=== 示例10：连接池管理示例 ===");

        try {
            // 打印连接池状态
            com.example.util.DatabaseUtil.printPoolStatus();

            // 创建多个不同类型的数据库配置
            DataMigrationService.DatabaseConfig starRocksConfig =
                DataMigrationService.DatabaseConfig.createStarRocksConfig(
                    "***********", 9030, "analytics_db", "staruser", "starpass");

            DataMigrationService.DatabaseConfig gaussdbConfig =
                DataMigrationService.DatabaseConfig.createGaussDBConfig(
                    "**********", 8000, "wbgzdb", "wbgz", "Wbgz@1234");

            // 创建迁移服务
            DataMigrationService service = new DataMigrationService(
                "test_source_table",
                "test_target_table",
                1000,  // 小批次大小用于测试
                2      // 小线程池用于测试
            );

            logger.info("连接池配置完成，开始测试连接...");

            // 测试连接（这会创建连接池）
            try (java.sql.Connection conn1 = com.example.util.DatabaseUtil.getConnection(
                    starRocksConfig.getUrl(), starRocksConfig.getUsername(), starRocksConfig.getPassword(), starRocksConfig.getDatabaseType())) {
                logger.info("StarRocks连接测试成功");
            }

            try (java.sql.Connection conn2 = com.example.util.DatabaseUtil.getConnection(
                    gaussdbConfig.getUrl(), gaussdbConfig.getUsername(), gaussdbConfig.getPassword(), gaussdbConfig.getDatabaseType())) {
                logger.info("GaussDB连接测试成功");
            }

            // 再次打印连接池状态
            com.example.util.DatabaseUtil.printPoolStatus();

            logger.info("示例10完成");

        } catch (Exception e) {
            logger.error("示例10失败", e);
        } finally {
            // 清理连接池
            com.example.util.DatabaseUtil.shutdown();
            logger.info("连接池已关闭");
        }
    }

    /**
     * 示例11：配置文件管理示例
     */
    public static void example11_ConfigurationManagement() {
        logger.info("=== 示例11：配置文件管理示例 ===");

        try {
            // 打印当前连接池配置
            logger.info("打印当前连接池配置：");
            com.example.util.DatabaseUtil.printConnectionPoolConfig();

            // 检查连接池是否启用
            boolean poolEnabled = com.example.config.ConnectionPoolConfig.isConnectionPoolEnabled();
            logger.info("连接池是否启用（配置文件）: {}", poolEnabled);

            // 检查监控是否启用
            boolean monitoringEnabled = com.example.config.ConnectionPoolConfig.isMonitoringEnabled();
            logger.info("连接池监控是否启用: {}", monitoringEnabled);

            // 获取不同数据库类型的连接池配置
            com.example.util.ConnectionPoolManager.PoolConfig gaussdbConfig =
                com.example.config.ConnectionPoolConfig.getPoolConfig(
                    com.example.util.ConnectionPoolManager.DatabaseType.GAUSSDB);
            logger.info("GaussDB连接池配置 - 最大连接数: {}, 最小空闲: {}",
                       gaussdbConfig.getMaximumPoolSize(), gaussdbConfig.getMinimumIdle());

            com.example.util.ConnectionPoolManager.PoolConfig starrocksConfig =
                com.example.config.ConnectionPoolConfig.getPoolConfig(
                    com.example.util.ConnectionPoolManager.DatabaseType.STARROCKS);
            logger.info("StarRocks连接池配置 - 最大连接数: {}, 最小空闲: {}",
                       starrocksConfig.getMaximumPoolSize(), starrocksConfig.getMinimumIdle());

            // 演示覆盖配置文件设置
            logger.info("演示覆盖配置文件设置...");
            logger.info("当前连接池使用状态: {}", com.example.util.DatabaseUtil.isUsingConnectionPool());

            // 临时禁用连接池
            com.example.util.DatabaseUtil.setUseConnectionPool(false);
            logger.info("临时禁用连接池后: {}", com.example.util.DatabaseUtil.isUsingConnectionPool());

            // 恢复配置文件设置
            com.example.util.DatabaseUtil.clearConnectionPoolOverride();
            logger.info("恢复配置文件设置后: {}", com.example.util.DatabaseUtil.isUsingConnectionPool());

            // 重新加载配置
            logger.info("重新加载配置文件...");
            com.example.config.ConnectionPoolConfig.reload();

            logger.info("示例11完成");

        } catch (Exception e) {
            logger.error("示例11失败", e);
        }
    }

    /**
     * 示例12：使用配置文件中的数据库连接
     */
    public static void example12_DatabaseConfigFromFile() {
        logger.info("=== 示例12：使用配置文件中的数据库连接 ===");

        try {
            // 打印数据库连接配置
            logger.info("打印数据库连接配置：");
            com.example.config.DatabaseConnectionConfig.printConfiguration();

            // 使用配置文件中的默认数据库配置
            DataMigrationService.DatabaseConfig defaultConfig = DataMigrationService.DatabaseConfig.getDefault();
            logger.info("默认数据库配置: {}", defaultConfig);

            // 使用配置文件中的源数据库配置
            DataMigrationService.DatabaseConfig sourceConfig = DataMigrationService.DatabaseConfig.getSource();
            logger.info("源数据库配置: {}", sourceConfig);

            // 根据数据库类型获取配置
            DataMigrationService.DatabaseConfig starRocksConfig =
                DataMigrationService.DatabaseConfig.getByType(
                    com.example.util.ConnectionPoolManager.DatabaseType.STARROCKS);
            logger.info("StarRocks数据库配置: {}", starRocksConfig);

            // 创建迁移服务，使用配置文件中的数据库连接
            DataMigrationService service = new DataMigrationService(
                "config_source_table",
                "config_target_table"
            );

            // 演示使用不同的配置进行迁移
            logger.info("演示使用配置文件中的数据库连接进行迁移...");

            // 使用默认配置进行迁移
            logger.info("使用默认配置进行迁移");
            // service.migrateData("configPlan001");

            // 使用源数据库配置进行迁移
            logger.info("使用源数据库配置进行迁移");
            // service.migrateData("configPlan001", sourceConfig);

            // 使用StarRocks配置进行迁移
            logger.info("使用StarRocks配置进行迁移");
            // service.migrateData("configPlan001", starRocksConfig);

            // 演示元数据视图配置
            logger.info("演示元数据视图配置：");
            com.example.config.DatabaseConnectionConfig.MetadataConfig gaussdbMeta =
                com.example.config.DatabaseConnectionConfig.getMetadataConfig(
                    com.example.util.ConnectionPoolManager.DatabaseType.GAUSSDB);
            logger.info("GaussDB元数据配置: 视图={}, 表名列={}, 列名列={}",
                       gaussdbMeta.getFullViewName(),
                       gaussdbMeta.getTableNameColumn(),
                       gaussdbMeta.getColumnNameColumn());

            com.example.config.DatabaseConnectionConfig.MetadataConfig starRocksMeta =
                com.example.config.DatabaseConnectionConfig.getMetadataConfig(
                    com.example.util.ConnectionPoolManager.DatabaseType.STARROCKS);
            logger.info("StarRocks元数据配置: 视图={}, 表名列={}, 列名列={}",
                       starRocksMeta.getFullViewName(),
                       starRocksMeta.getTableNameColumn(),
                       starRocksMeta.getColumnNameColumn());

            // 重新加载配置
            logger.info("重新加载数据库连接配置...");
            com.example.config.DatabaseConnectionConfig.reload();

            logger.info("示例12完成");

        } catch (Exception e) {
            logger.error("示例12失败", e);
        }
    }

    /**
     * 示例13：跨数据库迁移
     */
    public static void example13_CrossDatabaseMigration() {
        logger.info("=== 示例13：跨数据库迁移 ===");

        try {
            // 从StarRocks迁移到GaussDB
            logger.info("演示从StarRocks迁移到GaussDB：");
            DataMigrationService.DatabaseConfig starRocksConfig =
                DataMigrationService.DatabaseConfig.getByType(
                    com.example.util.ConnectionPoolManager.DatabaseType.STARROCKS);
            DataMigrationService.DatabaseConfig gaussdbConfig =
                DataMigrationService.DatabaseConfig.getByType(
                    com.example.util.ConnectionPoolManager.DatabaseType.GAUSSDB);

            // 创建迁移服务，指定不同的源和目标数据库
            DataMigrationService service1 = new DataMigrationService(
                "analytics_fact_table",
                "gaussdb_fact_table",
                2000, 4, gaussdbConfig  // 指定目标数据库配置
            );

            logger.info("从StarRocks迁移到GaussDB的服务已创建");
            // service1.migrateData("crossPlan001", starRocksConfig);

            // 从GaussDB迁移到MySQL
            logger.info("演示从GaussDB迁移到MySQL：");
            DataMigrationService.DatabaseConfig mysqlConfig =
                DataMigrationService.DatabaseConfig.getByType(
                    com.example.util.ConnectionPoolManager.DatabaseType.MYSQL);

            DataMigrationService service2 = new DataMigrationService(
                "gaussdb_source_table",
                "mysql_target_table",
                1000, 2, mysqlConfig  // 指定MySQL作为目标数据库
            );

            logger.info("从GaussDB迁移到MySQL的服务已创建");
            // service2.migrateData("crossPlan002", gaussdbConfig);

            // 日期分批跨数据库迁移
            logger.info("演示日期分批跨数据库迁移：");
            DataMigrationService service3 = DataMigrationService.createWithDateBatch(
                "starrocks_log_table",
                "postgresql_log_table",
                1500, 3,
                "c_port_code", "log_date", 7,
                DataMigrationService.DatabaseConfig.getByType(
                    com.example.util.ConnectionPoolManager.DatabaseType.POSTGRESQL)
            );

            logger.info("从StarRocks到PostgreSQL的日期分批迁移服务已创建");
            // service3.migrateDataByDateRange("crossPlan003", "2024-01-01", "2024-01-31", starRocksConfig);

            // 演示配置文件中的跨数据库配置
            logger.info("演示配置文件中的跨数据库配置：");
            logger.info("源数据库配置（StarRocks）: {}", starRocksConfig);
            logger.info("目标数据库配置（GaussDB）: {}", gaussdbConfig);
            logger.info("目标数据库配置（MySQL）: {}", mysqlConfig);

            logger.info("示例13完成");

        } catch (Exception e) {
            logger.error("示例13失败", e);
        }
    }

    /**
     * 示例14：日期字段类型处理
     */
    public static void example14_DateFieldTypeHandling() {
        logger.info("=== 示例14：日期字段类型处理 ===");

        try {
            // 演示不同日期字段类型的处理
            logger.info("演示不同数据库中日期字段类型的处理：");

            // GaussDB中的日期字段处理
            logger.info("1. GaussDB日期字段类型处理：");
            DataMigrationService.DatabaseConfig gaussdbConfig =
                DataMigrationService.DatabaseConfig.getByType(
                    com.example.util.ConnectionPoolManager.DatabaseType.GAUSSDB);

            // 创建日期分批迁移服务 - DATE类型字段
            DataMigrationService service1 = DataMigrationService.createWithDateBatch(
                "trade_log_table",           // 源表
                "trade_log_target",          // 目标表
                1000, 2,                     // 批次大小和线程数
                "c_port_code",               // 分组字段
                "d_trade_date",              // DATE类型的日期字段
                1                            // 每批1天
            );

            logger.info("创建了处理DATE类型字段的迁移服务");
            // service1.migrateDataByDateRange("datePlan001", "2024-01-01", "2024-01-31", gaussdbConfig);

            // StarRocks中的时间戳字段处理
            logger.info("2. StarRocks时间戳字段类型处理：");
            DataMigrationService.DatabaseConfig starrocksConfig =
                DataMigrationService.DatabaseConfig.getByType(
                    com.example.util.ConnectionPoolManager.DatabaseType.STARROCKS);

            // 创建日期分批迁移服务 - TIMESTAMP类型字段
            DataMigrationService service2 = DataMigrationService.createWithDateBatch(
                "analytics_events",          // 源表
                "events_target",             // 目标表
                2000, 4,                     // 批次大小和线程数
                "user_id",                   // 分组字段
                "event_timestamp",           // TIMESTAMP类型的日期字段
                7                            // 每批7天
            );

            logger.info("创建了处理TIMESTAMP类型字段的迁移服务");
            // service2.migrateDataByDateRange("timestampPlan001", "2024-01-01", "2024-01-31", starrocksConfig);

            // MySQL中的字符串日期字段处理
            logger.info("3. MySQL字符串日期字段类型处理：");
            DataMigrationService.DatabaseConfig mysqlConfig =
                DataMigrationService.DatabaseConfig.getByType(
                    com.example.util.ConnectionPoolManager.DatabaseType.MYSQL);

            // 创建日期分批迁移服务 - VARCHAR类型的日期字段
            DataMigrationService service3 = DataMigrationService.createWithDateBatch(
                "log_table",                 // 源表
                "log_target",                // 目标表
                1500, 3,                     // 批次大小和线程数
                "session_id",                // 分组字段
                "log_date_str",              // VARCHAR类型的日期字段（格式：yyyy-MM-dd）
                3                            // 每批3天
            );

            logger.info("创建了处理VARCHAR类型日期字段的迁移服务");
            // service3.migrateDataByDateRange("stringDatePlan001", "2024-01-01", "2024-01-31", mysqlConfig);

            // 演示日期字段类型检测
            logger.info("4. 日期字段类型检测演示：");

            // 模拟不同类型的字段信息
            DataMigrationService.ColumnInfo dateColumn = new DataMigrationService.ColumnInfo("trade_date", "DATE", 1);
            DataMigrationService.ColumnInfo timestampColumn = new DataMigrationService.ColumnInfo("created_at", "TIMESTAMP", 2);
            DataMigrationService.ColumnInfo stringColumn = new DataMigrationService.ColumnInfo("date_str", "VARCHAR(20)", 3);
            DataMigrationService.ColumnInfo numericColumn = new DataMigrationService.ColumnInfo("date_num", "BIGINT", 4);

            logger.info("DATE字段 '{}' 是否为日期时间类型: {}", dateColumn.getColumnName(), dateColumn.isDateTimeType());
            logger.info("TIMESTAMP字段 '{}' 是否为日期时间类型: {}", timestampColumn.getColumnName(), timestampColumn.isDateTimeType());
            logger.info("VARCHAR字段 '{}' 是否为字符串类型: {}", stringColumn.getColumnName(), stringColumn.isStringType());
            logger.info("BIGINT字段 '{}' 是否为数值类型: {}", numericColumn.getColumnName(), numericColumn.isNumericType());

            // 演示跨数据库日期字段迁移
            logger.info("5. 跨数据库日期字段迁移：");
            logger.info("从StarRocks（TIMESTAMP）迁移到GaussDB（DATE）");

            DataMigrationService service4 = DataMigrationService.createWithDateBatch(
                "starrocks_events",          // StarRocks源表
                "gaussdb_events",            // GaussDB目标表
                1000, 2,
                "event_id",
                "event_time",                // StarRocks中的TIMESTAMP字段
                1,
                gaussdbConfig                // 目标是GaussDB
            );

            logger.info("创建了跨数据库日期字段迁移服务");
            // service4.migrateDataByDateRange("crossDatePlan001", "2024-01-01", "2024-01-31", starrocksConfig);

            logger.info("示例14完成 - 展示了不同数据库中日期字段类型的正确处理方式");

        } catch (Exception e) {
            logger.error("示例14失败", e);
        }
    }

    /**
     * 创建数据库配置的工具方法
     */
    public static DataMigrationService.DatabaseConfig createDatabaseConfig(
            String host, int port, String database, String username, String password) {
        String url = String.format(
            "*********************************************************************************************",
            host, port, database
        );
        return new DataMigrationService.DatabaseConfig(url, username, password);
    }
}
