# 数据迁移服务使用指南

## 概述

`DataMigrationService` 现在支持从不同的数据库中迁移数据。您可以指定源数据库配置，而目标数据库仍使用默认配置。

## 新增功能

### 1. DatabaseConfig 类

新增了 `DatabaseConfig` 内部类来管理数据库连接配置：

```java
public static class DatabaseConfig {
    private final String url;
    private final String username;
    private final String password;

    public DatabaseConfig(String url, String username, String password) {
        // 构造函数
    }

    public static DatabaseConfig getDefault() {
        // 返回默认数据库配置
    }
}
```

### 2. ColumnInfo 类

新增了 `ColumnInfo` 内部类来存储表列信息：

```java
public static class ColumnInfo {
    private final String columnName;
    private final String dataType;
    private final int columnOrder;
    // getter 方法...
}
```

### 3. 重载的 migrateData 方法

```java
// 使用默认数据库配置
public void migrateData(String planCode)

// 使用自定义源数据库配置
public void migrateData(String planCode, DatabaseConfig sourceDbConfig)
```

### 4. 动态SQL构建

- **buildSelectSqlByPortCode**: 从元数据视图动态获取表字段信息构建SELECT语句
- **buildInsertSql**: 从元数据视图动态获取表字段信息构建INSERT语句
- **setInsertParameters**: 根据字段类型动态设置参数

### 5. 参数化分组字段

- 支持自定义分组字段名（默认为 `c_port_code`）
- 支持自定义元数据视图名（默认为 `v_table_metadata`）

### 6. 日期分批功能

- **适用场景**: 没有唯一主键的表，或需要按时间范围迁移的场景
- **分批策略**: 通过指定开始日期、结束日期和每批天数进行分批处理
- **日期字段**: 支持自定义日期字段名，用于WHERE条件过滤
- **并行处理**: 每个日期批次在独立线程中处理，提高迁移效率

### 7. 连接池管理

- **HikariCP**: 使用高性能的HikariCP连接池
- **多数据源**: 支持同时管理多个不同类型的数据库连接池
- **自动检测**: 根据JDBC URL自动检测数据库类型
- **连接复用**: 避免频繁创建和销毁数据库连接，提高性能
- **连接监控**: 提供连接池状态监控和泄漏检测

### 8. 多数据库支持

- **GaussDB**: 华为GaussDB数据库（默认支持）
- **StarRocks**: StarRocks分析型数据库
- **MySQL**: MySQL数据库
- **PostgreSQL**: PostgreSQL数据库
- **Oracle**: Oracle数据库
- **自动驱动**: 根据数据库类型自动加载相应的JDBC驱动

### 9. 配置文件管理

- **配置文件**: `connection-pool.properties` 存储所有配置参数
- **数据库连接**: 支持多个数据库连接配置
- **元数据视图**: 支持不同数据库的元数据视图配置
- **分类配置**: 支持不同数据库类型的独立配置
- **动态加载**: 支持运行时重新加载配置
- **覆盖机制**: 支持程序中临时覆盖配置文件设置
- **默认值**: 配置文件缺失时使用合理的默认值

### 10. 日期字段类型智能处理

- **自动类型检测**: 根据源表字段类型自动选择合适的参数绑定方式
- **多种日期类型支持**: DATE、TIMESTAMP、DATETIME、VARCHAR等
- **跨数据库兼容**: 不同数据库的日期类型自动适配
- **类型转换**: 字符串日期、数值时间戳等格式自动转换
- **错误恢复**: 类型转换失败时自动回退到字符串类型

## 使用方法

### 方法1：使用默认配置

```java
DataMigrationService service = new DataMigrationService(
    "source_table",
    "target_table"
);

service.migrateData("planCode");
```

### 方法2：使用自定义源数据库配置

```java
// 创建自定义源数据库配置
DataMigrationService.DatabaseConfig sourceDbConfig =
    new DataMigrationService.DatabaseConfig(
        "*************************************************************************************************************",
        "username",
        "password"
    );

DataMigrationService service = new DataMigrationService(
    "source_table",
    "target_table"
);

// 从自定义源数据库迁移数据到默认目标数据库
service.migrateData("planCode", sourceDbConfig);
```

### 方法3：使用自定义元数据视图和分组字段

```java
DataMigrationService service = new DataMigrationService(
    "source_table",
    "target_table",
    2000,  // 批次大小
    4,     // 线程池大小
    "v_custom_table_metadata",  // 自定义元数据视图名
    "c_custom_group_field"      // 自定义分组字段名
);

service.migrateData("planCode", sourceDbConfig);
```

### 方法4：使用不同的分组字段

```java
// 使用 c_region_code 作为分组字段而不是默认的 c_port_code
DataMigrationService service = new DataMigrationService(
    "regional_data_source",
    "regional_data_target",
    1500,  // 批次大小
    6,     // 线程池大小
    "v_table_metadata",  // 元数据视图名
    "c_region_code"      // 使用区域代码作为分组字段
);

service.migrateData("regionPlan001");
```

### 方法5：使用日期分批（适用于无主键表）

```java
// 创建支持日期分批的服务
DataMigrationService service = DataMigrationService.createWithDateBatch(
    "t_log_data_source",
    "t_log_data_target",
    "log_timestamp",  // 日期字段名
    7                 // 每批处理7天的数据
);

// 迁移指定日期范围的数据
service.migrateDataByDateRange("logPlan", "2024-01-01", "2024-01-31");
```

### 方法6：使用自定义日期分批配置

```java
// 创建自定义源数据库配置
DataMigrationService.DatabaseConfig sourceDbConfig =
    new DataMigrationService.DatabaseConfig(
        "****************************************",
        "historyuser",
        "historypass"
    );

// 创建支持日期分批的服务，使用完整自定义配置
DataMigrationService service = DataMigrationService.createWithDateBatch(
    "t_financial_data_source",
    "t_financial_data_target",
    3000,  // 批次大小
    8,     // 线程池大小
    "v_source_table_metadata",  // 源库元数据视图
    "v_target_table_metadata",  // 目标库元数据视图
    "c_account_code",           // 分组字段
    "d_business_date",          // 日期字段
    3                           // 每批处理3天
);

// 使用自定义源数据库进行迁移
service.migrateDataByDateRange("financialPlan", "2024-01-01", "2024-12-31", sourceDbConfig);
```

### 方法7：从StarRocks迁移数据

```java
// 创建StarRocks数据库配置
DataMigrationService.DatabaseConfig starRocksConfig =
    DataMigrationService.DatabaseConfig.createStarRocksConfig(
        "***********",  // StarRocks FE节点IP
        9030,           // StarRocks查询端口
        "analytics_db", // 数据库名
        "staruser",     // 用户名
        "starpass"      // 密码
    );

// 创建迁移服务
DataMigrationService service = DataMigrationService.createWithDateBatch(
    "analytics_fact_table",     // StarRocks源表
    "fact_table_target",        // 目标表
    3000, 6,                    // 批次大小和线程池
    "information_schema.columns", // StarRocks元数据视图
    "v_table_metadata",           // 目标库元数据视图
    "c_partition_key",            // 分组字段
    "dt",                         // 日期分区字段
    1                             // 每批处理1天数据
);

// 从StarRocks迁移数据
service.migrateDataByDateRange("starRocksPlan", "2024-01-01", "2024-01-07", starRocksConfig);
```

### 方法8：连接池管理

```java
// 启用/禁用连接池
DatabaseUtil.setUseConnectionPool(true);  // 默认启用

// 打印连接池状态
DatabaseUtil.printPoolStatus();

// 程序结束时关闭所有连接池
DatabaseUtil.shutdown();
```

### 方法9：自定义连接池配置

```java
// 创建自定义连接池配置
ConnectionPoolManager.PoolConfig poolConfig = new ConnectionPoolManager.PoolConfig();
poolConfig.setMaximumPoolSize(50);      // 最大连接数
poolConfig.setMinimumIdle(10);          // 最小空闲连接数
poolConfig.setConnectionTimeout(60000); // 连接超时时间
poolConfig.setIdleTimeout(300000);      // 空闲超时时间

// 使用自定义配置获取连接
Connection conn = ConnectionPoolManager.getConnection(
    url, username, password, DatabaseType.STARROCKS, poolConfig);
```

### 方法10：配置文件管理

#### 配置文件位置
配置文件位于 `src/main/resources/connection-pool.properties`

#### 主要配置项
```properties
# 是否启用连接池
connection.pool.enabled=true

# 默认连接池配置
connection.pool.default.maximumPoolSize=20
connection.pool.default.minimumIdle=5
connection.pool.default.connectionTimeout=30000
connection.pool.default.idleTimeout=600000
connection.pool.default.maxLifetime=1800000

# StarRocks特定配置
connection.pool.starrocks.maximumPoolSize=15
connection.pool.starrocks.minimumIdle=5
connection.pool.starrocks.connectionTimeout=45000
```

#### 程序中使用配置
```java
// 检查配置文件中的连接池开关
boolean poolEnabled = ConnectionPoolConfig.isConnectionPoolEnabled();

// 获取特定数据库类型的配置
PoolConfig starrocksConfig = ConnectionPoolConfig.getPoolConfig(DatabaseType.STARROCKS);

// 临时覆盖配置文件设置
DatabaseUtil.setUseConnectionPool(false);  // 临时禁用
DatabaseUtil.clearConnectionPoolOverride(); // 恢复配置文件设置

// 重新加载配置文件
ConnectionPoolConfig.reload();

// 打印当前配置
DatabaseUtil.printConnectionPoolConfig();
```

## 命令行使用

### 新版命令行参数（推荐）

#### 基本语法
```bash
java -jar gaussdb-data-migration.jar [OPTIONS]
```

#### 必需参数
- `--source-table, -st <table>`: 源表名
- `--target-table, -tt <table>`: 目标表名
- `--plan-code, -p <code>`: 计划代码

#### 可选参数
- `--mode, -m <mode>`: 迁移模式（migrate, date-migrate）
- `--batch-size, -bs <size>`: 批次大小（默认：2000）
- `--thread-pool, -tp <size>`: 线程池大小（默认：4）
- `--source-db, -sdb <config>`: 源数据库配置名（默认：default）
- `--target-db, -tdb <config>`: 目标数据库配置名（默认：default）
- `--group-by, -gb <column>`: 分组字段名（默认：c_port_code）

#### 日期分批参数
- `--date-column, -dc <column>`: 日期字段名
- `--batch-days, -bd <days>`: 每批天数（默认：1）
- `--start-date, -sd <date>`: 开始日期（格式：yyyy-MM-dd）
- `--end-date, -ed <date>`: 结束日期（格式：yyyy-MM-dd）

#### 工具参数
- `--config, -c`: 显示当前配置
- `--help, -h`: 显示帮助信息

### 使用示例

#### 1. 基本迁移
```bash
java -jar gaussdb-data-migration.jar -st source_table -tt target_table -p plan001
```

#### 2. 自定义参数迁移
```bash
java -jar gaussdb-data-migration.jar -st source_table -tt target_table -p plan001 -bs 5000 -tp 8
```

#### 3. 从StarRocks迁移到GaussDB
```bash
java -jar gaussdb-data-migration.jar -st analytics_table -tt target_table -p plan001 -sdb starrocks -tdb gaussdb
```

#### 4. 从GaussDB迁移到StarRocks
```bash
java -jar gaussdb-data-migration.jar -st source_table -tt analytics_table -p plan001 -sdb gaussdb -tdb starrocks
```

#### 5. 日期分批迁移
```bash
java -jar gaussdb-data-migration.jar -m date-migrate -st log_table -tt target_table -p plan001 \
    -dc log_timestamp -sd 2024-01-01 -ed 2024-01-31 -bd 7
```

#### 6. 从StarRocks进行日期分批迁移到MySQL
```bash
java -jar gaussdb-data-migration.jar -m date-migrate -st analytics_fact -tt target_table -p plan001 \
    -sdb starrocks -tdb mysql -dc dt -sd 2024-01-01 -ed 2024-01-31 -bd 1
```

#### 7. 跨数据库类型迁移
```bash
# 从MySQL迁移到PostgreSQL
java -jar gaussdb-data-migration.jar -st mysql_table -tt pg_table -p plan001 \
    -sdb mysql -tdb postgresql

# 从Oracle迁移到GaussDB
java -jar gaussdb-data-migration.jar -st oracle_table -tt gaussdb_table -p plan001 \
    -sdb oracle -tdb gaussdb
```

#### 8. 显示配置信息
```bash
java -jar gaussdb-data-migration.jar --config
```

#### 9. 显示帮助信息
```bash
java -jar gaussdb-data-migration.jar --help
```

### 启动脚本使用

#### Linux/Mac
```bash
# 使用启动脚本
./run-migration.sh migrate -st source_table -tt target_table -p plan001

# 日期分批迁移
./run-migration.sh date-migrate -st log_table -tt target_table -p plan001 \
    -dc timestamp -sd 2024-01-01 -ed 2024-01-31

# 显示配置
./run-migration.sh config

# 显示示例
./run-migration.sh examples
```

#### Windows
```cmd
REM 使用启动脚本
run-migration.bat migrate -st source_table -tt target_table -p plan001

REM 日期分批迁移
run-migration.bat date-migrate -st log_table -tt target_table -p plan001 ^
    -dc timestamp -sd 2024-01-01 -ed 2024-01-31

REM 显示配置
run-migration.bat config
```

### 旧版命令行参数（仍支持）

#### 基本用法
```bash
java -jar gaussdb-data-migration.jar <sourceTable> <targetTable> <planCode>
```

#### 自定义参数
```bash
java -jar gaussdb-data-migration.jar <sourceTable> <targetTable> <planCode> <batchSize> <threadPoolSize>
```

#### 示例
```bash
# 使用默认配置
java -jar gaussdb-data-migration.jar t_d_os_trade_curr_test_src t_d_os_trade_curr_test_dest1 jk1000Fa42

# 自定义批次大小和线程池
java -jar gaussdb-data-migration.jar t_d_os_trade_curr_test_src t_d_os_trade_curr_test_dest1 jk1000Fa42 3000 8
```

### 方法11：使用配置文件中的数据库连接

#### 数据库连接配置
```properties
# 默认数据库连接
database.default.url=***************************************
database.default.username=defaultuser
database.default.password=defaultpass
database.default.type=GAUSSDB

# StarRocks数据库连接
database.starrocks.url=*********************************************
database.starrocks.username=staruser
database.starrocks.password=starpass
database.starrocks.type=STARROCKS
```

#### 元数据视图配置
```properties
# GaussDB元数据视图
metadata.gaussdb.view=v_table_metadata
metadata.gaussdb.schema=public
metadata.gaussdb.table_name_column=table_name
metadata.gaussdb.column_name_column=column_name
metadata.gaussdb.data_type_column=data_type
metadata.gaussdb.ordinal_position_column=ordinal_position

# StarRocks元数据视图
metadata.starrocks.view=columns
metadata.starrocks.schema=information_schema
metadata.starrocks.table_name_column=table_name
metadata.starrocks.column_name_column=column_name
metadata.starrocks.data_type_column=data_type
metadata.starrocks.ordinal_position_column=ordinal_position
```

#### 程序中使用
```java
// 使用配置文件中的默认数据库配置
DatabaseConfig defaultConfig = DatabaseConfig.getDefault();

// 使用配置文件中的源数据库配置
DatabaseConfig sourceConfig = DatabaseConfig.getSource();

// 根据数据库类型获取配置
DatabaseConfig starRocksConfig = DatabaseConfig.getByType(DatabaseType.STARROCKS);

// 创建迁移服务，自动使用配置文件中的元数据视图配置
DataMigrationService service = new DataMigrationService("source_table", "target_table");

// 使用不同的数据库配置进行迁移
service.migrateData("planCode");                    // 使用默认配置
service.migrateData("planCode", sourceConfig);      // 使用源配置
service.migrateData("planCode", starRocksConfig);   // 使用StarRocks配置

// 打印数据库连接配置
DatabaseConnectionConfig.printConfiguration();
```

### 方法12：日期字段类型智能处理

#### 支持的日期字段类型

| 数据库类型 | 支持的日期字段类型 | 绑定方式 | 示例 |
|-----------|------------------|---------|------|
| **GaussDB** | DATE, TIMESTAMP, DATETIME | `setDate()`, `setTimestamp()` | `2024-01-01` |
| **StarRocks** | DATE, DATETIME, TIMESTAMP | `setDate()`, `setTimestamp()` | `2024-01-01 00:00:00` |
| **MySQL** | DATE, DATETIME, TIMESTAMP, VARCHAR | `setDate()`, `setTimestamp()`, `setString()` | `2024-01-01` |
| **PostgreSQL** | DATE, TIMESTAMP, VARCHAR | `setDate()`, `setTimestamp()`, `setString()` | `2024-01-01` |
| **Oracle** | DATE, TIMESTAMP, VARCHAR2, NUMBER | `setDate()`, `setTimestamp()`, `setString()`, `setLong()` | `2024-01-01` |

#### 自动类型检测和转换

```java
// 系统会自动检测日期字段类型并选择合适的绑定方式

// DATE类型字段 - 自动转换为java.sql.Date
// "2024-01-01" -> java.sql.Date.valueOf("2024-01-01")

// TIMESTAMP类型字段 - 自动转换为java.sql.Timestamp
// "2024-01-01" -> java.sql.Timestamp.valueOf("2024-01-01 00:00:00")

// VARCHAR类型字段 - 直接使用字符串
// "2024-01-01" -> setString("2024-01-01")

// 数值类型字段（时间戳） - 转换为数值
// "2024-01-01" -> 20240101L (Long类型)
```

#### 使用示例

```java
// 日期分批迁移 - 系统自动处理日期字段类型
java -jar gaussdb-data-migration.jar -m date-migrate \
    -st source_table -tt target_table -p plan001 \
    -dc trade_date -sd 2024-01-01 -ed 2024-01-31 -bd 1

// 跨数据库日期字段迁移
java -jar gaussdb-data-migration.jar -m date-migrate \
    -st starrocks_events -tt gaussdb_events -p plan001 \
    -sdb starrocks -tdb gaussdb \
    -dc event_timestamp -sd 2024-01-01 -ed 2024-01-31
```

## 工作原理

### 传统分批模式（基于分组字段 + OFFSET/LIMIT）

1. **源数据库读取**：使用指定的 `sourceDbConfig` 连接源数据库读取数据
2. **目标数据库写入**：使用默认数据库配置连接目标数据库写入数据
3. **并行处理**：每个分组值（如 port_code）在独立的线程中处理，支持并行迁移
4. **批量处理**：数据按固定条数（batchSize）读取和写入，使用 OFFSET/LIMIT 分页

### 日期分批模式（基于日期范围）

1. **日期范围分割**：将总的日期范围按指定天数（batchDays）分割成多个子范围
2. **并行日期批次**：每个日期范围在独立的线程中处理
3. **分组值查询**：对每个日期范围，先查询该范围内存在的所有分组值
4. **数据读取**：使用日期范围 + 分组值的组合条件读取数据
5. **SQL条件**：`WHERE groupByColumn = ? AND dateColumn >= ? AND dateColumn <= ?`

### 适用场景对比

| 特性 | 传统分批模式 | 日期分批模式 |
|------|-------------|-------------|
| **适用表类型** | 有唯一主键或自增ID | 无主键，有日期字段 |
| **分批依据** | 固定条数 + 排序 | 日期范围 |
| **并行维度** | 分组字段值 | 日期范围 |
| **数据一致性** | 依赖主键排序 | 依赖日期范围 |
| **典型场景** | 交易数据、用户数据 | 日志数据、历史数据 |

## 元数据视图要求

系统需要一个元数据视图来获取表的字段信息，视图应包含以下字段：

```sql
CREATE VIEW v_table_metadata AS
SELECT
    table_name,           -- 表名
    column_name,          -- 列名
    data_type,            -- 数据类型
    ordinal_position      -- 列的顺序位置
FROM information_schema.columns
WHERE table_schema = 'your_schema_name'
ORDER BY table_name, ordinal_position;
```

### 支持的数据类型映射

- `VARCHAR`, `CHAR`, `TEXT` → `setString()`
- `TIMESTAMP`, `DATETIME` → `setTimestamp()`
- `DATE` → `setDate()`
- `DECIMAL`, `NUMERIC` → `setBigDecimal()`
- `INT`, `INTEGER` → `setInt()`
- `BIGINT` → `setLong()`
- `FLOAT`, `DOUBLE` → `setDouble()`
- 其他类型 → `setString()` (默认)

## 注意事项

1. **连接管理**：每个批次处理都会创建独立的源数据库和目标数据库连接
2. **事务控制**：目标数据库的写入操作在事务中执行，确保数据一致性
3. **错误处理**：如果某个批次失败，不会影响其他批次的处理
4. **性能监控**：详细的日志记录包括读取时间、写入时间和吞吐量统计
5. **动态SQL回退**：如果动态SQL构建失败，会自动回退到静态SQL
6. **元数据缓存**：表字段信息会被缓存，避免重复查询元数据视图
7. **分组字段参数化**：支持使用任意字段作为数据分组依据

## 配置参数说明

- `sourceTable`: 源表名
- `targetTable`: 目标表名  
- `planCode`: 计划代码，用于筛选数据
- `batchSize`: 批次大小（默认：2000）
- `threadPoolSize`: 线程池大小（默认：4）
- `sourceDbUrl`: 源数据库URL
- `sourceDbUser`: 源数据库用户名
- `sourceDbPassword`: 源数据库密码

## 示例代码

参考 `DataMigrationExample.java` 文件中的完整示例代码。
