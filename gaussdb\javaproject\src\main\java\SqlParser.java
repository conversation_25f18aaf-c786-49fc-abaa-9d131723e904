import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

public class SqlParser {

    public static void main(String[] args) {
        String query = "SELECT C_IDEN,C_PORT_CODE,D_BOOK_DATE,C_SEC_CODE,C_SEC_VAR_CODE,  C_DV_INVEST_CLS,C_DTA_CODE,C_DV_ISSUE_MODE,C_MKT_CODE,C_TAX_WAY, N_BASE, N_BASE_BAL  ,N_TAXBAL_LAST,N_TAX,N_TAX_BAL,N_KMBAL_LAST,N_VAL_MONEY,N_KM_BAL,N_PORT_COST,N_PORT_PRICE,C_DT_CODE,C_FEE_CODE,C_TAX_TYPE,N_INITGZ_BAL,C_DAI_CODE FROM  (SELECT ' ' AS C_IDEN,  $1 AS C_PORT_CODE,  $2::date AS D_BOOK_DATE,  C_SEC_CODE,  C_SEC_VAR_CODE,   C_DV_INVEST_CLS,  C_DTA_CODE,  C_DV_ISSUE_MODE,  C_MKT_CODE,  C_TAX_WAY,  N_BASE,  N_BASE_BAL,  N_TAXBAL_LAST,  N_TAX,  N_TAX_BAL,  N_KMBAL_LAST,  N_VAL_MONEY,  N_KM_BAL,  N_PORT_COST,  N_PORT_PRICE, C_DT_CODE, C_FEE_CODE,  C_TAX_TYPE,  N_INITGZ_BAL,C_DAI_CODE  FROM (SELECT P.*,  RANK() OVER(PARTITION BY C_SEC_CODE, C_SEC_VAR_CODE, C_DV_INVEST_CLS,  C_DTA_CODE, C_DV_ISSUE_MODE, C_MKT_CODE, C_TAX_TYPE ORDER BY C_TAX_WAY DESC)  AS RN FROM (SELECT C_SEC_CODE,C_SEC_VAR_CODE,C_DV_INVEST_CLS,C_DTA_CODE,C_DV_ISSUE_MODE,C_MKT_CODE,C_TAX_WAY,N_BASE,N_BASE_BAL,N_TAXBAL_LAST,N_TAX,N_TAX_BAL,N_KMBAL_LAST,N_VAL_MONEY,N_KM_BAL,N_PORT_COST,N_PORT_PRICE,C_DT_CODE,C_FEE_CODE,C_TAX_TYPE,N_INITGZ_BAL,C_DAI_CODE,N_AMOUNT  FROM (  SELECT T.C_SEC_CODE AS C_SEC_CODE,  CASE WHEN NVL(TRANS.C_TRANSFER_VAR_CODE,' ') != ' ' THEN TRANS.C_TRANSFER_VAR_CODE  ELSE T.C_SEC_VAR_CODE END AS C_SEC_VAR_CODE,   T.C_DV_INVEST_CLS AS C_DV_INVEST_CLS,  T.C_DTA_CODE AS C_DTA_CODE,  T.C_DV_ISSUE_MODE AS C_DV_ISSUE_MODE,  T.C_MKT_CODE AS C_MKT_CODE,  T.C_TAX_WAY AS C_TAX_WAY,  0.0 AS N_BASE,  0.0 AS N_BASE_BAL,  0.0 AS N_TAXBAL_LAST,  0.0 AS N_TAX,  0.0 AS N_TAX_BAL,  0.0 AS N_KMBAL_LAST,  0.0 AS N_VAL_MONEY,  0.0 AS N_KM_BAL,  0.0 AS N_PORT_COST,  0.0 AS N_PORT_PRICE, ' ' AS C_DT_CODE, 'ZZS_XX_ZR' AS C_FEE_CODE,  'XXS_QD' AS C_TAX_TYPE  , 0.01 AS N_INITGZ_BAL,C_DAI_CODE,N_AMOUNT  FROM (  SELECT R.C_SEC_CODE,  R.C_DV_INVEST_CLS, R.C_DV_ISSUE_MODE, R.C_DTA_CODE,  R.C_SEC_VAR_CODE, R.C_MKT_CODE, R.C_TAX_WAY, SUM(N_AMOUNT) AS N_AMOUNT, 'ZZS_XX_ZR' AS C_FEE_CODE, (CASE WHEN C_DAI_CODE = 'ZQFZ_CB' THEN 'ZQFZ_GYBD' ELSE ' ' END) AS C_DAI_CODE FROM (  SELECT A.C_DAI_CODE,A.C_SEC_CODE, A.C_DV_INVEST_CLS,A.C_DV_ISSUE_MODE,A.C_DTA_CODE, A.C_SEC_VAR_CODE, A.C_MKT_CODE, A.N_AMOUNT * A.N_WAY * B.N_FUND_WAY AS N_AMOUNT, CASE  WHEN A.C_DT_CODE IN (SELECT C_CODE FROM R_S_FAST_CODE WHERE C_TYPE = $3) THEN 'M_TAX'  WHEN R.C_VALUE5 IS NOT NULL THEN R.C_VALUE5  WHEN A.NEWC_SEC_VAR_CODE IN ('ZQ_QD','ZQ_QYGSZ','GP_GQ_ZPZR','ZQ_BZHPJ','GP_YXG','ZQ_JRJG','GP_GQ','ZQ_CTJRZ','ZQ_JRZ_JW','ZQ_ZQGSDQRZQ','ZQ_ZCZQH','ZQ_SMZQ_KZH','ZQ_MYZ','ZQ_ZJZ','ZQ_DFZFZ','ZQ_QYZ','ZQ_SMZQ_KJH','ZQ_YXZ','ZQ_TYCD','ZQ_YXZQ_ZW','ZQ_DECD','GP_GP_KCB','ZQ_ZCZQH_ZJ','ZQ_YXZQ_QY','ZQ_QT','ZQ_ZQJH','ZQ_YXG','ZQ_BXGSZ','ZQ_TYCD_JW','ZQ_KJHZ','ZQ_JRZ','ZQ_JHPJ','ZQ_KZZ_DX','ZQ_CJZ_ZC','ZQ_PRB','ZQ_CJZ_YH','GP_FDCXT','ZQ_DQRZZ','ZQ_SYPJ','ZQ_CT','ZQ_ZCZQH_LH','ZQ_ZCZCPJ_ZJ','ZQ_DFZFZ_ZFZCZQ','ZQ_PPN','ZQ_CJZ_BXZW','ZQ_ZCZCPJ_YX','ZQ_ZQGSZ','ZQ_SYYH_EJZBZ','GP_GQ_A','GP_GQ_B','ZQ_JHQYZ','ZQ_JRZ_FYH','GP_CT','ZQ_SMZQ','ZQ_KZZ_SC','ZQ_SMZQ_BGCZ','ZQ_KZZ_TS','ZQ_YBQYZ','ZQ_PRN','ZQ_GZXQ','ZQ_KFLZ','ZQ_ZCZCPJ','ZQ_XDZCLZ','ZQ_ZFZCJGZ','ZQ_GZXQ_PZS','ZQ_RMBZ_JW','GP_GP_CYB','ZQ_JRZ_ZC','ZQ_GSZ','ZQ_JRZ_YH','GP_GSGQ','ZQ_GJJGZ','ZQ_ZCZCPJ_LH','ZQ_GKZ','GP_CT_KCB','GP_GQ_YXG','ZQ_YHPJ','ZQ_SYCDPJ','GP_GP','ZQ_KZZ','ZQ_DQRZZ_C','ZQ_ZCZHLH','ZQ_ZQPJ','ZQ_OZMYZ','ZQ_DXGJ','ZQ_CJZ_YHZW','ZQ_CJZ','GP_TSGP','ZQ_ZBBC')  THEN 'M_TAX'  ELSE 'Y_TAX' END AS C_TAX_WAY FROM ( \tSELECT   CASE \tWHEN NVL(TRANS.C_TRANSFER_VAR_CODE,' ') != ' ' THEN TRANS.C_TRANSFER_VAR_CODE \t\t\t\tELSE ST.C_SEC_VAR_CODE END AS NEWC_SEC_VAR_CODE,  \t\tST.*  \tFROM R_D_AI_ACT_VAL ST    \t\tLEFT JOIN T_D_MP_SEC_TRANSFER TRANS ON ST.C_SEC_CODE=TRANS.C_SEC_CODE AND TRANS.C_TRANSFER_CODE ='TRAN_ZZSPLPZ' AND TRANS.N_CHECK_STATE = 1 AND TRANS.C_TYPE = 'P' AND TRANS.C_TRANSFER_VAR_CODE != ' '    WHERE ST.C_DVA_ITEM_CODE NOT IN (SELECT C_DVA_ITEM_CODE FROM T_S_DVA_ITEM T WHERE T.C_DVA_ITEM_CODE_P = 'VATJT_ACTVAL')  AND ST.C_DVA_ITEM_CODE != 'VATJT_ACTVAL'  ) A JOIN (SELECT C_DAI_CODE, N_FUND_WAY FROM T_S_DAI_ITEM B) B ON A.C_DAI_CODE = B.C_DAI_CODE  LEFT JOIN R_D_ACT_AIDE R   ON R.C_VALUE1 = A.C_SEC_CODE  AND R.C_VALUE2 = A.C_SEC_VAR_CODE  AND R.C_VALUE3 = A.C_MKT_CODE  AND R.C_VALUE4 = A.C_DV_INVEST_CLS  WHERE A.C_PORT_CODE = $4 AND A.D_CHK_ACC > $5  AND A.D_CHK_ACC <= $6  AND A.C_DAI_CODE IN ('ZQTZ_CB','YSGJ_CB', 'ZQFZ_CB') AND A.NEWC_SEC_VAR_CODE IN (SELECT ID_D_AC_TD_IVT FROM R_D_FEE_ID D)  AND NOT EXISTS (SELECT 1 FROM R_D_VAT_VAL O WHERE A.C_SEC_VAR_CODE = O.C_SEC_VAR_CODE AND A.C_MKT_CODE = O.C_MKT_CODE AND O.C_BOOK_TYPE = 'JR') AND A.C_DT_CODE NOT IN (SELECT C_VALUE1 FROM R_D_ACT_AIDE)  AND NOT EXISTS     (select 1             from R_D_ACT_AIDE R   where R.C_VALUE1 = A.C_SEC_CODE   AND R.C_VALUE2 = A.C_SEC_VAR_CODE   AND R.C_VALUE3 = A.C_MKT_CODE   AND R.C_VALUE4 = A.C_DV_INVEST_CLS  AND R.C_VALUE5 = 'B_TAX')  UNION ALL  SELECT A.C_DAI_CODE,A.C_SEC_CODE, A.C_DV_INVEST_CLS, A.C_DV_ISSUE_MODE,A.C_DTA_CODE, A.C_SEC_VAR_CODE, A.C_MKT_CODE, A.N_AMOUNT * B.N_FUND_WAY AS N_AMOUNT, CASE  WHEN R.C_VALUE5 IS NOT NULL THEN R.C_VALUE5  WHEN A.NEWC_SEC_VAR_CODE IN ('ZQ_QD','ZQ_QYGSZ','GP_GQ_ZPZR','ZQ_BZHPJ','GP_YXG','ZQ_JRJG','GP_GQ','ZQ_CTJRZ','ZQ_JRZ_JW','ZQ_ZQGSDQRZQ','ZQ_ZCZQH','ZQ_SMZQ_KZH','ZQ_MYZ','ZQ_ZJZ','ZQ_DFZFZ','ZQ_QYZ','ZQ_SMZQ_KJH','ZQ_YXZ','ZQ_TYCD','ZQ_YXZQ_ZW','ZQ_DECD','GP_GP_KCB','ZQ_ZCZQH_ZJ','ZQ_YXZQ_QY','ZQ_QT','ZQ_ZQJH','ZQ_YXG','ZQ_BXGSZ','ZQ_TYCD_JW','ZQ_KJHZ','ZQ_JRZ','ZQ_JHPJ','ZQ_KZZ_DX','ZQ_CJZ_ZC','ZQ_PRB','ZQ_CJZ_YH','GP_FDCXT','ZQ_DQRZZ','ZQ_SYPJ','ZQ_CT','ZQ_ZCZQH_LH','ZQ_ZCZCPJ_ZJ','ZQ_DFZFZ_ZFZCZQ','ZQ_PPN','ZQ_CJZ_BXZW','ZQ_ZCZCPJ_YX','ZQ_ZQGSZ','ZQ_SYYH_EJZBZ','GP_GQ_A','GP_GQ_B','ZQ_JHQYZ','ZQ_JRZ_FYH','GP_CT','ZQ_SMZQ','ZQ_KZZ_SC','ZQ_SMZQ_BGCZ','ZQ_KZZ_TS','ZQ_YBQYZ','ZQ_PRN','ZQ_GZXQ','ZQ_KFLZ','ZQ_ZCZCPJ','ZQ_XDZCLZ','ZQ_ZFZCJGZ','ZQ_GZXQ_PZS','ZQ_RMBZ_JW','GP_GP_CYB','ZQ_JRZ_ZC','ZQ_GSZ','ZQ_JRZ_YH','GP_GSGQ','ZQ_GJJGZ','ZQ_ZCZCPJ_LH','ZQ_GKZ','GP_CT_KCB','GP_GQ_YXG','ZQ_YHPJ','ZQ_SYCDPJ','GP_GP','ZQ_KZZ','ZQ_DQRZZ_C','ZQ_ZCZHLH','ZQ_ZQPJ','ZQ_OZMYZ','ZQ_DXGJ','ZQ_CJZ_YHZW','ZQ_CJZ','GP_TSGP','ZQ_ZBBC')  THEN 'M_TAX'  ELSE 'Y_TAX' END AS C_TAX_WAY  FROM (   \tSELECT CASE  \tWHEN NVL(TRANS.C_TRANSFER_VAR_CODE,' ') != ' ' THEN TRANS.C_TRANSFER_VAR_CODE \t\t\t\tELSE ST.C_SEC_VAR_CODE END AS NEWC_SEC_VAR_CODE,  \t\tST.*  \tFROM T_D_AI_STOCK ST    \t\tLEFT JOIN T_D_MP_SEC_TRANSFER TRANS ON ST.C_SEC_CODE=TRANS.C_SEC_CODE AND TRANS.C_TRANSFER_CODE ='TRAN_ZZSPLPZ' AND TRANS.N_CHECK_STATE = 1 AND TRANS.C_TYPE = 'P' AND TRANS.C_TRANSFER_VAR_CODE != ' '  ) A   JOIN (SELECT C_DAI_CODE, N_FUND_WAY FROM T_S_DAI_ITEM B) B ON A.C_DAI_CODE = B.C_DAI_CODE  LEFT JOIN R_D_ACT_AIDE R   ON R.C_VALUE1 = A.C_SEC_CODE  AND R.C_VALUE2 = A.C_SEC_VAR_CODE  AND R.C_VALUE3 = A.C_MKT_CODE  AND R.C_VALUE4 = A.C_DV_INVEST_CLS    WHERE A.C_PORT_CODE = $7 AND A.C_DAI_CODE IN ('ZQTZ_CB','YSGJ_CB','ZQFZ_CB') AND A.D_STOCK = $8 AND a.c_year_month = $9  AND A.NEWC_SEC_VAR_CODE IN (SELECT ID_D_AC_TD_IVT FROM R_D_FEE_ID R)  AND NOT EXISTS (SELECT 1 FROM R_D_VAT_VAL O WHERE A.C_SEC_VAR_CODE = O.C_SEC_VAR_CODE AND A.C_MKT_CODE = O.C_MKT_CODE AND O.C_BOOK_TYPE = 'JR')   AND NOT EXISTS\t\t\t\t\t\t\t\t\t     (select 1                                                    from R_D_ACT_AIDE R                                where R.C_VALUE1 = A.C_SEC_CODE                       AND R.C_VALUE2 = A.C_SEC_VAR_CODE                   AND R.C_VALUE3 = A.C_MKT_CODE                       AND R.C_VALUE4 = A.C_DV_INVEST_CLS                  AND R.C_VALUE5 = 'B_TAX')              ) R   GROUP BY R.C_SEC_CODE, R.C_DV_INVEST_CLS, R.C_DV_ISSUE_MODE, R.C_DTA_CODE,  R.C_SEC_VAR_CODE, R.C_MKT_CODE, R.C_TAX_WAY, R.C_DAI_CODE  ) T  LEFT JOIN T_D_MP_SEC_TRANSFER TRANS ON T.C_SEC_CODE=TRANS.C_SEC_CODE  AND TRANS.C_TRANSFER_CODE ='TRAN_ZZSPLPZ' AND TRANS.N_CHECK_STATE = 1 AND TRANS.C_TYPE = 'P' AND TRANS.C_TRANSFER_VAR_CODE != ' ') T  WHERE NOT EXISTS (SELECT 1 FROM R_R_FR_VAT_ZR TT WHERE TT.C_PORT_CODE = $10  AND TT.D_BOOK_DATE = $11 AND  TT.C_TAX_TYPE = 'XXS_QD' AND T.C_SEC_CODE = TT.C_SEC_CODE  AND (CASE WHEN TT.C_SEC_VAR_CODE LIKE 'HH%' THEN TT.C_DV_INVEST_CLS ELSE T.C_DV_INVEST_CLS END) = TT.C_DV_INVEST_CLS  AND (CASE WHEN TT.C_SEC_VAR_CODE LIKE 'HH%' THEN TT.C_DV_ISSUE_MODE ELSE T.C_DV_ISSUE_MODE END) = TT.C_DV_ISSUE_MODE  AND T.C_DTA_CODE = TT.C_DTA_CODE   AND T.C_SEC_VAR_CODE = TT.C_SEC_VAR_CODE  AND T.C_MKT_CODE = TT.C_MKT_CODE  AND T.C_FEE_CODE = TT.C_FEE_CODE ) AND T.N_AMOUNT <> 0  AND T.C_MKT_CODE NOT IN (SELECT C_MKT_CODE FROM T_S_VAT_MKTFILTER R)  ) P)T WHERE RN = 1) A  ";
        String operationType ="UNKNOWN";
        String tableName = "UNKNOWN";
        if (query.toLowerCase().contains("select")) {
                        operationType = "SELECT";
                        // 使用正则提取FROM后的表名
                        java.util.regex.Pattern fromPattern = java.util.regex.Pattern.compile("from\\s+([\\w\\.]+)", java.util.regex.Pattern.CASE_INSENSITIVE);
                        java.util.regex.Matcher matcher = fromPattern.matcher(query);
                        Set<String> tableNames = new HashSet<>();
                        while (matcher.find()) {
                            String table = matcher.group(1).toLowerCase();
                            if (table.contains(".")) {
                                table = table.substring(table.lastIndexOf(".") + 1);
                            }
                            tableNames.add(table);
                        }
                        
                        // 打印解析出的表
                        System.out.printf("解析出的表: %s%n", tableNames);
                        
                        tableName = tableNames.isEmpty() ? query.toLowerCase() : tableNames.stream().sorted().collect(Collectors.joining(","));
                    }            
    }
    
}
