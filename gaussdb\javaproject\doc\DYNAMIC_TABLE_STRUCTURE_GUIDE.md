# 动态表结构压力测试指南

## 概述

压力测试程序现在支持动态表结构，可以自动适应任何表结构，无需硬编码字段定义。程序通过数据库元数据视图自动获取表结构信息，并动态生成SQL语句和测试数据。

## 核心特性

### 1. 动态表结构发现
- 通过数据库元数据视图自动获取表结构
- 支持多种数据库类型（GaussDB、Oracle、MySQL、PostgreSQL）
- 缓存表结构信息以提高性能

### 2. 动态SQL生成
- 根据表结构自动生成INSERT、SELECT、UPDATE、DELETE语句
- 智能参数设置，支持各种数据类型
- 自动处理类型转换和空值

### 3. 智能数据生成
- 根据列名和数据类型智能生成测试数据
- 支持特殊列名的定制化数据生成
- 自动处理日期时间、数值、字符串等类型

## 关键组件

### 1. TableStructureManager
负责从数据库元数据视图获取表结构信息：

```java
// 获取表结构
TableStructure structure = TableStructureManager.getTestDataTableStructure(connection);

// 验证表结构一致性
boolean consistent = TableStructureManager.validateTableStructuresConsistent(connection);
```

### 2. DynamicSqlBuilder
根据表结构动态构建SQL语句：

```java
// 构建INSERT语句
String insertSql = DynamicSqlBuilder.buildInsertSql(tableStructure);

// 构建带分页的SELECT语句
String selectSql = DynamicSqlBuilder.buildSelectSqlWithPaging(
    tableStructure, "c_port_code = ?", "id", 100, 0);

// 动态设置参数
DynamicSqlBuilder.setParametersFromResultSet(stmt, rs, tableStructure);
```

### 3. DynamicDataGenerator
智能生成测试数据：

```java
// 生成并插入测试数据
DynamicDataGenerator.generateAndInsertTestData(
    connection, tableStructure, portCodes, recordsPerPort);

// 验证表结构是否适合压力测试
boolean suitable = DynamicDataGenerator.validateTableForStressTesting(tableStructure);
```

## 表结构要求

### 必需列
1. **分组列**: `c_port_code` - 用于并发分组
2. **主键列**: `id` 或以 `_id` 结尾的列 - 用于数据标识

### 支持的数据类型
- **整数类型**: INT, BIGINT, SMALLINT, TINYINT
- **小数类型**: DECIMAL, NUMERIC, FLOAT, DOUBLE
- **字符串类型**: VARCHAR, CHAR, TEXT, STRING
- **日期时间类型**: DATE, TIME, TIMESTAMP, DATETIME

### 智能列名识别
程序会根据列名自动识别用途：

| 列名模式 | 数据生成策略 |
|---------|-------------|
| `id`, `*_id` | 顺序ID |
| `c_port_code`, `*port_code*` | 使用传入的端口代码 |
| `*status*` | 随机整数(0-2) |
| `*create_time*`, `*createtime*` | 当前时间戳 |
| `*update_time*`, `*updatetime*` | 当前时间戳 |
| `*description*`, `*desc*` | 随机描述文本 |
| `*data_value*`, `*value*` | 随机数据值 |

## 配置说明

### 表名配置
在 `connection-pool.properties` 中配置表名：

```properties
# 压力测试表名配置
stress.test.table.testData=your_test_data_table
stress.test.table.temp=your_temp_table
stress.test.table.execution=your_execution_table
```

### 数据库元数据配置
程序自动根据数据库类型选择合适的元数据视图：

- **GaussDB/OpenGauss**: `information_schema.columns`
- **Oracle**: `user_tab_columns`
- **MySQL**: `information_schema.columns`
- **PostgreSQL**: `information_schema.columns`

## 使用方法

### 1. 准备表结构
确保三张表具有相同的结构，并包含必需的列：

```sql
-- 示例表结构
CREATE TABLE your_test_data_table (
    id BIGINT PRIMARY KEY,
    c_port_code VARCHAR(50) NOT NULL,
    data_value VARCHAR(200),
    description TEXT,
    status INTEGER,
    create_time TIMESTAMP,
    update_time TIMESTAMP
);

-- 创建相同结构的其他两张表
CREATE TABLE your_temp_table AS SELECT * FROM your_test_data_table WHERE 1=0;
CREATE TABLE your_execution_table AS SELECT * FROM your_test_data_table WHERE 1=0;
```

### 2. 配置表名
更新 `connection-pool.properties` 文件中的表名配置。

### 3. 运行压力测试
```bash
# 使用动态表结构运行压力测试
./run-stress-test.sh --init-data -c 10 -p 50 -r 100 -b 25
```

## 优势

### 1. 灵活性
- 支持任意表结构，无需修改代码
- 自动适应不同的数据库和表设计
- 支持新增或删除列而无需代码变更

### 2. 智能化
- 根据列名和类型智能生成合适的测试数据
- 自动处理数据类型转换和参数设置
- 智能识别特殊用途的列

### 3. 可维护性
- 减少硬编码，提高代码可维护性
- 统一的表结构管理和SQL生成
- 清晰的组件分离和职责划分

### 4. 性能
- 表结构信息缓存，避免重复查询
- 批量数据处理，提高插入效率
- 流式批处理，减少内存占用

## 故障排除

### 1. 表结构不一致
```
ERROR: Table structures are not consistent
```
**解决方案**: 确保三张表具有完全相同的结构。

### 2. 缺少必需列
```
ERROR: Table does not contain required column 'c_port_code'
```
**解决方案**: 确保表中包含 `c_port_code` 列用于分组。

### 3. 元数据访问权限
```
ERROR: No columns found for table
```
**解决方案**: 确保数据库用户有权限访问元数据视图。

### 4. 数据类型不支持
```
WARN: Failed to set parameter with specific type, falling back to string
```
**解决方案**: 程序会自动回退到字符串类型，通常不影响功能。

## 扩展指南

### 1. 添加新数据库支持
在 `DatabaseConnectionConfig` 中添加新的元数据配置：

```java
case "NEWDB":
    return new MetadataConfig(
        "information_schema.columns",
        "column_name",
        "data_type", 
        "ordinal_position",
        "table_name"
    );
```

### 2. 自定义数据生成策略
在 `DynamicDataGenerator` 中添加新的列名识别规则：

```java
else if (columnName.contains("your_pattern")) {
    stmt.setString(paramIndex, generateYourCustomData());
}
```

### 3. 添加新数据类型支持
在 `DynamicSqlBuilder` 中扩展类型判断逻辑：

```java
public boolean isYourCustomType() {
    String type = dataType.toUpperCase();
    return type.contains("YOUR_TYPE");
}
```

## 最佳实践

1. **表设计**: 使用标准的列命名约定，便于程序智能识别
2. **索引优化**: 在 `c_port_code` 列上创建索引以提高查询性能
3. **权限管理**: 确保数据库用户有足够权限访问元数据视图
4. **监控**: 使用程序提供的性能监控功能观察测试结果
5. **测试**: 在生产环境使用前，先在测试环境验证表结构兼容性

通过动态表结构功能，压力测试程序现在可以适应各种不同的表设计，大大提高了程序的通用性和可维护性。
