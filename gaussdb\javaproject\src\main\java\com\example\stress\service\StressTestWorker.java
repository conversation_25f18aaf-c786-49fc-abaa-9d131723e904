package com.example.stress.service;

import com.example.stress.dao.TestDataDao;
import com.example.stress.monitor.PerformanceMonitor;
import com.example.util.DatabaseUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.concurrent.Callable;

/**
 * 压力测试工作线程
 * 执行单个port code的delete、insert、update操作
 */
public class StressTestWorker implements Callable<StressTestResult> {
    private static final Logger logger = LoggerFactory.getLogger(StressTestWorker.class);
    
    private final String portCode;
    private final TestDataDao testDataDao;
    private final PerformanceMonitor performanceMonitor;
    private final int workerId;
    private final int batchSize;

    public StressTestWorker(String portCode, int workerId, int batchSize) {
        this.portCode = portCode;
        this.workerId = workerId;
        this.batchSize = batchSize;
        this.testDataDao = new TestDataDao();
        this.performanceMonitor = new PerformanceMonitor();
    }
    
    @Override
    public StressTestResult call() throws Exception {
        logger.info("Worker {} starting stress test for port code: {}", workerId, portCode);
        
        StressTestResult result = new StressTestResult(portCode, workerId);
        long startTime = System.currentTimeMillis();
        
        try (Connection connection = DatabaseUtil.getConnection()) {
            // 设置手动提交，确保事务控制
            connection.setAutoCommit(false);
            
            try {
                // 步骤1: 删除执行表中的数据
                long deleteStartTime = System.currentTimeMillis();
                int deletedRows = performDelete(connection);
                // 提交事务
                connection.commit();
                long deleteEndTime = System.currentTimeMillis();
                
                result.setDeletedRows(deletedRows);
                result.setDeleteTime(deleteEndTime - deleteStartTime);
                
                // 步骤2: 从测试数据表获取数据并插入执行表
                long insertStartTime = System.currentTimeMillis();
                int insertedRows = performInsert(connection);
                long insertEndTime = System.currentTimeMillis();
                
                result.setInsertedRows(insertedRows);
                result.setInsertTime(insertEndTime - insertStartTime);
                
                
                // 步骤3: 更新执行表中的数据
                long updateStartTime = System.currentTimeMillis();
                int updatedRows = performUpdate(connection);
                long updateEndTime = System.currentTimeMillis();
                
                result.setUpdatedRows(updatedRows);
                result.setUpdateTime(updateEndTime - updateStartTime);
                
                result.setSuccess(true);
                
                logger.info("Worker {} completed successfully for port code: {} (D:{}, I:{}, U:{})", 
                           workerId, portCode, deletedRows, insertedRows, updatedRows);
                
            } catch (Exception e) {
                // 回滚事务
                try {
                    connection.rollback();
                } catch (SQLException rollbackEx) {
                    logger.error("Failed to rollback transaction for worker {}", workerId, rollbackEx);
                }
                
                result.setSuccess(false);
                result.setErrorMessage(e.getMessage());
                logger.error("Worker {} failed for port code: {}", workerId, portCode, e);
            } finally {
                connection.setAutoCommit(true);
            }
            
        } catch (SQLException e) {
            result.setSuccess(false);
            result.setErrorMessage("Database connection failed: " + e.getMessage());
            logger.error("Worker {} database connection failed for port code: {}", workerId, portCode, e);
        }
        
        long endTime = System.currentTimeMillis();
        result.setTotalTime(endTime - startTime);
        
        return result;
    }
    
    /**
     * 执行删除操作
     */
    private int performDelete(Connection connection) throws SQLException {
        logger.debug("Worker {} deleting data for port code: {}", workerId, portCode);
        
        long startTime = System.nanoTime();
        int deletedRows = testDataDao.deleteByPortCode(connection, portCode, batchSize);
        long endTime = System.nanoTime();
        
        performanceMonitor.recordDeleteOperation(endTime - startTime, deletedRows > 0);
        
        logger.debug("Worker {} deleted {} rows for port code: {}", workerId, deletedRows, portCode);
        return deletedRows;
    }
    
    /**
     * 执行插入操作（分批次处理）
     */
    private int performInsert(Connection connection) throws SQLException {
        logger.debug("Worker {} inserting data for port code: {} with batch size: {}", workerId, portCode, batchSize);

        long startTime = System.nanoTime();
        int totalInserted = 0;

        try {
            // 步骤1: 清空临时表中该port_code的数据
            // 使用全局会话级临时表，不需要主动删除
            // testDataDao.clearTempTableByPortCode(connection, portCode);

            // 步骤2: 分批次处理 - 每批次插入临时表后立即转移到执行表
            int offset = 0;
            int batchInserted;
            int totalExecutionInserted = 0;

            do {
                // 2.1: 从测试数据表分批次插入到临时表
                batchInserted = testDataDao.batchInsertToTempTable(connection, portCode, batchSize, offset);
                totalInserted += batchInserted;

                if (batchInserted > 0) {
                    // 2.2: 立即将该批次从临时表转移到执行表
                    int batchExecutionInserted = testDataDao.batchInsertFromTempToExecution(connection, portCode, offset);
                    totalExecutionInserted += batchExecutionInserted;

                    logger.debug("Worker {} processed batch: {} rows temp → {} rows execution (offset: {})",
                               workerId, batchInserted, batchExecutionInserted, offset);

                    // 2.3: 清空临时表中该port_code的数据为下一批次做准备
                    // 使用全局会话级临时表，不需要主动删除
                    // testDataDao.clearTempTableByPortCode(connection, portCode);
                }

                offset += batchSize;

            } while (batchInserted == batchSize); // 继续直到没有更多数据

            if (totalInserted == 0) {
                logger.warn("Worker {} found no test data for port code: {}", workerId, portCode);
                return 0;
            }

            long endTime = System.nanoTime();
            performanceMonitor.recordInsertOperation(endTime - startTime, true);

            logger.debug("Worker {} completed batch insert: {} rows processed, {} rows inserted to execution table",
                       workerId, totalInserted, totalExecutionInserted);

            return totalExecutionInserted;

        } catch (SQLException e) {
            long endTime = System.nanoTime();
            performanceMonitor.recordInsertOperation(endTime - startTime, false);
            throw e;
        }
    }
    
    /**
     * 执行更新操作
     */
    private int performUpdate(Connection connection) throws SQLException {
        logger.debug("Worker {} updating data for port code: {}", workerId, portCode);
        
        long startTime = System.nanoTime();
        
        // 更新状态为2（表示已处理）
        int newStatus = 2;
        int updatedRows = testDataDao.updateStatusByPortCode(connection, portCode, newStatus, batchSize);
        
        long endTime = System.nanoTime();
        performanceMonitor.recordUpdateOperation(endTime - startTime, updatedRows > 0);
        
        logger.debug("Worker {} updated {} rows for port code: {}", workerId, updatedRows, portCode);
        return updatedRows;
    }
    
    public PerformanceMonitor getPerformanceMonitor() {
        return performanceMonitor;
    }
}
