# 开发环境配置示例
# Development Environment Configuration Example

# ============================================================================
# 数据库连接配置 Database Connection Configuration
# ============================================================================

# 默认数据库连接配置（开发GaussDB）
database.default.url=*********************************************************************************************************
database.default.username=dev_user
database.default.password=dev_password
database.default.type=GAUSSDB

# 源数据库连接配置（开发环境测试数据）
database.source.url=**********************************************************************************************************
database.source.username=test_user
database.source.password=test_password
database.source.type=GAUSSDB

# StarRocks数据库连接配置（本地StarRocks）
database.starrocks.url=*****************************************
database.starrocks.username=root
database.starrocks.password=
database.starrocks.type=STARROCKS

# MySQL数据库连接配置（本地MySQL）
database.mysql.url=****************************************************************************
database.mysql.username=root
database.mysql.password=root
database.mysql.type=MYSQL

# ============================================================================
# 元数据视图配置 Metadata View Configuration
# ============================================================================

# GaussDB元数据视图配置
metadata.gaussdb.view=v_table_metadata
metadata.gaussdb.schema=public
metadata.gaussdb.table_name_column=table_name
metadata.gaussdb.column_name_column=column_name
metadata.gaussdb.data_type_column=data_type
metadata.gaussdb.ordinal_position_column=ordinal_position

# StarRocks元数据视图配置
metadata.starrocks.view=columns
metadata.starrocks.schema=information_schema
metadata.starrocks.table_name_column=table_name
metadata.starrocks.column_name_column=column_name
metadata.starrocks.data_type_column=data_type
metadata.starrocks.ordinal_position_column=ordinal_position

# MySQL元数据视图配置
metadata.mysql.view=columns
metadata.mysql.schema=information_schema
metadata.mysql.table_name_column=table_name
metadata.mysql.column_name_column=column_name
metadata.mysql.data_type_column=data_type
metadata.mysql.ordinal_position_column=ordinal_position

# ============================================================================
# 连接池配置 Connection Pool Configuration
# ============================================================================

# 启用连接池
connection.pool.enabled=true

# 开发环境连接池配置（较小的连接池）
connection.pool.default.maximumPoolSize=10
connection.pool.default.minimumIdle=2
connection.pool.default.connectionTimeout=30000
connection.pool.default.idleTimeout=600000
connection.pool.default.maxLifetime=1800000
connection.pool.default.autoCommit=false
connection.pool.default.leakDetectionThreshold=60000

# GaussDB连接池配置
connection.pool.gaussdb.maximumPoolSize=15
connection.pool.gaussdb.minimumIdle=3
connection.pool.gaussdb.connectionTimeout=30000
connection.pool.gaussdb.idleTimeout=600000
connection.pool.gaussdb.maxLifetime=1800000
connection.pool.gaussdb.autoCommit=false
connection.pool.gaussdb.leakDetectionThreshold=60000

# StarRocks连接池配置
connection.pool.starrocks.maximumPoolSize=8
connection.pool.starrocks.minimumIdle=2
connection.pool.starrocks.connectionTimeout=45000
connection.pool.starrocks.idleTimeout=300000
connection.pool.starrocks.maxLifetime=1200000
connection.pool.starrocks.autoCommit=false
connection.pool.starrocks.leakDetectionThreshold=60000

# MySQL连接池配置
connection.pool.mysql.maximumPoolSize=10
connection.pool.mysql.minimumIdle=2
connection.pool.mysql.connectionTimeout=30000
connection.pool.mysql.idleTimeout=600000
connection.pool.mysql.maxLifetime=1800000
connection.pool.mysql.autoCommit=false
connection.pool.mysql.leakDetectionThreshold=60000

# 连接池监控配置
connection.pool.monitoring.enabled=true
connection.pool.monitoring.interval=60000

# 数据源特定配置（开发环境）
connection.pool.cachePrepStmts=true
connection.pool.prepStmtCacheSize=100
connection.pool.prepStmtCacheSqlLimit=2048
connection.pool.useServerPrepStmts=true
connection.pool.useLocalSessionState=true
connection.pool.rewriteBatchedStatements=true
connection.pool.cacheResultSetMetadata=true
connection.pool.cacheServerConfiguration=true
connection.pool.elideSetAutoCommits=true
connection.pool.maintainTimeStats=false
