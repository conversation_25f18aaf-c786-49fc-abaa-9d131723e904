package com.example.stress.util;

import com.example.stress.config.UpdateFieldConfig;
import com.example.stress.model.TableStructure;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * SQL缓存管理器
 * 在压力测试准备阶段预先构建和缓存所有SQL语句
 */
public class SqlCacheManager {
    private static final Logger logger = LoggerFactory.getLogger(SqlCacheManager.class);
    
    // SQL缓存
    private static final Map<String, String> sqlCache = new ConcurrentHashMap<>();
    
    // 缓存是否已初始化
    private static volatile boolean initialized = false;
    
    /**
     * 初始化SQL缓存
     * 在压力测试开始前调用
     */
    public static boolean initializeSqlCache(Connection connection, int batchSize) {
        if (initialized) {
            logger.info("SQL cache already initialized");
            return true;
        }
        
        try {
            logger.info("Initializing SQL cache...");
            
            // 获取表结构
            TableStructure testDataStructure = TableStructureManager.getTestDataTableStructure(connection);
            TableStructure tempStructure = TableStructureManager.getTempTableStructure(connection);
            TableStructure executionStructure = TableStructureManager.getExecutionTableStructure(connection);
            
            // 获取主键列名
            String executionPrimaryKey = DynamicSqlBuilder.getPrimaryKeyColumn(executionStructure);
            String testDataPrimaryKey = DynamicSqlBuilder.getPrimaryKeyColumn(testDataStructure);
            
            // 1. deleteByPortCodeInBatches相关SQL
            initializeDeleteSql(executionPrimaryKey, batchSize);
            
            // 2. batchInsertToTempTable相关SQL
            initializeBatchInsertToTempSql(testDataStructure, tempStructure, testDataPrimaryKey, batchSize);
            
            // 3. updateStatusByPortCodeInBatches相关SQL
            initializeUpdateStatusSql(testDataPrimaryKey, batchSize);
            
            // 4. batchInsertFromTempToExecution相关SQL
            initializeBatchInsertFromTempSql(tempStructure, executionStructure);
            
            initialized = true;
            logger.info("SQL cache initialized successfully with {} cached statements", sqlCache.size());
            
            // 打印缓存的SQL语句（调试用）
            if (logger.isDebugEnabled()) {
                printCachedSql();
            }
            
            return true;
            
        } catch (Exception e) {
            logger.error("Failed to initialize SQL cache", e);
            return false;
        }
    }
    
    /**
     * 初始化删除相关的SQL
     */
    private static void initializeDeleteSql(String primaryKeyColumn, int batchSize) throws SQLException {
        // SELECT SQL for getting primary keys
        String selectSql = String.format(
            "SELECT %s FROM %s WHERE c_port_code = ? ORDER BY %s LIMIT %d",
            primaryKeyColumn, getExecutionTable(), primaryKeyColumn, batchSize
        );
        sqlCache.put("deleteByPortCodeInBatches_select_" + batchSize, selectSql);
        
        // DELETE SQL
        String deleteSql = String.format(
            "DELETE FROM %s WHERE %s = ?",
            getExecutionTable(), primaryKeyColumn
        );
        sqlCache.put("deleteByPortCodeInBatches_delete", deleteSql);
    }
    
    /**
     * 初始化批量插入到临时表的SQL
     */
    private static void initializeBatchInsertToTempSql(TableStructure testDataStructure, 
                                                       TableStructure tempStructure,
                                                       String primaryKeyColumn, 
                                                       int batchSize) {
        // SELECT SQL with update fields as null
        String selectSql = DynamicSqlBuilder.buildSelectSqlWithPagingAndUpdateFields(
            testDataStructure, "c_port_code = ?", primaryKeyColumn, batchSize, batchSize
        );
        // 移除具体的offset，使用占位符
        selectSql = selectSql.replaceAll("LIMIT \\d+ OFFSET \\d+", "LIMIT " + batchSize + " OFFSET ?");
        sqlCache.put("batchInsertToTempTable_select_" + batchSize, selectSql);
        
        // INSERT SQL
        String insertSql = DynamicSqlBuilder.buildInsertSql(tempStructure);
        sqlCache.put("batchInsertToTempTable_insert", insertSql);
    }
    
    /**
     * 初始化更新状态的SQL
     */
    private static void initializeUpdateStatusSql(String primaryKeyColumn, int batchSize) {
        // 获取配置的更新字段
        List<String> updateFields = UpdateFieldConfig.getUpdateFields();
        
        if (!updateFields.isEmpty()) {
            // SELECT SQL
            StringBuilder selectColumns = new StringBuilder(primaryKeyColumn);
            for (String field : updateFields) {
                selectColumns.append(", ").append(field);
            }
            
            String selectSql = String.format(
                "SELECT %s FROM %s WHERE c_port_code = ? ORDER BY %s LIMIT %d OFFSET ?",
                selectColumns.toString(), getTestDataTable(), primaryKeyColumn, batchSize
            );
            sqlCache.put("updateStatusByPortCodeInBatches_select_" + batchSize, selectSql);
            
            // UPDATE SQL
            StringBuilder updateSqlBuilder = new StringBuilder("UPDATE ");
            updateSqlBuilder.append(getExecutionTable()).append(" SET ");
            for (int i = 0; i < updateFields.size(); i++) {
                if (i > 0) {
                    updateSqlBuilder.append(", ");
                }
                updateSqlBuilder.append(updateFields.get(i)).append(" = ?");
            }
            updateSqlBuilder.append(" WHERE ").append(primaryKeyColumn).append(" = ?");
            sqlCache.put("updateStatusByPortCodeInBatches_update", updateSqlBuilder.toString());
        }
    }
    
    /**
     * 初始化从临时表到执行表的批量插入SQL
     */
    private static void initializeBatchInsertFromTempSql(TableStructure tempStructure, 
                                                         TableStructure executionStructure) {
        // SELECT SQL
        String selectSql = DynamicSqlBuilder.buildSelectSql(tempStructure, "c_port_code = ?");
        sqlCache.put("batchInsertFromTempToExecution_select", selectSql);
        
        // INSERT SQL
        String insertSql = DynamicSqlBuilder.buildInsertSql(executionStructure);
        sqlCache.put("batchInsertFromTempToExecution_insert", insertSql);
    }
    
    /**
     * 获取缓存的SQL
     */
    public static String getCachedSql(String key) {
        if (!initialized) {
            throw new IllegalStateException("SQL cache not initialized. Call initializeSqlCache() first.");
        }
        return sqlCache.get(key);
    }
    
    /**
     * 检查SQL缓存是否已初始化
     */
    public static boolean isInitialized() {
        return initialized;
    }
    
    /**
     * 打印所有缓存的SQL（调试用）
     */
    private static void printCachedSql() {
        logger.debug("=== Cached SQL Statements ===");
        for (Map.Entry<String, String> entry : sqlCache.entrySet()) {
            logger.debug("{}: {}", entry.getKey(), entry.getValue());
        }
    }
    
    /**
     * 清空缓存
     */
    public static void clearCache() {
        sqlCache.clear();
        initialized = false;
        logger.info("SQL cache cleared");
    }
    
    // 辅助方法，获取表名
    private static String getExecutionTable() {
        return com.example.stress.config.TableNameConfig.getExecutionTable();
    }

    private static String getTestDataTable() {
        return com.example.stress.config.TableNameConfig.getTestDataTable();
    }

    private static String getTempTable() {
        return com.example.stress.config.TableNameConfig.getTempTable();
    }
}
