#!/bin/bash
# GaussDB 压力测试启动脚本 (Linux/Unix)
# 使用方法: ./run-stress-test.sh [参数]

echo "Starting GaussDB Stress Test..."

# 设置Java内存参数
JAVA_OPTS="-Xms512m -Xmx2g -XX:+UseG1GC"

# 检查是否存在编译后的jar文件
if [ -f "target/gaussdb-demo-1.0.0-fat.jar" ]; then
    echo "Using compiled jar file..."
    java $JAVA_OPTS -jar target/gaussdb-demo-1.0.0-fat.jar "$@"
else
    echo "Jar file not found. Please compile first with: mvn clean package"
    echo ""
    echo "Compiling now..."
    mvn clean package -DskipTests
    
    if [ -f "target/gaussdb-demo-1.0.0-fat.jar" ]; then
        echo "Compilation successful. Starting stress test..."
        java $JAVA_OPTS -jar target/gaussdb-demo-1.0.0-fat.jar "$@"
    else
        echo "Compilation failed. Please check the build output."
        exit 1
    fi
fi

echo ""
echo "Stress test completed."


java -Xms32G -Xmx32g -XX:+UseG1GC -Dlog.path=output -Dlog.name=stress -jar gaussdb-demo-1.0.0-fat.jar --no-init -f stress_java_test -c 100
java -Xms32G -Xmx32g -XX:+UseG1GC -Dlog.path=output -Dlog.name=stress -jar gaussdb-demo-1.0.0-fat.jar --no-init -f jk1000Fa42 -c 100
