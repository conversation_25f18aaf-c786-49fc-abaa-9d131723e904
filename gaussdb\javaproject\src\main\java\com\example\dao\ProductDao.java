package com.example.dao;

import com.example.entity.Product;
import com.example.impl.GaussdbTracer;
import com.example.util.DatabaseUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;
import java.util.UUID;

public class ProductDao {
    private static final Logger logger = LoggerFactory.getLogger(ProductDao.class);
    
    public boolean insertProduct(Product product) {
        String sql = "INSERT INTO products (product_no, name, price) VALUES (?, ?, ?)";
        GaussdbTracer tracer = GaussdbTracer.getInstance();
        String traceId = UUID.randomUUID().toString().replaceAll("-","" );
        tracer.setTraceId(traceId);
        try (Connection connection = DatabaseUtil.getConnection();
             PreparedStatement stmt = connection.prepareStatement(sql)) {
            
            stmt.setInt(1, product.getProductNo());
            stmt.setString(2, product.getName());
            stmt.setBigDecimal(3, product.getPrice());
            
            int rowsAffected = stmt.executeUpdate();
            tracer.reset();
            logger.info("Product inserted successfully, rows affected: {}", rowsAffected);
            return rowsAffected > 0;
            
        } catch (SQLException e) {
            logger.error("Error inserting product: {}", product, e);
            return false;
        }
    }
    
    public int batchInsertProducts(List<Product> products) {
        String sql = "INSERT INTO products (product_no, name, price) VALUES (?, ?, ?)";
        int totalInserted = 0;
        GaussdbTracer tracer = GaussdbTracer.getInstance();
        String traceId = UUID.randomUUID().toString().replaceAll("-","" );
        tracer.setTraceId(traceId);
        try (Connection connection = DatabaseUtil.getConnection();
             PreparedStatement stmt = connection.prepareStatement(sql)) {
            
            connection.setAutoCommit(false);
            
            for (Product product : products) {
                stmt.setInt(1, product.getProductNo());
                stmt.setString(2, product.getName());
                stmt.setBigDecimal(3, product.getPrice());
                stmt.addBatch();
            }
            
            int[] results = stmt.executeBatch();
            connection.commit();
            tracer.reset();
            for (int result : results) {
                if (result > 0) totalInserted++;
            }
            
            logger.info("Batch insert completed, {} products inserted", totalInserted);
            
        } catch (SQLException e) {
            logger.error("Error in batch insert", e);
        }
        
        return totalInserted;
    }
}