package com.example.config;

import com.example.util.ConnectionPoolManager.DatabaseType;
import com.example.util.ConnectionPoolManager.PoolConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * 连接池配置管理类
 * 从配置文件中读取连接池相关参数
 */
public class ConnectionPoolConfig {
    private static final Logger logger = LoggerFactory.getLogger(ConnectionPoolConfig.class);
    
    private static final String CONFIG_FILE = "connection-pool.properties";
    private static Properties properties;
    private static volatile boolean initialized = false;
    
    // 默认配置值
    private static final int DEFAULT_MAXIMUM_POOL_SIZE = 20;
    private static final int DEFAULT_MINIMUM_IDLE = 5;
    private static final long DEFAULT_CONNECTION_TIMEOUT = 30000;
    private static final long DEFAULT_IDLE_TIMEOUT = 600000;
    private static final long DEFAULT_MAX_LIFETIME = 1800000;
    private static final boolean DEFAULT_AUTO_COMMIT = false;
    private static final long DEFAULT_LEAK_DETECTION_THRESHOLD = 60000;
    
    /**
     * 初始化配置
     */
    private static void initialize() {
        if (!initialized) {
            synchronized (ConnectionPoolConfig.class) {
                if (!initialized) {
                    loadProperties();
                    initialized = true;
                }
            }
        }
    }
    
    /**
     * 加载配置文件
     */
    private static void loadProperties() {
        properties = new Properties();
        
        try (InputStream inputStream = ConnectionPoolConfig.class.getClassLoader()
                .getResourceAsStream(CONFIG_FILE)) {
            
            if (inputStream != null) {
                properties.load(inputStream);
                logger.info("Successfully loaded connection pool configuration from {}", CONFIG_FILE);
            } else {
                logger.warn("Configuration file {} not found, using default values", CONFIG_FILE);
            }
            
        } catch (IOException e) {
            logger.error("Failed to load configuration file {}, using default values", CONFIG_FILE, e);
        }
    }
    
    /**
     * 检查是否启用连接池
     */
    public static boolean isConnectionPoolEnabled() {
        initialize();
        return getBooleanProperty("connection.pool.enabled", true);
    }
    
    /**
     * 获取默认连接池配置
     */
    public static PoolConfig getDefaultPoolConfig() {
        initialize();
        return createPoolConfig("connection.pool.default");
    }
    
    /**
     * 根据数据库类型获取连接池配置
     */
    public static PoolConfig getPoolConfig(DatabaseType databaseType) {
        initialize();
        
        String prefix = "connection.pool." + databaseType.name().toLowerCase();
        PoolConfig config = createPoolConfig(prefix);
        
        // 如果特定数据库类型的配置不存在，使用默认配置
        if (!hasConfigForPrefix(prefix)) {
            logger.debug("No specific configuration found for {}, using default configuration", databaseType);
            return getDefaultPoolConfig();
        }
        
        return config;
    }
    
    /**
     * 创建连接池配置对象
     */
    private static PoolConfig createPoolConfig(String prefix) {
        PoolConfig config = new PoolConfig();
        
        config.setMaximumPoolSize(getIntProperty(prefix + ".maximumPoolSize", DEFAULT_MAXIMUM_POOL_SIZE));
        config.setMinimumIdle(getIntProperty(prefix + ".minimumIdle", DEFAULT_MINIMUM_IDLE));
        config.setConnectionTimeout(getLongProperty(prefix + ".connectionTimeout", DEFAULT_CONNECTION_TIMEOUT));
        config.setIdleTimeout(getLongProperty(prefix + ".idleTimeout", DEFAULT_IDLE_TIMEOUT));
        config.setMaxLifetime(getLongProperty(prefix + ".maxLifetime", DEFAULT_MAX_LIFETIME));
        config.setAutoCommit(getBooleanProperty(prefix + ".autoCommit", DEFAULT_AUTO_COMMIT));
        
        return config;
    }
    
    /**
     * 检查是否存在指定前缀的配置
     */
    private static boolean hasConfigForPrefix(String prefix) {
        return properties.containsKey(prefix + ".maximumPoolSize");
    }
    
    /**
     * 获取连接池监控配置
     */
    public static boolean isMonitoringEnabled() {
        initialize();
        return getBooleanProperty("connection.pool.monitoring.enabled", true);
    }
    
    /**
     * 获取监控间隔时间
     */
    public static long getMonitoringInterval() {
        initialize();
        return getLongProperty("connection.pool.monitoring.interval", 60000);
    }
    
    /**
     * 获取泄漏检测阈值
     */
    public static long getLeakDetectionThreshold(DatabaseType databaseType) {
        initialize();
        String key = "connection.pool." + databaseType.name().toLowerCase() + ".leakDetectionThreshold";
        return getLongProperty(key, DEFAULT_LEAK_DETECTION_THRESHOLD);
    }
    
    /**
     * 获取数据源特定配置
     */
    public static Properties getDataSourceProperties() {
        initialize();
        Properties dsProps = new Properties();
        
        dsProps.setProperty("cachePrepStmts", 
            getBooleanProperty("connection.pool.cachePrepStmts", true) ? "true" : "false");
        dsProps.setProperty("prepStmtCacheSize", 
            String.valueOf(getIntProperty("connection.pool.prepStmtCacheSize", 250)));
        dsProps.setProperty("prepStmtCacheSqlLimit", 
            String.valueOf(getIntProperty("connection.pool.prepStmtCacheSqlLimit", 2048)));
        dsProps.setProperty("useServerPrepStmts", 
            getBooleanProperty("connection.pool.useServerPrepStmts", true) ? "true" : "false");
        dsProps.setProperty("useLocalSessionState", 
            getBooleanProperty("connection.pool.useLocalSessionState", true) ? "true" : "false");
        dsProps.setProperty("rewriteBatchedStatements", 
            getBooleanProperty("connection.pool.rewriteBatchedStatements", true) ? "true" : "false");
        dsProps.setProperty("cacheResultSetMetadata", 
            getBooleanProperty("connection.pool.cacheResultSetMetadata", true) ? "true" : "false");
        dsProps.setProperty("cacheServerConfiguration", 
            getBooleanProperty("connection.pool.cacheServerConfiguration", true) ? "true" : "false");
        dsProps.setProperty("elideSetAutoCommits", 
            getBooleanProperty("connection.pool.elideSetAutoCommits", true) ? "true" : "false");
        dsProps.setProperty("maintainTimeStats", 
            getBooleanProperty("connection.pool.maintainTimeStats", false) ? "true" : "false");
        
        return dsProps;
    }
    
    /**
     * 获取字符串属性
     */
    private static String getStringProperty(String key, String defaultValue) {
        return properties.getProperty(key, defaultValue);
    }
    
    /**
     * 获取整数属性
     */
    private static int getIntProperty(String key, int defaultValue) {
        String value = properties.getProperty(key);
        if (value != null) {
            try {
                return Integer.parseInt(value.trim());
            } catch (NumberFormatException e) {
                logger.warn("Invalid integer value for property {}: {}, using default: {}", 
                           key, value, defaultValue);
            }
        }
        return defaultValue;
    }
    
    /**
     * 获取长整数属性
     */
    private static long getLongProperty(String key, long defaultValue) {
        String value = properties.getProperty(key);
        if (value != null) {
            try {
                return Long.parseLong(value.trim());
            } catch (NumberFormatException e) {
                logger.warn("Invalid long value for property {}: {}, using default: {}", 
                           key, value, defaultValue);
            }
        }
        return defaultValue;
    }
    
    /**
     * 获取布尔属性
     */
    private static boolean getBooleanProperty(String key, boolean defaultValue) {
        String value = properties.getProperty(key);
        if (value != null) {
            return Boolean.parseBoolean(value.trim());
        }
        return defaultValue;
    }
    
    /**
     * 重新加载配置
     */
    public static void reload() {
        synchronized (ConnectionPoolConfig.class) {
            initialized = false;
            initialize();
            logger.info("Connection pool configuration reloaded");
        }
    }
    
    /**
     * 打印当前配置
     */
    public static void printConfiguration() {
        initialize();
        logger.info("=== Connection Pool Configuration ===");
        logger.info("Connection Pool Enabled: {}", isConnectionPoolEnabled());
        logger.info("Monitoring Enabled: {}", isMonitoringEnabled());
        logger.info("Monitoring Interval: {} ms", getMonitoringInterval());
        
        for (DatabaseType dbType : DatabaseType.values()) {
            PoolConfig config = getPoolConfig(dbType);
            logger.info("{} Pool Config - MaxSize: {}, MinIdle: {}, ConnTimeout: {} ms, IdleTimeout: {} ms", 
                       dbType, config.getMaximumPoolSize(), config.getMinimumIdle(), 
                       config.getConnectionTimeout(), config.getIdleTimeout());
        }
    }
}
