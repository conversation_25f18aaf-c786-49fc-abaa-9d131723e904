package com.example.stress.service;

import com.example.stress.monitor.PerformanceMonitor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;

/**
 * 并发压力测试服务
 * 管理多个worker并发执行压力测试
 */
public class ConcurrentStressTestService {
    private static final Logger logger = LoggerFactory.getLogger(ConcurrentStressTestService.class);
    
    private final ExecutorService executorService;
    private final int concurrency;
    
    public ConcurrentStressTestService(int concurrency) {
        this.concurrency = concurrency;
        this.executorService = Executors.newFixedThreadPool(concurrency);
        logger.info("Created concurrent stress test service with {} threads", concurrency);
    }
    
    /**
     * 执行并发压力测试
     * @param portCodes 要测试的port code列表
     * @param batchSize 批次大小
     * @return 测试结果列表
     */
    public List<StressTestResult> executeStressTest(List<String> portCodes, int batchSize) {
        logger.info("Starting concurrent stress test with {} port codes and {} threads",
                   portCodes.size(), concurrency);

        long startTime = System.currentTimeMillis();
        List<Future<StressTestResult>> futures = new ArrayList<>();
        List<StressTestWorker> workers = new ArrayList<>();
        List<StressTestResult> results = new ArrayList<>();

        try {
            // 提交所有任务
            for (int i = 0; i < portCodes.size(); i++) {
                String portCode = portCodes.get(i);
                StressTestWorker worker = new StressTestWorker(portCode, i + 1, batchSize);
                workers.add(worker);
                Future<StressTestResult> future = executorService.submit(worker);
                futures.add(future);
            }

            // 等待所有任务完成
            for (int i = 0; i < futures.size(); i++) {
                Future<StressTestResult> future = futures.get(i);
                try {
                    StressTestResult result = future.get(300, TimeUnit.SECONDS); // 5分钟超时
                    results.add(result);
                } catch (TimeoutException e) {
                    logger.error("Worker task timed out", e);
                    future.cancel(true);
                    // 创建一个失败的结果
                    StressTestResult failedResult = new StressTestResult("TIMEOUT", -1);
                    failedResult.setSuccess(false);
                    failedResult.setErrorMessage("Task timed out");
                    results.add(failedResult);
                } catch (ExecutionException e) {
                    logger.error("Worker task execution failed", e);
                    StressTestResult failedResult = new StressTestResult("ERROR", -1);
                    failedResult.setSuccess(false);
                    failedResult.setErrorMessage("Execution failed: " + e.getMessage());
                    results.add(failedResult);
                }
            }

            // 收集性能统计
            collectPerformanceStats(workers);

        } catch (InterruptedException e) {
            logger.error("Stress test was interrupted", e);
            Thread.currentThread().interrupt();
        }

        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;

        logger.info("Concurrent stress test completed in {} ms", totalTime);
        printTestSummary(results, totalTime);

        return results;
    }
    
    /**
     * 打印测试摘要
     */
    private void printTestSummary(List<StressTestResult> results, long totalTime) {
        int successCount = 0;
        int failureCount = 0;
        long totalDeletedRows = 0;
        long totalInsertedRows = 0;
        long totalUpdatedRows = 0;
        long totalDeleteTime = 0;
        long totalInsertTime = 0;
        long totalUpdateTime = 0;
        
        for (StressTestResult result : results) {
            if (result.isSuccess()) {
                successCount++;
                totalDeletedRows += result.getDeletedRows();
                totalInsertedRows += result.getInsertedRows();
                totalUpdatedRows += result.getUpdatedRows();
                totalDeleteTime += result.getDeleteTime();
                totalInsertTime += result.getInsertTime();
                totalUpdateTime += result.getUpdateTime();
            } else {
                failureCount++;
            }
        }
        
        logger.info("=== Stress Test Summary ===");
        logger.info("Total tasks: {}", results.size());
        logger.info("Successful tasks: {}", successCount);
        logger.info("Failed tasks: {}", failureCount);
        logger.info("Success rate: {}%", String.format("%.2f", (double) successCount / results.size() * 100));
        logger.info("Total execution time: {} ms", totalTime);
        logger.info("Concurrency level: {}", concurrency);
        
        if (successCount > 0) {
            logger.info("=== Operation Statistics ===");
            logger.info("Total deleted rows: {}", totalDeletedRows);
            logger.info("Total inserted rows: {}", totalInsertedRows);
            logger.info("Total updated rows: {}", totalUpdatedRows);
            logger.info("Average delete time: {} ms", String.format("%.2f", (double) totalDeleteTime / successCount));
            logger.info("Average insert time: {} ms", String.format("%.2f", (double) totalInsertTime / successCount));
            logger.info("Average update time: {} ms", String.format("%.2f", (double) totalUpdateTime / successCount));
            
            // 计算吞吐量
            double throughputPerSecond = (double) (totalDeletedRows + totalInsertedRows + totalUpdatedRows) / (totalTime / 1000.0);
            logger.info("Overall throughput: {} operations/second", String.format("%.2f", throughputPerSecond));
        }
        
        // 打印失败的任务详情
        if (failureCount > 0) {
            logger.info("=== Failed Tasks ===");
            for (StressTestResult result : results) {
                if (!result.isSuccess()) {
                    logger.info("Failed: {}", result);
                }
            }
        }
    }
    
    /**
     * 执行单轮压力测试（所有port codes一次）
     */
    public void executeSingleRound(List<String> portCodes, int batchSize) {
        logger.info("Executing single round stress test");
        List<StressTestResult> results = executeStressTest(portCodes, batchSize);

        // 打印详细结果
        logger.info("=== Detailed Results ===");
        for (StressTestResult result : results) {
            logger.info(result.toString());
        }
    }
    
    /**
     * 执行多轮压力测试
     */
    public void executeMultipleRounds(List<String> portCodes, int rounds, long intervalMs, int batchSize) {
        logger.info("Executing {} rounds of stress test with {} ms interval", rounds, intervalMs);

        for (int round = 1; round <= rounds; round++) {
            logger.info("=== Starting Round {} of {} ===", round, rounds);

            long roundStartTime = System.currentTimeMillis();
            executeStressTest(portCodes, batchSize);
            long roundEndTime = System.currentTimeMillis();
            
            logger.info("Round {} completed in {} ms", round, roundEndTime - roundStartTime);
            
            // 如果不是最后一轮，等待间隔时间
            if (round < rounds && intervalMs > 0) {
                try {
                    logger.info("Waiting {} ms before next round...", intervalMs);
                    Thread.sleep(intervalMs);
                } catch (InterruptedException e) {
                    logger.warn("Sleep interrupted", e);
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
        
        logger.info("All {} rounds completed", rounds);
    }
    
    /**
     * 关闭服务
     */
    public void shutdown() {
        logger.info("Shutting down concurrent stress test service");
        executorService.shutdown();
        
        try {
            if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                logger.warn("Executor did not terminate gracefully, forcing shutdown");
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            logger.warn("Shutdown interrupted", e);
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        logger.info("Concurrent stress test service shutdown completed");
    }

    /**
     * 收集所有worker的性能统计
     */
    private void collectPerformanceStats(List<StressTestWorker> workers) {
        if (workers.isEmpty()) {
            return;
        }

        logger.info("=== Aggregated Performance Statistics ===");

        // 创建总体性能监控器
        PerformanceMonitor aggregatedMonitor = new PerformanceMonitor();

        // 合并所有worker的统计
        for (StressTestWorker worker : workers) {
            aggregatedMonitor.merge(worker.getPerformanceMonitor());
        }

        // 打印聚合统计
        aggregatedMonitor.printStats();
    }
}
