package com.example;

import com.example.dao.ProductDao;
import com.example.entity.Product;
import com.example.impl.GaussdbTracer;
import com.huawei.gaussdb.jdbc.log.Tracer;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

public class GaussDBDemo {
    private static final Logger logger = LoggerFactory.getLogger(GaussDBDemo.class);
    
    public static void main(String[] args) {
        ProductDao productDao = new ProductDao();
        // 单条插入示例
        Product product1 = new Product(1, "Cheese", new BigDecimal("9.99"));
        boolean success = productDao.insertProduct(product1);
        
        logger.info("Single insert result: {}", success);
        
        // 批量插入示例
        List<Product> products = Arrays.asList(
            new Product(2, "Bread", new BigDecimal("1.99")),
            new Product(3, "Milk", new BigDecimal("2.99")),
            new Product(4, "Butter", new BigDecimal("3.49"))
        );
        
        int batchResult = productDao.batchInsertProducts(products);
        logger.info("Batch insert result: {} products inserted", batchResult);
    }
}

