package com.example.util;

import com.example.config.ConnectionPoolConfig;
import com.example.config.DatabaseConnectionConfig;
import com.example.util.ConnectionPoolManager.DatabaseType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

/**
 * 数据库工具类
 * 支持连接池和传统连接方式
 */
public class DatabaseUtil {
    private static final Logger logger = LoggerFactory.getLogger(DatabaseUtil.class);

    // 连接池开关，从配置文件读取，可以通过方法覆盖
    private static Boolean useConnectionPoolOverride = null;

    /**
     * 检查是否使用连接池
     */
    private static boolean shouldUseConnectionPool() {
        if (useConnectionPoolOverride != null) {
            return useConnectionPoolOverride;
        }
        return ConnectionPoolConfig.isConnectionPoolEnabled();
    }

    /**
     * 获取数据库连接（自动检测数据库类型）
     * @param url 数据库URL
     * @param username 用户名
     * @param password 密码
     * @return 数据库连接
     */
    public static Connection getConnection(String url, String username, String password) throws SQLException {
        if (shouldUseConnectionPool()) {
            return getConnectionFromPool(url, username, password);
        } else {
            return getDirectConnection(url, username, password);
        }
    }

    /**
     * 获取数据库连接（指定数据库类型）
     * @param url 数据库URL
     * @param username 用户名
     * @param password 密码
     * @param databaseType 数据库类型
     * @return 数据库连接
     */
    public static Connection getConnection(String url, String username, String password, DatabaseType databaseType) throws SQLException {
        if (shouldUseConnectionPool()) {
            return ConnectionPoolManager.getConnection(url, username, password, databaseType);
        } else {
            return getDirectConnection(url, username, password, databaseType);
        }
    }

    /**
     * 从连接池获取连接
     */
    private static Connection getConnectionFromPool(String url, String username, String password) throws SQLException {
        DatabaseType databaseType = ConnectionPoolManager.detectDatabaseType(url);
        logger.debug("Detected database type: {} for URL: {}", databaseType, url);
        return ConnectionPoolManager.getConnection(url, username, password, databaseType);
    }

    /**
     * 直接获取数据库连接（不使用连接池）
     */
    private static Connection getDirectConnection(String url, String username, String password) throws SQLException {
        DatabaseType databaseType = ConnectionPoolManager.detectDatabaseType(url);
        return getDirectConnection(url, username, password, databaseType);
    }

    /**
     * 直接获取数据库连接（指定数据库类型，不使用连接池）
     */
    private static Connection getDirectConnection(String url, String username, String password, DatabaseType databaseType) throws SQLException {
        try {
            Class.forName(databaseType.getDriverClassName());
            Connection connection = DriverManager.getConnection(url, username, password);
            logger.debug("Direct database connection established successfully for: {}", url);
            return connection;
        } catch (ClassNotFoundException e) {
            logger.error("JDBC Driver not found for {}: {}", databaseType, databaseType.getDriverClassName(), e);
            throw new SQLException("JDBC Driver not found: " + databaseType.getDriverClassName(), e);
        } catch (SQLException e) {
            logger.error("Failed to establish direct database connection to: {}", url, e);
            throw e;
        }
    }

    /**
     * 获取默认数据库连接
     */
    public static Connection getConnection() throws SQLException {
        DatabaseConnectionConfig.DatabaseConfig config = DatabaseConnectionConfig.getDefaultDatabaseConfig();
        return getConnection(config.getUrl(), config.getUsername(), config.getPassword());
    }

    /**
     * 关闭数据库连接
     */
    public static void closeConnection(Connection connection) {
        if (connection != null) {
            try {
                connection.close();
                logger.debug("Database connection closed");
            } catch (SQLException e) {
                logger.error("Error closing database connection", e);
            }
        }
    }

    /**
     * 设置是否使用连接池（覆盖配置文件设置）
     */
    public static void setUseConnectionPool(boolean usePool) {
        useConnectionPoolOverride = usePool;
        logger.info("Connection pool usage override set to: {}", usePool);
    }

    /**
     * 清除连接池使用覆盖设置，恢复使用配置文件设置
     */
    public static void clearConnectionPoolOverride() {
        useConnectionPoolOverride = null;
        logger.info("Connection pool usage override cleared, using configuration file setting");
    }

    /**
     * 检查是否使用连接池
     */
    public static boolean isUsingConnectionPool() {
        return shouldUseConnectionPool();
    }

    /**
     * 关闭所有连接池
     */
    public static void shutdown() {
        if (shouldUseConnectionPool()) {
            ConnectionPoolManager.closeAllDataSources();
        }
    }

    /**
     * 打印连接池状态
     */
    public static void printPoolStatus() {
        if (shouldUseConnectionPool()) {
            ConnectionPoolManager.printPoolStatus();
        } else {
            logger.info("Connection pool is disabled");
        }
    }

    /**
     * 打印连接池配置信息
     */
    public static void printConnectionPoolConfig() {
        ConnectionPoolConfig.printConfiguration();
    }
}