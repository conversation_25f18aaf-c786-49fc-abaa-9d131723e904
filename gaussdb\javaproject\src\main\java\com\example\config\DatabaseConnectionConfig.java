package com.example.config;

import com.example.util.ConnectionPoolManager.DatabaseType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * 数据库连接配置管理类
 * 从配置文件中读取数据库连接URL、用户名、密码等信息
 */
public class DatabaseConnectionConfig {
    private static final Logger logger = LoggerFactory.getLogger(DatabaseConnectionConfig.class);
    
    private static final String CONFIG_FILE = "connection-pool.properties";
    private static Properties properties;
    private static volatile boolean initialized = false;
    
    /**
     * 数据库连接配置类
     */
    public static class DatabaseConfig {
        private final String url;
        private final String username;
        private final String password;
        private final DatabaseType type;
        
        public DatabaseConfig(String url, String username, String password, DatabaseType type) {
            this.url = url;
            this.username = username;
            this.password = password;
            this.type = type;
        }
        
        public String getUrl() { return url; }
        public String getUsername() { return username; }
        public String getPassword() { return password; }
        public DatabaseType getType() { return type; }
        
        @Override
        public String toString() {
            return String.format("DatabaseConfig{type=%s, url='%s', username='%s'}", 
                               type, url, username);
        }
    }
    
    /**
     * 元数据视图配置类
     */
    public static class MetadataConfig {
        private final String viewName;
        private final String schema;
        private final String tableNameColumn;
        private final String columnNameColumn;
        private final String dataTypeColumn;
        private final String ordinalPositionColumn;
        
        public MetadataConfig(String viewName, String schema, String tableNameColumn, 
                             String columnNameColumn, String dataTypeColumn, String ordinalPositionColumn) {
            this.viewName = viewName;
            this.schema = schema;
            this.tableNameColumn = tableNameColumn;
            this.columnNameColumn = columnNameColumn;
            this.dataTypeColumn = dataTypeColumn;
            this.ordinalPositionColumn = ordinalPositionColumn;
        }
        
        public String getViewName() { return viewName; }
        public String getSchema() { return schema; }
        public String getTableNameColumn() { return tableNameColumn; }
        public String getColumnNameColumn() { return columnNameColumn; }
        public String getDataTypeColumn() { return dataTypeColumn; }
        public String getOrdinalPositionColumn() { return ordinalPositionColumn; }
        
        /**
         * 获取完整的视图名（包含schema）
         */
        public String getFullViewName() {
            if (schema != null && !schema.trim().isEmpty()) {
                return schema + "." + viewName;
            }
            return viewName;
        }
        
        @Override
        public String toString() {
            return String.format("MetadataConfig{viewName='%s', schema='%s'}", viewName, schema);
        }
    }
    
    /**
     * 初始化配置
     */
    private static void initialize() {
        if (!initialized) {
            synchronized (DatabaseConnectionConfig.class) {
                if (!initialized) {
                    loadProperties();
                    initialized = true;
                }
            }
        }
    }
    
    /**
     * 加载配置文件
     */
    private static void loadProperties() {
        properties = new Properties();
        
        try (InputStream inputStream = DatabaseConnectionConfig.class.getClassLoader()
                .getResourceAsStream(CONFIG_FILE)) {
            
            if (inputStream != null) {
                properties.load(inputStream);
                logger.info("Successfully loaded database connection configuration from {}", CONFIG_FILE);
            } else {
                logger.warn("Configuration file {} not found, using default values", CONFIG_FILE);
            }
            
        } catch (IOException e) {
            logger.error("Failed to load configuration file {}, using default values", CONFIG_FILE, e);
        }
    }
    
    /**
     * 获取默认数据库配置
     */
    public static DatabaseConfig getDefaultDatabaseConfig() {
        initialize();
        return getDatabaseConfig("database.default");
    }
    
    /**
     * 根据配置名称获取数据库配置
     */
    public static DatabaseConfig getDatabaseConfig(String configName) {
        initialize();
        
        String url = getStringProperty(configName + ".url", "");
        String username = getStringProperty(configName + ".username", "");
        String password = getStringProperty(configName + ".password", "");
        String typeStr = getStringProperty(configName + ".type", "GAUSSDB");
        
        DatabaseType type;
        try {
            type = DatabaseType.valueOf(typeStr.toUpperCase());
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid database type: {}, using GAUSSDB as default", typeStr);
            type = DatabaseType.GAUSSDB;
        }
        
        if (url.isEmpty()) {
            logger.warn("Database URL not configured for {}, using default", configName);
            return getDefaultDatabaseConfig();
        }
        
        return new DatabaseConfig(url, username, password, type);
    }
    
    /**
     * 根据数据库类型获取数据库配置
     */
    public static DatabaseConfig getDatabaseConfigByType(DatabaseType databaseType) {
        initialize();
        String configName = "database." + databaseType.name().toLowerCase();
        return getDatabaseConfig(configName);
    }
    
    /**
     * 获取源数据库配置
     */
    public static DatabaseConfig getSourceDatabaseConfig() {
        initialize();
        return getDatabaseConfig("database.source");
    }
    
    /**
     * 获取默认元数据配置
     */
    public static MetadataConfig getDefaultMetadataConfig() {
        initialize();
        return getMetadataConfig("metadata.default");
    }
    
    /**
     * 根据数据库类型获取元数据配置
     */
    public static MetadataConfig getMetadataConfig(DatabaseType databaseType) {
        initialize();
        String configName = "metadata." + databaseType.name().toLowerCase();
        return getMetadataConfig(configName);
    }
    
    /**
     * 根据配置名称获取元数据配置
     */
    private static MetadataConfig getMetadataConfig(String configName) {
        String viewName = getStringProperty(configName + ".view", "v_table_metadata");
        String schema = getStringProperty(configName + ".schema", "");
        String tableNameColumn = getStringProperty(configName + ".table_name_column", "table_name");
        String columnNameColumn = getStringProperty(configName + ".column_name_column", "column_name");
        String dataTypeColumn = getStringProperty(configName + ".data_type_column", "data_type");
        String ordinalPositionColumn = getStringProperty(configName + ".ordinal_position_column", "ordinal_position");
        
        return new MetadataConfig(viewName, schema, tableNameColumn, columnNameColumn, 
                                 dataTypeColumn, ordinalPositionColumn);
    }
    
    /**
     * 获取字符串属性
     */
    private static String getStringProperty(String key, String defaultValue) {
        return properties.getProperty(key, defaultValue);
    }
    
    /**
     * 重新加载配置
     */
    public static void reload() {
        synchronized (DatabaseConnectionConfig.class) {
            initialized = false;
            initialize();
            logger.info("Database connection configuration reloaded");
        }
    }
    
    /**
     * 打印当前配置
     */
    public static void printConfiguration() {
        initialize();
        logger.info("=== Database Connection Configuration ===");
        
        // 打印默认配置
        DatabaseConfig defaultConfig = getDefaultDatabaseConfig();
        logger.info("Default Database: {}", defaultConfig);
        
        // 打印各种数据库类型的配置
        for (DatabaseType dbType : DatabaseType.values()) {
            try {
                DatabaseConfig config = getDatabaseConfigByType(dbType);
                MetadataConfig metaConfig = getMetadataConfig(dbType);
                logger.info("{} Database: {}", dbType, config);
                logger.info("{} Metadata: {}", dbType, metaConfig);
            } catch (Exception e) {
                logger.debug("No configuration found for database type: {}", dbType);
            }
        }
    }
}
