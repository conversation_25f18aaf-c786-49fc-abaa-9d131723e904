# 更新字段配置文件
# Update Fields Configuration

# 需要在插入时设置为null，后续通过update操作更新的字段列表（用逗号分隔）
# Fields that should be set to null during insert and updated later via update operations (comma separated)
update.fields=c_km_code,c_km_name

# 字段的更新值配置
# Update value configuration for fields

# c_km_code字段的更新值和类型
# update.field.c_km_code.value=4103.02.02.007491
update.field.c_km_code.type=string

# c_km_name字段的更新值和类型
# update.field.c_km_name.value=交易性股票投资公允价值变动损益
update.field.c_km_name.type=string

# 可以添加更多字段配置
# 格式：update.field.<字段名>.value=<更新值>
#      update.field.<字段名>.type=<值类型>
# 支持的类型：string, number, date, timestamp
