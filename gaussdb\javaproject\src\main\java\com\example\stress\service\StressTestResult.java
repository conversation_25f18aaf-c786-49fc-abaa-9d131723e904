package com.example.stress.service;

/**
 * 压力测试结果
 * 记录单个worker的执行结果
 */
public class StressTestResult {
    private final String portCode;
    private final int workerId;
    private boolean success;
    private String errorMessage;
    
    // 操作统计
    private int deletedRows;
    private int insertedRows;
    private int updatedRows;
    
    // 时间统计（毫秒）
    private long deleteTime;
    private long insertTime;
    private long updateTime;
    private long totalTime;
    
    public StressTestResult(String portCode, int workerId) {
        this.portCode = portCode;
        this.workerId = workerId;
        this.success = false;
    }
    
    // Getters and Setters
    public String getPortCode() {
        return portCode;
    }
    
    public int getWorkerId() {
        return workerId;
    }
    
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    public int getDeletedRows() {
        return deletedRows;
    }
    
    public void setDeletedRows(int deletedRows) {
        this.deletedRows = deletedRows;
    }
    
    public int getInsertedRows() {
        return insertedRows;
    }
    
    public void setInsertedRows(int insertedRows) {
        this.insertedRows = insertedRows;
    }
    
    public int getUpdatedRows() {
        return updatedRows;
    }
    
    public void setUpdatedRows(int updatedRows) {
        this.updatedRows = updatedRows;
    }
    
    public long getDeleteTime() {
        return deleteTime;
    }
    
    public void setDeleteTime(long deleteTime) {
        this.deleteTime = deleteTime;
    }
    
    public long getInsertTime() {
        return insertTime;
    }
    
    public void setInsertTime(long insertTime) {
        this.insertTime = insertTime;
    }
    
    public long getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(long updateTime) {
        this.updateTime = updateTime;
    }
    
    public long getTotalTime() {
        return totalTime;
    }
    
    public void setTotalTime(long totalTime) {
        this.totalTime = totalTime;
    }
    
    public int getTotalProcessedRows() {
        return Math.max(deletedRows, Math.max(insertedRows, updatedRows));
    }
    
    @Override
    public String toString() {
        if (success) {
            return String.format("Worker %d [%s]: SUCCESS - D:%d(%dms) I:%d(%dms) U:%d(%dms) Total:%dms",
                    workerId, portCode, deletedRows, deleteTime, insertedRows, insertTime, 
                    updatedRows, updateTime, totalTime);
        } else {
            return String.format("Worker %d [%s]: FAILED - %s (Total:%dms)",
                    workerId, portCode, errorMessage, totalTime);
        }
    }
}
