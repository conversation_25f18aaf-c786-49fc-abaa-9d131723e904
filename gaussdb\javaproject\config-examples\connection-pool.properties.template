# 数据库连接池配置文件模板
# Database Connection Pool Configuration Template
# 
# 使用说明：
# 1. 复制此文件到 src/main/resources/connection-pool.properties
# 2. 根据实际环境修改配置参数
# 3. 重启应用程序使配置生效

# ============================================================================
# 数据库连接配置 Database Connection Configuration
# ============================================================================

# 默认数据库连接配置
# Default Database Connection Settings
database.default.url=*************************************************************************************************************
database.default.username=defaultuser
database.default.password=defaultpass
database.default.type=GAUSSDB

# 源数据库连接配置（用于数据迁移）
# Source Database Connection Settings (for data migration)
database.source.url=**************************************************************************************************************
database.source.username=sourceuser
database.source.password=sourcepass
database.source.type=GAUSSDB

# StarRocks数据库连接配置
# StarRocks Database Connection Settings
database.starrocks.url=*********************************************
database.starrocks.username=staruser
database.starrocks.password=starpass
database.starrocks.type=STARROCKS

# MySQL数据库连接配置
# MySQL Database Connection Settings
database.mysql.url=***********************************
database.mysql.username=mysqluser
database.mysql.password=mysqlpass
database.mysql.type=MYSQL

# PostgreSQL数据库连接配置
# PostgreSQL Database Connection Settings
database.postgresql.url=*************************************
database.postgresql.username=pguser
database.postgresql.password=pgpass
database.postgresql.type=POSTGRESQL

# Oracle数据库连接配置
# Oracle Database Connection Settings
database.oracle.url=*****************************************
database.oracle.username=oracleuser
database.oracle.password=oraclepass
database.oracle.type=ORACLE

# ============================================================================
# 元数据视图配置 Metadata View Configuration
# ============================================================================

# 默认元数据视图配置
# Default Metadata View Settings
metadata.default.view=v_table_metadata
metadata.default.schema=public
metadata.default.table_name_column=table_name
metadata.default.column_name_column=column_name
metadata.default.data_type_column=data_type
metadata.default.ordinal_position_column=ordinal_position

# GaussDB元数据视图配置
# GaussDB Metadata View Configuration
metadata.gaussdb.view=v_table_metadata
metadata.gaussdb.schema=public
metadata.gaussdb.table_name_column=table_name
metadata.gaussdb.column_name_column=column_name
metadata.gaussdb.data_type_column=data_type
metadata.gaussdb.ordinal_position_column=ordinal_position

# StarRocks元数据视图配置
# StarRocks Metadata View Configuration
metadata.starrocks.view=columns
metadata.starrocks.schema=information_schema
metadata.starrocks.table_name_column=table_name
metadata.starrocks.column_name_column=column_name
metadata.starrocks.data_type_column=data_type
metadata.starrocks.ordinal_position_column=ordinal_position

# MySQL元数据视图配置
# MySQL Metadata View Configuration
metadata.mysql.view=columns
metadata.mysql.schema=information_schema
metadata.mysql.table_name_column=table_name
metadata.mysql.column_name_column=column_name
metadata.mysql.data_type_column=data_type
metadata.mysql.ordinal_position_column=ordinal_position

# PostgreSQL元数据视图配置
# PostgreSQL Metadata View Configuration
metadata.postgresql.view=columns
metadata.postgresql.schema=information_schema
metadata.postgresql.table_name_column=table_name
metadata.postgresql.column_name_column=column_name
metadata.postgresql.data_type_column=data_type
metadata.postgresql.ordinal_position_column=ordinal_position

# Oracle元数据视图配置
# Oracle Metadata View Configuration
metadata.oracle.view=user_tab_columns
metadata.oracle.schema=
metadata.oracle.table_name_column=table_name
metadata.oracle.column_name_column=column_name
metadata.oracle.data_type_column=data_type
metadata.oracle.ordinal_position_column=column_id

# ============================================================================
# 全局配置 Global Settings
# ============================================================================

# 是否启用连接池 (true/false)
# Enable connection pool
connection.pool.enabled=true

# 是否启用连接池监控 (true/false)
# Enable connection pool monitoring
connection.pool.monitoring.enabled=true

# 监控间隔时间（毫秒）
# Monitoring interval in milliseconds
connection.pool.monitoring.interval=60000

# ============================================================================
# 默认连接池配置 Default Connection Pool Settings
# ============================================================================

# 最大连接数
# Maximum pool size
connection.pool.default.maximumPoolSize=20

# 最小空闲连接数
# Minimum idle connections
connection.pool.default.minimumIdle=5

# 连接超时时间（毫秒）
# Connection timeout in milliseconds
connection.pool.default.connectionTimeout=30000

# 空闲连接超时时间（毫秒）
# Idle timeout in milliseconds
connection.pool.default.idleTimeout=600000

# 连接最大生存时间（毫秒）
# Maximum lifetime in milliseconds
connection.pool.default.maxLifetime=1800000

# 是否自动提交
# Auto commit
connection.pool.default.autoCommit=false

# 连接泄漏检测阈值（毫秒）
# Leak detection threshold in milliseconds
connection.pool.default.leakDetectionThreshold=60000

# ============================================================================
# GaussDB连接池配置 GaussDB Connection Pool Settings
# ============================================================================

connection.pool.gaussdb.maximumPoolSize=25
connection.pool.gaussdb.minimumIdle=8
connection.pool.gaussdb.connectionTimeout=30000
connection.pool.gaussdb.idleTimeout=600000
connection.pool.gaussdb.maxLifetime=1800000
connection.pool.gaussdb.autoCommit=false
connection.pool.gaussdb.leakDetectionThreshold=60000

# ============================================================================
# StarRocks连接池配置 StarRocks Connection Pool Settings
# ============================================================================

# StarRocks建议使用较小的连接池，因为查询通常比较重
# StarRocks recommends smaller pool size due to heavy queries
connection.pool.starrocks.maximumPoolSize=15
connection.pool.starrocks.minimumIdle=5
connection.pool.starrocks.connectionTimeout=45000
connection.pool.starrocks.idleTimeout=300000
connection.pool.starrocks.maxLifetime=1200000
connection.pool.starrocks.autoCommit=false
connection.pool.starrocks.leakDetectionThreshold=60000

# ============================================================================
# MySQL连接池配置 MySQL Connection Pool Settings
# ============================================================================

connection.pool.mysql.maximumPoolSize=20
connection.pool.mysql.minimumIdle=5
connection.pool.mysql.connectionTimeout=30000
connection.pool.mysql.idleTimeout=600000
connection.pool.mysql.maxLifetime=1800000
connection.pool.mysql.autoCommit=false
connection.pool.mysql.leakDetectionThreshold=60000

# ============================================================================
# PostgreSQL连接池配置 PostgreSQL Connection Pool Settings
# ============================================================================

connection.pool.postgresql.maximumPoolSize=20
connection.pool.postgresql.minimumIdle=5
connection.pool.postgresql.connectionTimeout=30000
connection.pool.postgresql.idleTimeout=600000
connection.pool.postgresql.maxLifetime=1800000
connection.pool.postgresql.autoCommit=false
connection.pool.postgresql.leakDetectionThreshold=60000

# ============================================================================
# Oracle连接池配置 Oracle Connection Pool Settings
# ============================================================================

# Oracle连接相对昂贵，建议使用较小的连接池
# Oracle connections are expensive, recommend smaller pool size
connection.pool.oracle.maximumPoolSize=15
connection.pool.oracle.minimumIdle=3
connection.pool.oracle.connectionTimeout=45000
connection.pool.oracle.idleTimeout=900000
connection.pool.oracle.maxLifetime=2700000
connection.pool.oracle.autoCommit=false
connection.pool.oracle.leakDetectionThreshold=60000

# ============================================================================
# 数据源特定配置 DataSource Specific Settings
# ============================================================================

# 是否缓存预编译语句
# Cache prepared statements
connection.pool.cachePrepStmts=true

# 预编译语句缓存大小
# Prepared statement cache size
connection.pool.prepStmtCacheSize=250

# 预编译语句SQL长度限制
# Prepared statement SQL limit
connection.pool.prepStmtCacheSqlLimit=2048

# 是否使用服务器端预编译语句
# Use server prepared statements
connection.pool.useServerPrepStmts=true

# 是否使用本地会话状态
# Use local session state
connection.pool.useLocalSessionState=true

# 是否重写批量语句
# Rewrite batched statements
connection.pool.rewriteBatchedStatements=true

# 是否缓存结果集元数据
# Cache result set metadata
connection.pool.cacheResultSetMetadata=true

# 是否缓存服务器配置
# Cache server configuration
connection.pool.cacheServerConfiguration=true

# 是否省略自动提交设置
# Elide set auto commits
connection.pool.elideSetAutoCommits=true

# 是否维护时间统计
# Maintain time stats
connection.pool.maintainTimeStats=false

# ============================================================================
# 环境特定配置示例 Environment Specific Configuration Examples
# ============================================================================

# 开发环境 Development Environment
# connection.pool.default.maximumPoolSize=10
# connection.pool.default.minimumIdle=2

# 测试环境 Test Environment  
# connection.pool.default.maximumPoolSize=15
# connection.pool.default.minimumIdle=3

# 生产环境 Production Environment
# connection.pool.default.maximumPoolSize=50
# connection.pool.default.minimumIdle=10

# ============================================================================
# 性能调优建议 Performance Tuning Recommendations
# ============================================================================

# CPU密集型应用：maximumPoolSize = CPU核心数 + 1
# CPU intensive: maximumPoolSize = CPU cores + 1

# IO密集型应用：maximumPoolSize = CPU核心数 × 2
# IO intensive: maximumPoolSize = CPU cores × 2

# 混合型应用：根据实际测试调整，通常在10-50之间
# Mixed workload: Adjust based on testing, typically 10-50

# 连接超时建议：30秒（获取连接的超时时间）
# Connection timeout: 30 seconds (timeout for getting connection)

# 空闲超时建议：10分钟（空闲连接的超时时间）
# Idle timeout: 10 minutes (timeout for idle connections)

# 最大生存时间建议：30分钟（连接的最大生存时间）
# Max lifetime: 30 minutes (maximum lifetime of connections)
