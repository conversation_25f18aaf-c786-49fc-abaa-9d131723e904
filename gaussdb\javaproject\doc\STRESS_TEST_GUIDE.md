# GaussDB 压力测试程序使用指南

## 概述

这是一个专门为GaussDB设计的压力测试模拟程序，可以并发执行对数据库表的INSERT、UPDATE和DELETE操作。程序按照不同的`c_port_code`进行分组，每个分组作为一个独立的工作单元进行并发处理。

## 前置条件

- Java 8 或更高版本
- Maven 3.6 或更高版本
- GaussDB 数据库实例
- 配置好的数据库连接参数
- **手动创建的数据库表**（使用提供的 create_tables.sql 脚本）

## 功能特性

- **并发操作**: 支持可配置的并发度，每个线程处理一个port_code分组
- **完整的CRUD操作**: 对每个分组执行DELETE → INSERT → UPDATE的完整流程
- **性能监控**: 详细的操作耗时统计和成功率监控
- **数据准备**: 自动创建测试表和初始化测试数据
- **灵活配置**: 支持命令行参数配置各种测试参数

## 测试流程

1. **数据准备阶段**: 创建三张结构相同的表（测试数据表、临时表和执行操作表），并灌入相同的测试数据
2. **并发执行阶段**:
   - 从执行表中删除指定port_code的数据
   - **流式分批次处理**:
     - 从测试数据表分批次查询数据并插入临时表
     - **每批次立即**从临时表转移到执行表
     - 清空临时表为下一批次做准备
   - 更新执行表中该port_code的数据状态
3. **统计报告阶段**: 输出详细的性能统计和操作结果

## 表结构

程序会自动创建以下三张表：

```sql
-- 测试数据表（只读，用于提供测试数据）
CREATE TABLE stress_test_data (
    id BIGINT PRIMARY KEY,
    c_port_code VARCHAR(50) NOT NULL,  -- 分组字段
    data_value VARCHAR(200),
    description VARCHAR(500),
    status INTEGER DEFAULT 1,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 临时表（用于分批次处理的中间存储）
CREATE TABLE stress_temp_data (
    id BIGINT PRIMARY KEY,
    c_port_code VARCHAR(50) NOT NULL,  -- 分组字段
    data_value VARCHAR(200),
    description VARCHAR(500),
    status INTEGER DEFAULT 1,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 执行操作表（用于实际的增删改操作）
CREATE TABLE stress_execution_data (
    id BIGINT PRIMARY KEY,
    c_port_code VARCHAR(50) NOT NULL,  -- 分组字段
    data_value VARCHAR(200),
    description VARCHAR(500),
    status INTEGER DEFAULT 1,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 快速开始

### 1. 创建数据库表

```bash
# 在GaussDB中执行提供的SQL脚本
# 使用数据库客户端连接到GaussDB，执行 create_tables.sql
```

### 2. 配置表名（可选）

如果需要自定义表名，在 `src/main/resources/connection-pool.properties` 中配置：

```properties
# 测试数据表名
stress.test.table.testData=your_test_data_table
# 临时表名
stress.test.table.temp=your_temp_table
# 执行操作表名
stress.test.table.execution=your_execution_table
```

### 3. 编译项目

```bash
mvn clean package
```

### 4. 基本使用

```bash
# Windows
run-stress-test.bat

# Linux/Unix
chmod +x run-stress-test.sh
./run-stress-test.sh
```

### 3. 自定义参数

```bash
# 设置并发度为20，50个port_code，每个port_code 100条记录，批次大小25
./run-stress-test.sh -c 20 -p 50 -r 100 -b 25

# 执行5轮测试，每轮间隔2秒
./run-stress-test.sh --rounds 5 --interval 2000

# 跳过数据初始化，显示详细结果
./run-stress-test.sh --no-init --detailed
```

## 命令行参数

| 参数 | 简写 | 默认值 | 说明 |
|------|------|--------|------|
| `--concurrency` | `-c` | 10 | 并发线程数 |
| `--port-codes` | `-p` | 50 | 不同port_code的数量 |
| `--records-per-port` | `-r` | 100 | 每个port_code对应的记录数 |
| `--batch-size` | `-b` | 50 | 分批次处理的批次大小 |
| `--rounds` | | 1 | 测试轮数 |
| `--interval` | | 1000 | 轮次间隔时间（毫秒） |
| `--no-init` | | false | 跳过数据初始化 |
| `--detailed` | | false | 显示详细的worker结果 |
| `--help` | `-h` | | 显示帮助信息 |

## 使用示例

### 示例1: 基本压力测试

```bash
# 使用默认参数：10个并发线程，50个port_code，每个100条记录
./run-stress-test.sh
```

### 示例2: 高并发测试

```bash
# 50个并发线程，100个port_code，每个200条记录，批次大小30
./run-stress-test.sh -c 50 -p 100 -r 200 -b 30
```

### 示例3: 多轮持续测试

```bash
# 执行10轮测试，每轮间隔5秒
./run-stress-test.sh --rounds 10 --interval 5000 --detailed
```

### 示例4: 仅执行测试（不重新初始化数据）

```bash
# 跳过数据初始化，使用现有数据进行测试
./run-stress-test.sh --no-init -c 30
```

## 性能监控

程序会自动收集和显示以下性能指标：

- **操作统计**: 每种操作（DELETE/INSERT/UPDATE）的成功/失败次数
- **时间统计**: 平均、最小、最大执行时间
- **吞吐量**: 每秒操作数
- **成功率**: 操作成功百分比
- **并发效果**: 总体执行时间和并发效率

## 输出示例

```
=== Stress Test Configuration ===
Concurrency: 20 threads
Port codes: 100
Records per port code: 50
Total records: 5000
Test rounds: 1

=== Stress Test Summary ===
Total tasks: 100
Successful tasks: 98
Failed tasks: 2
Success rate: 98.00%
Total execution time: 15432 ms
Concurrency level: 20

=== Operation Statistics ===
Total deleted rows: 4900
Total inserted rows: 4900
Total updated rows: 4900
Average delete time: 45.23 ms
Average insert time: 123.45 ms
Average update time: 67.89 ms
Overall throughput: 952.34 operations/second

=== Aggregated Performance Statistics ===
DELETE: 98 ops (98S/0F), Avg: 45.23ms, Min: 12.34ms, Max: 234.56ms, Success: 100.00%
INSERT: 98 ops (98S/0F), Avg: 123.45ms, Min: 67.89ms, Max: 456.78ms, Success: 100.00%
UPDATE: 98 ops (98S/0F), Avg: 67.89ms, Min: 23.45ms, Max: 189.12ms, Success: 100.00%
```

## 数据库配置

确保在 `src/main/resources/connection-pool.properties` 中正确配置了数据库连接信息：

```properties
# GaussDB连接配置
database.default.url=**************************************
database.default.username=your-username
database.default.password=your-password
database.default.type=GAUSSDB

# 连接池配置
connection.pool.gaussdb.maximumPoolSize=1000
connection.pool.gaussdb.minimumIdle=50
```

## 注意事项

1. **资源消耗**: 高并发测试会消耗大量数据库连接和系统资源
2. **数据安全**: 程序会清空和重新填充测试表，请在测试环境中运行
3. **表创建**: 程序不再自动创建表，需要手动执行 create_tables.sql 脚本
4. **表名配置**: 可以通过配置文件自定义表名，确保配置的表名在数据库中存在
5. **网络延迟**: 测试结果会受到网络延迟影响
6. **数据库性能**: 确保数据库有足够的性能来处理并发请求
7. **内存设置**: 大数据量测试时可能需要调整JVM内存参数

## 故障排除

### 常见问题

1. **连接失败**: 检查数据库连接配置和网络连通性
2. **内存不足**: 增加JVM内存参数 `-Xmx`
3. **编译失败**: 确保Maven和Java版本正确
4. **权限问题**: 确保数据库用户有执行DML操作的权限
5. **表不存在**: 如果提示表不存在，请检查是否已执行 create_tables.sql 脚本
6. **表名配置**: 如果使用自定义表名，请确保配置文件中的表名与数据库中的表名一致

### 日志查看

程序使用SLF4J + Logback记录详细日志，可以通过调整 `logback.xml` 配置来控制日志级别。

## 扩展功能

程序设计为可扩展的架构，可以根据需要添加：

- 更多的操作类型
- 自定义的数据生成策略
- 不同的并发模式
- 更详细的性能分析
- 结果导出功能
