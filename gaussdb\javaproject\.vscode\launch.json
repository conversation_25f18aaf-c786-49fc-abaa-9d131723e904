{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "java",
            "name": "StressTestApplication",
            "request": "launch",
            "mainClass": "com.example.stress.StressTestApplication",
            "projectName": "gaussdb-demo",
            "vmArgs": [
                "-Dfile.encoding=UTF-8",
                "-Dlog.path=output",
                "-Dlog.name=test"
            ],
            "args": [
                "--no-init","-f","stress_java_test","-c","100"
            ]
        },
        {
            "type": "java",
            "name": "GaussDBSqlFormatter",
            "request": "launch",
            "mainClass": "GaussDBSqlFormatter",
            "projectName": "gaussdb-demo",
            "vmArgs": [
                "-Dfile.encoding=UTF-8"
            ]
        },
        {
            "type": "java",
            "name": "SqlParser",
            "request": "launch",
            "mainClass": "SqlParser",
            "projectName": "gaussdb-demo"
        },
        {
            "type": "java",
            "name": "SqlFieldAnalyzer",
            "request": "launch",
            "mainClass": "com.example.SqlFieldAnalyzer",
            "projectName": "gaussdb-demo",
            "vmArgs": [
                "-Dfile.encoding=UTF-8"
            ],
            "args": [
                "summary_statement_20250916_0000",
                "1766847063"
            ]
        },
        {
            "type": "java",
            "name": "LogAnalyzer",
            "request": "launch",
            "mainClass": "com.example.LogAnalyzer",
            "projectName": "gaussdb-demo",
            "args": [
                "output/archive/gsjdbc-2025-07-23-0.log"
            ]
        },
        {
            "type": "java",
            "name": "Current File",
            "request": "launch",
            "mainClass": "${file}"
        },
        {
            "type": "java",
            "name": "GaussDBStatementAnalyzer",
            "request": "launch",
            "mainClass": "GaussDBStatementAnalyzer",
            "projectName": "gaussdb-demo",
            "args": [
                "summary_statement_0920_2200"
            ]
        },
        {
            "type": "java",
            "name": "DataMigrationService",
            "request": "launch",
            "mainClass": "com.example.DataMigrationService",
            "projectName": "gaussdb-demo",
            "args": [
                "-m",
                "migrate",
                "-sdb",
                "gaussdb",
                "-tdb",
                "gaussdb",
                "-gb",
                "c_group_code",
                "-st",
                "t_d_ai_act_val_test",
                "-tt",
                "r_d_ai_act_val_test",
                "-p",
                "jk1000Fa42",
                "-dc",
                "t_date",
                "-bd",
                "20",
                "-sd",
                "2014-06-20",
                "-ed",
                "2014-07-20"
            ]
        },
        {
            "type": "java",
            "name": "GaussDBDemo",
            "request": "launch",
            "mainClass": "com.example.GaussDBDemo",
            "projectName": "gaussdb-demo"
        }
    ]
}