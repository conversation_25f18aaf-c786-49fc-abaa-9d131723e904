package com.example.stress.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * 更新字段配置管理类
 * 用于管理需要在插入时设置为null，后续通过update操作更新的字段
 */
public class UpdateFieldConfig {
    private static final Logger logger = LoggerFactory.getLogger(UpdateFieldConfig.class);
    private static final String CONFIG_FILE = "update-fields.properties";
    
    private static Properties properties;
    private static boolean initialized = false;
    
    /**
     * 初始化配置
     */
    private static synchronized void initialize() {
        if (initialized) {
            return;
        }
        
        properties = new Properties();
        
        try (InputStream inputStream = UpdateFieldConfig.class.getClassLoader().getResourceAsStream(CONFIG_FILE)) {
            if (inputStream == null) {
                logger.warn("Configuration file {} not found, no update fields configured", CONFIG_FILE);
            } else {
                properties.load(inputStream);
                logger.info("Update field configuration loaded from {}", CONFIG_FILE);
            }
        } catch (IOException e) {
            logger.error("Failed to load configuration file {}, no update fields configured", CONFIG_FILE, e);
        }
        
        initialized = true;
        logUpdateFields();
    }
    
    /**
     * 获取需要更新的字段列表
     * @return 字段名列表
     */
    public static List<String> getUpdateFields() {
        initialize();
        
        String fieldsStr = properties.getProperty("update.fields", "");
        if (fieldsStr.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        List<String> fields = new ArrayList<>();
        for (String field : fieldsStr.split(",")) {
            String trimmedField = field.trim();
            if (!trimmedField.isEmpty()) {
                fields.add(trimmedField);
            }
        }
        
        return fields;
    }
    
    /**
     * 检查字段是否需要更新
     * @param fieldName 字段名
     * @return true如果字段需要更新
     */
    public static boolean isUpdateField(String fieldName) {
        return getUpdateFields().contains(fieldName);
    }
    
    /**
     * 获取字段的更新值配置
     * @param fieldName 字段名
     * @return 更新值，如果没有配置则返回null
     */
    public static String getUpdateValue(String fieldName) {
        initialize();
        return properties.getProperty("update.field." + fieldName + ".value");
    }
    
    /**
     * 获取字段的更新值类型
     * @param fieldName 字段名
     * @return 值类型（string, number, date等），默认为string
     */
    public static String getUpdateValueType(String fieldName) {
        initialize();
        return properties.getProperty("update.field." + fieldName + ".type", "string");
    }
    
    /**
     * 记录更新字段配置
     */
    private static void logUpdateFields() {
        List<String> updateFields = getUpdateFields();
        if (updateFields.isEmpty()) {
            logger.info("No update fields configured");
        } else {
            logger.info("=== Update Field Configuration ===");
            for (String field : updateFields) {
                String value = getUpdateValue(field);
                String type = getUpdateValueType(field);
                logger.info("Field: {}, Value: {}, Type: {}", field, value, type);
            }
        }
    }
}
