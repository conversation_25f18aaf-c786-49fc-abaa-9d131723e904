package com.example.stress.dao;

import com.example.stress.config.TableNameConfig;
import com.example.stress.config.UpdateFieldConfig;
import com.example.stress.model.TableStructure;
import com.example.stress.model.TestData;
import com.example.stress.monitor.MethodPerformanceStats;
import com.example.stress.util.DynamicSqlBuilder;
import com.example.stress.util.SqlCacheManager;
import com.example.stress.util.TableStructureManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.ArrayList;
import java.util.concurrent.ConcurrentHashMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 测试数据访问对象
 * 负责数据库操作
 */
public class TestDataDao {
    private static final Logger logger = LoggerFactory.getLogger(TestDataDao.class);

    // SQL缓存
    private static final Map<String, String> sqlCache = new HashMap<>();

    // 从配置文件获取表名
    public static String getTestDataTable() {
        return TableNameConfig.getTestDataTable();
    }

    public static String getExecutionTable() {
        return TableNameConfig.getExecutionTable();
    }

    public static String getTempTable() {
        return TableNameConfig.getTempTable();
    }

    public static String getPlanTable() {
        return TableNameConfig.getPlanTable();
    }
    
    /**
     * 验证表是否存在并检查表结构
     * 注意：表需要在数据库中预先创建，程序不再负责创建表
     */
    public boolean validateTablesExist(Connection connection) throws SQLException {
        logger.info("Validating that required tables exist in database...");

        boolean testDataExists = tableExists(connection, getTestDataTable());
        boolean executionExists = tableExists(connection, getExecutionTable());
        boolean tempExists = tableExists(connection, getTempTable());
        boolean planExists = tableExists(connection, getPlanTable());

        if (!testDataExists) {
            logger.error("Test data table '{}' does not exist", getTestDataTable());
        }
        if (!executionExists) {
            logger.error("Execution table '{}' does not exist", getExecutionTable());
        }
        if (!tempExists) {
            logger.error("Temporary table '{}' does not exist", getTempTable());
        }
        if (!planExists) {
            logger.error("Plan table '{}' does not exist", getPlanTable());
        }

        boolean allExist = testDataExists && executionExists && tempExists && planExists;
        if (allExist) {
            logger.info("All required tables exist: {}, {}, {}",
                       getTestDataTable(), getExecutionTable(), getTempTable(),getPlanTable());

            // 验证表结构一致性
            try {
                boolean structuresConsistent = TableStructureManager.validateTableStructuresConsistent(connection);
                if (!structuresConsistent) {
                    logger.error("Table structures are not consistent");
                    return false;
                }

                // 验证是否包含分组字段
                if (!TableStructureManager.hasGroupingColumn(connection, getTestDataTable(), "c_port_code")) {
                    logger.error("Test data table '{}' does not contain grouping column 'c_port_code'", getTestDataTable());
                    return false;
                }

                logger.info("Table structure validation passed");
            } catch (SQLException e) {
                logger.error("Failed to validate table structures", e);
                return false;
            }
        }

        return allExist;
    }

    /**
     * 检查表是否存在
     */
    private boolean tableExists(Connection connection, String tableName) throws SQLException {
        DatabaseMetaData metaData = connection.getMetaData();
        try (ResultSet rs = metaData.getTables(null, null, tableName.toUpperCase(), new String[]{"TABLE"})) {
            boolean exists = rs.next();
            logger.debug("Table '{}' exists: {}", tableName, exists);
            return exists;
        }
    }
    
    
    /**
     * 根据c_port_code按主键分批次删除执行表数据
     */
    public int deleteByPortCode(Connection connection, String portCode, int batchSize) throws SQLException {
        return deleteByPortCodeInBatches(connection, portCode, batchSize);
    }

    /**
     * 根据c_port_code按主键分批次删除执行表数据
     * @param connection 数据库连接
     * @param portCode 端口代码
     * @param batchSize 批次大小
     * @return 总删除行数
     */
    public int deleteByPortCodeInBatches(Connection connection, String portCode, int batchSize) throws SQLException {

        int totalDeleted = 0;
        boolean hasMoreData = true;
        int offset = 0;
        long startTime = 0;
        long queryEnd = 0;
        long delStart = 0;
        long delEnd = 0;
        long endTime = 0;



        // 从缓存获取SQL
        String selectCacheKey = "deleteByPortCodeInBatches_select_" + batchSize;
        String selectSql = SqlCacheManager.getCachedSql(selectCacheKey);
        String deleteSql = SqlCacheManager.getCachedSql("deleteByPortCodeInBatches_delete");

        // 预创建PreparedStatement，在循环中复用
        try (PreparedStatement selectStmt = connection.prepareStatement(selectSql);
             PreparedStatement deleteStmt = connection.prepareStatement(deleteSql)) {

            selectStmt.setFetchSize(batchSize); // 设置fetchSize优化性能

            while (hasMoreData) {
                List<Object> primaryKeys = new ArrayList<>();
                startTime = System.nanoTime();

                // 查询主键
                selectStmt.setString(1, portCode);

                try (ResultSet rs = selectStmt.executeQuery()) {
                    while (rs.next()) {
                        primaryKeys.add(rs.getObject(1));
                    }
                }
                queryEnd = System.nanoTime();

            // 记录查询时间
            MethodPerformanceStats.recordTime("deleteByPortCodeInBatches", "query", queryEnd - startTime);

            if (primaryKeys.isEmpty()) {
                hasMoreData = false;
                break;
            }

                // 按主键逐条删除，使用批处理
                delStart = System.nanoTime();

                // 清空之前的批处理
                deleteStmt.clearBatch();

                // 为每个主键值添加到批处理中
                for (Object primaryKey : primaryKeys) {
                    deleteStmt.setObject(1, primaryKey);
                    deleteStmt.addBatch();
                }

                // 执行批处理
                int[] batchResults = deleteStmt.executeBatch();
                delEnd = System.nanoTime();

                // 记录删除时间
                MethodPerformanceStats.recordTime("deleteByPortCodeInBatches", "delete", delEnd - delStart);

                // 提交事务
                long commitStart = System.nanoTime();
                connection.commit();
                long commitEnd = System.nanoTime();

                // 记录提交时间
                MethodPerformanceStats.recordTime("deleteByPortCodeInBatches", "commit", commitEnd - commitStart);
                endTime = System.nanoTime();
                int batchDeleted = java.util.Arrays.stream(batchResults).sum();
                totalDeleted += batchDeleted;
                logger.debug("Batch deleted {} rows for port code: {}, total: {}, total costed {}ms, query costed {}ms, delete costed {}ms, commit costed {}ms.(offset: {})",
                           batchDeleted, portCode, totalDeleted
                           ,String.format("%.2f", (double)(endTime-startTime)/1_000_1000.0)
                           ,String.format("%.2f", (double)(queryEnd-startTime)/1_000_1000.0)
                           ,String.format("%.2f", (double)(delEnd-delStart)/1_000_1000.0)
                           ,String.format("%.2f", (double)(endTime-delEnd)/1_000_1000.0)
                           ,offset);
                offset += batchSize;
                // 如果删除的行数小于批次大小，说明没有更多数据了
                if (batchDeleted < batchSize) {
                    hasMoreData = false;
                }
            }
        }

        logger.info("Total deleted {} rows for port code: {}", totalDeleted, portCode);

        return totalDeleted;
    }

    /**
     * 获取表的主键列名
     */
    private String getPrimaryKeyColumn(Connection connection, String tableName) throws SQLException {
        // 首先尝试从数据库元数据获取主键信息
        try {
            DatabaseMetaData metaData = connection.getMetaData();
            try (ResultSet rs = metaData.getPrimaryKeys(null, null, tableName.toLowerCase())) {
                if (rs.next()) {
                    String pkColumn = rs.getString("COLUMN_NAME");
                    logger.debug("Found primary key column: {} for table: {}", pkColumn, tableName);
                    return pkColumn;
                }
            }
        } catch (SQLException e) {
            logger.warn("Failed to get primary key from metadata for table: {}, error: {}",
                       tableName, e.getMessage());
        }

        // 如果无法从元数据获取，尝试常见的主键列名
        String[] commonPkNames = {"id", "ID", "pk_id", "PK_ID", tableName.toLowerCase() + "_id"};
        for (String pkName : commonPkNames) {
            if (columnExists(connection, tableName, pkName)) {
                logger.debug("Using common primary key column: {} for table: {}", pkName, tableName);
                return pkName;
            }
        }

        // 最后尝试查找以 'id' 结尾或包含 '_id' 的列
        try {
            String sql = "SELECT column_name FROM information_schema.columns " +
                        "WHERE table_name = ? AND (LOWER(column_name) LIKE '%id' OR LOWER(column_name) LIKE '%_id%') " +
                        "ORDER BY ordinal_position LIMIT 1";
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.setString(1, tableName.toLowerCase());
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        String pkColumn = rs.getString("column_name");
                        logger.debug("Found ID-like column: {} for table: {}", pkColumn, tableName);
                        return pkColumn;
                    }
                }
            }
        } catch (SQLException e) {
            logger.warn("Failed to query information_schema for table: {}, error: {}",
                       tableName, e.getMessage());
        }

        // 如果都找不到，使用第一列作为默认主键
        logger.warn("Could not determine primary key for table: {}, using first column", tableName);
        return getFirstColumn(connection, tableName);
    }

    /**
     * 检查列是否存在
     */
    private boolean columnExists(Connection connection, String tableName, String columnName) {
        try {
            String sql = "SELECT 1 FROM information_schema.columns WHERE table_name = ? AND column_name = ?";
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.setString(1, tableName.toLowerCase());
                stmt.setString(2, columnName.toLowerCase());
                try (ResultSet rs = stmt.executeQuery()) {
                    return rs.next();
                }
            }
        } catch (SQLException e) {
            logger.debug("Error checking column existence: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取表的第一列名
     */
    private String getFirstColumn(Connection connection, String tableName) throws SQLException {
        String sql = "SELECT column_name FROM information_schema.columns " +
                    "WHERE table_name = ? ORDER BY ordinal_position LIMIT 1";
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, tableName.toUpperCase());
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getString("column_name");
                }
            }
        }
        throw new SQLException("Could not find any columns for table: " + tableName);
    }
    
    
    
    /**
     * 根据c_port_code更新执行表数据（分批次更新）
     */
    public int updateStatusByPortCode(Connection connection, String portCode, int newStatus, int batchSize) throws SQLException {
        return updateStatusByPortCodeInBatches(connection, portCode, newStatus, batchSize); // 默认批次大小1000
    }

    /**
     * 根据c_port_code分批次更新执行表数据
     * @param connection 数据库连接
     * @param portCode 端口代码
     * @param newStatus 新状态（保留参数兼容性，实际不使用）
     * @param batchSize 批次大小
     * @return 总更新行数
     */
    public int updateStatusByPortCodeInBatches(Connection connection, String portCode, int newStatus, int batchSize) throws SQLException {
        // 获取主键列名
        String primaryKeyColumn = getPrimaryKeyColumn(connection, getExecutionTable());

        // 获取配置的更新字段
        List<String> updateFields = UpdateFieldConfig.getUpdateFields();
        if (updateFields.isEmpty()) {
            logger.warn("No update fields configured, skipping update operation for port code: {}", portCode);
            return 0;
        }

        // 从缓存获取SQL
        String selectSql = SqlCacheManager.getCachedSql("updateStatusByPortCodeInBatches_select_" + batchSize);
        String updateSql = SqlCacheManager.getCachedSql("updateStatusByPortCodeInBatches_update");

        int totalUpdated = 0;
        int offset = 0;
        boolean hasMoreData = true;
        long startTime = 0;
        long queryEnd = 0;
        long updStart = 0;
        long updEnd = 0;
        long endTime = 0;

        // 预创建PreparedStatement，在循环中复用
        try (PreparedStatement selectStmt = connection.prepareStatement(selectSql);
             PreparedStatement updateStmt = connection.prepareStatement(updateSql)) {

            selectStmt.setFetchSize(batchSize); // 设置fetchSize优化性能

            while (hasMoreData) {
                startTime = System.nanoTime();
                List<Map<String, Object>> batchData = new ArrayList<>();

                // 查询数据
                selectStmt.setString(1, portCode);
                selectStmt.setInt(2, offset);
                try (ResultSet rs = selectStmt.executeQuery()) {
                    while (rs.next()) {
                        Map<String, Object> row = new HashMap<>();
                        row.put(primaryKeyColumn, rs.getObject(primaryKeyColumn));
                        for (String field : updateFields) {
                            // 使用配置的更新值，而不是从测试数据表中读取的值
                            String configValue = UpdateFieldConfig.getUpdateValue(field);
                            if (configValue != null) {
                                row.put(field, configValue);
                            } else {
                                row.put(field, rs.getObject(field));
                            }
                        }
                        batchData.add(row);
                    }
                }
                queryEnd = System.nanoTime();

            // 记录查询时间
            MethodPerformanceStats.recordTime("updateStatusByPortCodeInBatches", "query", queryEnd - startTime);

            if (batchData.isEmpty()) {
                hasMoreData = false;
                break;
            }

            // 如果查询到的数据量小于批次大小，说明没有更多数据了
            if (batchData.size() < batchSize) {
                hasMoreData = false;
            }

                // 批量更新执行表
                updStart = System.nanoTime();

                // 清空之前的批处理
                updateStmt.clearBatch();

                for (Map<String, Object> row : batchData) {
                    // 设置更新字段的值
                    for (int i = 0; i < updateFields.size(); i++) {
                        String field = updateFields.get(i);
                        updateStmt.setObject(i + 1, row.get(field));
                    }
                    // 设置主键条件
                    updateStmt.setObject(updateFields.size() + 1, row.get(primaryKeyColumn));
                    updateStmt.addBatch();
                }
                // 执行批处理
                int[] batchResults = updateStmt.executeBatch();
                updEnd = System.nanoTime();

                // 记录更新时间
                MethodPerformanceStats.recordTime("updateStatusByPortCodeInBatches", "update", updEnd - updStart);

                // 提交事务
                long commitStart = System.nanoTime();
                connection.commit();
                endTime = System.nanoTime();

                // 记录提交时间
                MethodPerformanceStats.recordTime("updateStatusByPortCodeInBatches", "commit", endTime - commitStart);

                int batchUpdated = java.util.Arrays.stream(batchResults).sum();
                totalUpdated += batchUpdated;
                logger.debug("Batch updated {} rows for port code: {}, total: {}, total costed {}ms, query costed {}ms, update costed {}ms, commit costed {}ms.(offset: {})",
                           batchUpdated, portCode, totalUpdated
                           ,String.format("%.2f", (double)(endTime-startTime)/1_000_1000.0)
                           ,String.format("%.2f", (double)(queryEnd-startTime)/1_000_1000.0)
                           ,String.format("%.2f", (double)(updEnd-updStart)/1_000_1000.0)
                           ,String.format("%.2f", (double)(endTime-updEnd)/1_000_1000.0)
                           ,offset);

                // 更新offset，准备获取下一批数据
                offset += batchSize;
            }
        }

        logger.info("Total updated {} rows for port code: {}", totalUpdated, portCode);
        return totalUpdated;
    }
    
    /**
     * 获取所有不同的c_port_code
     */
    public List<String> getAllPortCodes(Connection connection,String planCode) throws SQLException {
        String sql = "SELECT DISTINCT c_port_code FROM " + getPlanTable() + " WHERE C_GROUP_CODE='" + planCode + "' ORDER BY C_PORT_CODE";
        List<String> portCodes = new ArrayList<>();
        
        try (Statement stmt = connection.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            
            while (rs.next()) {
                portCodes.add(rs.getString("c_port_code"));
            }
        }
        
        logger.info("Found {} distinct port codes", portCodes.size());
        return portCodes;
    }
    
    /**
     * 清空表数据
     */
    public void truncateTable(Connection connection, String tableName) throws SQLException {
        String sql = "TRUNCATE TABLE " + tableName;
        try (Statement stmt = connection.createStatement()) {
            stmt.execute(sql);
            logger.info("Truncated table: {}", tableName);
        }
    }
    
    /**
     * 获取表记录数
     */
    public long getTableCount(Connection connection, String tableName) throws SQLException {
        String sql = "SELECT COUNT(*) FROM " + tableName;
        try (Statement stmt = connection.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {

            if (rs.next()) {
                return rs.getLong(1);
            }
            return 0;
        }
    }

    /**
     * 清空临时表数据
     */
    public void clearTempTable(Connection connection) throws SQLException {
        String sql = "DELETE FROM " + getTempTable();
        try (Statement stmt = connection.createStatement()) {
            int deletedRows = stmt.executeUpdate(sql);
            logger.debug("Cleared {} rows from temp table", deletedRows);
        }
    }

    /**
     * 清空临时表中指定port_code的数据
     */
    public void clearTempTableByPortCode(Connection connection, String portCode) throws SQLException {
        String sql = "DELETE FROM " + getTempTable() + " WHERE c_port_code = ?";
        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, portCode);
            int deletedRows = pstmt.executeUpdate();
            logger.debug("Cleared {} rows from temp table for port code: {}", deletedRows, portCode);
        }
    }

    /**
     * 分批次从测试数据表插入到临时表
     */
    public int batchInsertToTempTable(Connection connection, String portCode, int batchSize, int offset) throws SQLException {
        // 获取表结构
        TableStructure tempStructure = TableStructureManager.getTempTableStructure(connection);

        // 从缓存获取SQL
        String selectSql = SqlCacheManager.getCachedSql("batchInsertToTempTable_select_" + batchSize);
        String insertSql = SqlCacheManager.getCachedSql("batchInsertToTempTable_insert");

        int insertedCount = 0;
        long startTime = 0;
        long queryEnd = 0;
        long insStart = 0;
        long endTime = 0;

        // 预创建PreparedStatement，复用
        try (PreparedStatement selectStmt = connection.prepareStatement(selectSql);
             PreparedStatement insertStmt = connection.prepareStatement(insertSql)) {

            selectStmt.setFetchSize(batchSize); // 设置fetchSize优化性能

            // 查询数据
            startTime = System.nanoTime();
            selectStmt.setString(1, portCode);
            selectStmt.setInt(2, offset);

            try (ResultSet rs = selectStmt.executeQuery()) {
                queryEnd = System.nanoTime();

                // 记录查询时间
                MethodPerformanceStats.recordTime("batchInsertToTempTable", "query", queryEnd - startTime);

                // 插入到临时表
                while (rs.next()) {
                    // 使用普通的参数设置方法，因为SQL已经将更新字段设置为null
                    DynamicSqlBuilder.setParametersFromResultSet(insertStmt, rs, tempStructure);
                    insertStmt.addBatch();
                    insertedCount++;
                }

                if (insertedCount > 0) {
                    insStart = System.nanoTime();
                    insertStmt.executeBatch();
                    endTime = System.nanoTime();

                    // 记录插入时间
                    MethodPerformanceStats.recordTime("batchInsertToTempTable", "insert", endTime - insStart);
                    logger.debug("Inserted {} records to temp table for port code: {}, total costed {}ms, query costed {}ms, insert costed {}ms.(offset: {})", insertedCount, portCode
                    ,String.format("%.2f", (double)(endTime-startTime)/1_000_1000.0)
                    ,String.format("%.2f", (double)(queryEnd-startTime)/1_000_1000.0)
                    ,String.format("%.2f", (double)(endTime-insStart)/1_000_1000.0)
                    ,offset);
                }
            }
        }

        return insertedCount;
    }

    /**
     * 从临时表批量插入到执行表
     */
    public int batchInsertFromTempToExecution(Connection connection, String portCode, int offset) throws SQLException {
        // 获取表结构
        TableStructure executionStructure = TableStructureManager.getExecutionTableStructure(connection);

        // 从缓存获取SQL
        String selectSql = SqlCacheManager.getCachedSql("batchInsertFromTempToExecution_select");
        String insertSql = SqlCacheManager.getCachedSql("batchInsertFromTempToExecution_insert");

        int insertedCount = 0;
        long startTime = 0;
        long queryEnd = 0;
        long insStart = 0;
        long insEnd = 0;
        long endTime = 0;

        // 预创建PreparedStatement，复用
        try (PreparedStatement selectStmt = connection.prepareStatement(selectSql);
             PreparedStatement insertStmt = connection.prepareStatement(insertSql)) {

            selectStmt.setFetchSize(1000); // 设置fetchSize优化性能

            // 从临时表查询数据并直接插入到执行表
            startTime = System.nanoTime();
            selectStmt.setString(1, portCode);

            try (ResultSet rs = selectStmt.executeQuery()) {
                queryEnd = System.nanoTime();

                // 记录查询时间
                MethodPerformanceStats.recordTime("batchInsertFromTempToExecution", "query", queryEnd - startTime);

                // 插入到执行表
                while (rs.next()) {
                    // 使用动态参数设置
                    DynamicSqlBuilder.setParametersFromResultSet(insertStmt, rs, executionStructure);
                    insertStmt.addBatch();
                    insertedCount++;
                }

                if (insertedCount > 0) {
                    insStart = System.nanoTime();
                    insertStmt.executeBatch();
                    insEnd = System.nanoTime();

                    // 记录插入时间
                    MethodPerformanceStats.recordTime("batchInsertFromTempToExecution", "insert", insEnd - insStart);

                    // 提交事务
                    long commitStart = System.nanoTime();
                    connection.commit();
                    endTime = System.nanoTime();

                    // 记录提交时间
                    MethodPerformanceStats.recordTime("batchInsertFromTempToExecution", "commit", endTime - commitStart);

                    logger.debug("Inserted {} records from temp to execution table for port code: {}, total costed {}ms, query costed {}ms,insert costed {}ms, commit costed {}ms.(offset: {})", insertedCount, portCode
                    ,String.format("%.2f", (double)(endTime-startTime)/1_000_1000.0)
                    ,String.format("%.2f", (double)(queryEnd-startTime)/1_000_1000.0)
                    ,String.format("%.2f", (double)(insEnd-insStart)/1_000_1000.0)
                    ,String.format("%.2f", (double)(endTime-insEnd)/1_000_1000.0)
                    ,offset);
                }
            }
        }

        return insertedCount;
    }
}
