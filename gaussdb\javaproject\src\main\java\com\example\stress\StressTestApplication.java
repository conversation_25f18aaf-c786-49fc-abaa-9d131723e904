package com.example.stress;

import com.example.stress.config.StressTestConfig;
import com.example.stress.monitor.MethodPerformanceStats;
import com.example.stress.service.ConcurrentStressTestService;
import com.example.stress.service.DataPreparationService;
import com.example.stress.service.StressTestResult;
import com.example.stress.util.SqlCacheManager;
import com.example.util.DatabaseUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * 压力测试应用程序主入口
 * 负责协调整个压力测试流程
 */
public class StressTestApplication {
    private static final Logger logger = LoggerFactory.getLogger(StressTestApplication.class);
    
    public static void main(String[] args) {
        logger.info("Starting GaussDB Stress Test Application");
        
        // 解析命令行参数
        StressTestConfig config = StressTestConfig.fromArgs(args);
        config.printConfig();
        
        StressTestApplication app = new StressTestApplication();
        
        try {
            app.run(config);
        } catch (Exception e) {
            logger.error("Stress test application failed", e);
            System.exit(1);
        } finally {
            // 确保资源清理
            DatabaseUtil.shutdown();
        }
        
        logger.info("GaussDB Stress Test Application completed");
    }
    
    /**
     * 运行压力测试
     */
    public void run(StressTestConfig config) {
        long applicationStartTime = System.currentTimeMillis();
        
        try {
            // 步骤1: 数据准备
            if (config.isInitData()) {
                logger.info("=== Step 1: Data Preparation ===");
                prepareData(config);
            } else {
                logger.info("=== Step 1: Skipping Data Preparation ===");
            }
            
            // 步骤2: 获取测试数据
            logger.info("=== Step 2: Loading Test Data ===");
            List<String> portCodes = loadPortCodes(config.getPlanCode());
            
            if (portCodes.isEmpty()) {
                logger.error("No port codes found. Please run with --init-data first.");
                return;
            }
            
            logger.info("Loaded {} port codes for testing", portCodes.size());

            // 步骤2.5: 初始化SQL缓存
            logger.info("=== Step 2.5: Initializing SQL Cache ===");
            try (java.sql.Connection connection = DatabaseUtil.getConnection()) {
                boolean cacheInitialized = SqlCacheManager.initializeSqlCache(connection, config.getBatchSize());
                if (!cacheInitialized) {
                    logger.error("Failed to initialize SQL cache. Exiting.");
                    return;
                }
                logger.info("SQL cache initialized successfully");
            } catch (Exception e) {
                logger.error("Failed to initialize SQL cache", e);
                return;
            }

            // 步骤3: 执行压力测试
            logger.info("=== Step 3: Executing Stress Test ===");
            executeStressTest(config, portCodes);
            
            // 步骤4: 打印最终统计
            long applicationEndTime = System.currentTimeMillis();
            long totalApplicationTime = applicationEndTime - applicationStartTime;

            logger.info("=== Final Summary ===");
            logger.info("Total application runtime: {} ms ({} seconds)",
                       totalApplicationTime, String.format("%.2f", totalApplicationTime / 1000.0));

            // 步骤5: 打印方法性能统计
            logger.info("=== Method Performance Statistics ===");
            MethodPerformanceStats.printAllStats();
            
        } catch (Exception e) {
            logger.error("Error during stress test execution", e);
            throw new RuntimeException("Stress test failed", e);
        }
    }
    
    /**
     * 准备测试数据
     */
    private void prepareData(StressTestConfig config) {
        logger.info("Initializing test data with {} port codes and {} records per port code", 
                   config.getPortCodeCount(), config.getRecordsPerPortCode());
        
        DataPreparationService dataService = new DataPreparationService();
        
        long startTime = System.currentTimeMillis();
        // 初始化数据
        dataService.initializeDataDynamic(config.getPortCodeCount(), config.getRecordsPerPortCode());
        long endTime = System.currentTimeMillis();
        
        logger.info("Data preparation completed in {} ms", endTime - startTime);
        dataService.printDataStatistics(config.getPlanCode());
    }
    
    /**
     * 加载port codes
     */
    private List<String> loadPortCodes(String planCode) {
        DataPreparationService dataService = new DataPreparationService();
        
        if (!dataService.isDataInitialized()) {
            logger.warn("Test data not initialized. Run with data initialization first.");
            return List.of(); // 返回空列表
        }
        
        return dataService.getAllPortCodes(planCode);
    }
    
    /**
     * 执行压力测试
     */
    private void executeStressTest(StressTestConfig config, List<String> portCodes) {
        ConcurrentStressTestService stressTestService = new ConcurrentStressTestService(config.getConcurrency());
        
        try {
            if (config.getTestRounds() == 1) {
                // 单轮测试
                logger.info("Executing single round stress test with batch size: {}", config.getBatchSize());
                List<StressTestResult> results = stressTestService.executeStressTest(portCodes, config.getBatchSize());

                if (config.isPrintDetailedResults()) {
                    printDetailedResults(results);
                }

            } else {
                // 多轮测试
                logger.info("Executing {} rounds of stress test with batch size: {}", config.getTestRounds(), config.getBatchSize());
                stressTestService.executeMultipleRounds(
                    portCodes,
                    config.getTestRounds(),
                    config.getRoundIntervalMs(),
                    config.getBatchSize()
                );
            }
            
        } finally {
            stressTestService.shutdown();
        }
    }
    
    /**
     * 打印详细结果
     */
    private void printDetailedResults(List<StressTestResult> results) {
        logger.info("=== Detailed Worker Results ===");
        
        for (StressTestResult result : results) {
            if (result.isSuccess()) {
                logger.info("✓ {}", result);
            } else {
                logger.info("✗ {}", result);
            }
        }
        
        // 按性能排序显示前5名和后5名
        results.sort((r1, r2) -> Long.compare(r1.getTotalTime(), r2.getTotalTime()));
        
        logger.info("=== Performance Rankings ===");
        logger.info("Top 5 fastest workers:");
        for (int i = 0; i < Math.min(5, results.size()); i++) {
            StressTestResult result = results.get(i);
            if (result.isSuccess()) {
                logger.info("  {}. Worker {} [{}]: {}ms", 
                           i + 1, result.getWorkerId(), result.getPortCode(), result.getTotalTime());
            }
        }
        
        if (results.size() > 5) {
            logger.info("Bottom 5 slowest workers:");
            for (int i = Math.max(0, results.size() - 5); i < results.size(); i++) {
                StressTestResult result = results.get(i);
                if (result.isSuccess()) {
                    logger.info("  {}. Worker {} [{}]: {}ms", 
                               results.size() - i, result.getWorkerId(), result.getPortCode(), result.getTotalTime());
                }
            }
        }
    }
    
    /**
     * 检查系统环境
     */
    private void checkSystemEnvironment() {
        logger.info("=== System Environment ===");
        
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        
        logger.info("Max memory: {} MB", maxMemory / 1024 / 1024);
        logger.info("Total memory: {} MB", totalMemory / 1024 / 1024);
        logger.info("Free memory: {} MB", freeMemory / 1024 / 1024);
        logger.info("Available processors: {}", runtime.availableProcessors());
        
        // 检查数据库连接
        try {
            DatabaseUtil.printConnectionPoolConfig();
            DatabaseUtil.printPoolStatus();
        } catch (Exception e) {
            logger.warn("Could not check database connection pool status", e);
        }
    }
}
