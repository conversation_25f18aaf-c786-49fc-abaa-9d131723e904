#webplus组件是否开启自定义菜单功能，默认关闭
=
#webplus组件系统菜单显示编码和名称
#废弃
#招银理财接口调用超时时间
#招银理财老受托净值下发标识
#webplus组件系统菜单显示编码和名称
#废弃
#招银理财接口调用超时时间
#招银理财老受托净值下发标识
#BUG #361925 【深国投信托4.6】webservice接口净值行情、产品持仓接口导入清算卡
#建立连接的超时时间(分)
CONNECTIONTIMEOUT=1
DOP.appId=zctg
DOP.bucketName=yfw
#STORY #107802 估值4.5系统增加净值文件自动生成功能  配置文件上传服务信息
#true调试模式，不发送文件到DOP文件系统
DOP.debug=true
DOP.reportCode=INFO_PL_NAV_TA@REP_PL_TA
FOMP-AMS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.algoServices.path=
FOMP-AMS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.algoServices.ribbon.discovery=local
FOMP-AMS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.algoServices.ribbon.listOfServers=192.168.105.158:8205
FOMP-AMS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.eureka.client.enabled=false
FOMP-AMS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.eureka.client.fetchRegistry=false
FOMP-AMS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.eureka.client.register-with-eureka=false
FOMP-AMS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.eureka.client.server.waitTimeInMsWhenSyncEmpty=0
FOMP-AMS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.feign.client.config.default.readTimeout=3600000
FOMP-AMS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.feign.client.config.fomp-uco-crust-mic.logger-level=FULL
FOMP-AMS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.fomp.ftp.host=************
FOMP-AMS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.fomp.ftp.password=ftp
FOMP-AMS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.fomp.ftp.port=2133
FOMP-AMS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.fomp.ftp.rootPath=/home/<USER>/ftp/xc-test
FOMP-AMS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.fomp.ftp.username=yssftp
FOMP-AMS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.logging.config=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/global/logConfig.xml
FOMP-AMS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.logging.level.com.yss.fomp.ams.foundation.feignclient=debug
FOMP-AMS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.logging.level.org.hibernate.SQL=DEBUG
FOMP-AMS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.logging.level.org.hibernate.type.descriptor.sql.BasicBinder=info
FOMP-AMS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.logging.pattern.console=[%X{request_id}][%X{fun_code} %X{service_id} %X{request_uri}] %d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n
FOMP-AMS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.mysql.load.isInitSql=false
FOMP-AMS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.ribbon.OkToRetryOnAllOperations=false
FOMP-AMS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.rocketmq.client.localOffsetStoreDir=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/zhenghegongcheng2/rocketmq11911/FOMP-AMS-FOUNDATION/.rocketmq_offsets11911
FOMP-AMS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.spring.application.name=fomp-ams-foundation
FOMP-AMS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.spring.jpa.show-sql=false
FOMP-AMS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.yss.ams.preload.maxCacheWarnSum=1000000
FOMP-AMS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.yss.ams.thread-pool.batchCount=5
FOMP-AMS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.yss.ams.thread-pool.corePoolSize=10
FOMP-AMS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.yss.zookeeper.server.clientPort=5181
FOMP-BASEBUSINESS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.eureka.client.enabled=false
FOMP-BASEBUSINESS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.eureka.client.fetchRegistry=false
FOMP-BASEBUSINESS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.eureka.client.register-with-eureka=false
FOMP-BASEBUSINESS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.eureka.client.server.waitTimeInMsWhenSyncEmpty=0
FOMP-BASEBUSINESS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.feign.client.config.default.readTimeout=600000
FOMP-BASEBUSINESS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.feign.client.config.fomp-uco-crust-mic.logger-level=FULL
FOMP-BASEBUSINESS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.logging.config=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/global/logConfig.xml
FOMP-BASEBUSINESS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.logging.level.com.yss.fomp.basebusiness.foundation.uco.feign=debug
FOMP-BASEBUSINESS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.logging.level.org.hibernate.SQL=DEBUG
FOMP-BASEBUSINESS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.logging.level.org.hibernate.type.descriptor.sql.BasicBinder=info
FOMP-BASEBUSINESS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.logging.pattern.console=[%X{request_id}][%X{fun_code} %X{service_id} %X{request_uri}] %d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n
FOMP-BASEBUSINESS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.mysql.load.isInitSql=false
FOMP-BASEBUSINESS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.ribbon.OkToRetryOnAllOperations=false
FOMP-BASEBUSINESS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.rocketmq.client.localOffsetStoreDir=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/zhenghegongcheng2/rocketmq11911/FOMP-BASEBUSINESS-FOUNDATION/.rocketmq_offsets11911
FOMP-BASEBUSINESS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.spring.application.name=fomp-basebusiness-foundation
FOMP-BASEBUSINESS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.spring.jpa.show-sql=false
FOMP-BASEBUSINESS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.yss.base.preload.maxCacheWarnSum=1000000
FOMP-BASEBUSINESS-FOUNDATION.com.yss.fomp.YSSUCOBRIDGE.yss.zookeeper.server.clientPort=5181
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.camunda.bpm.database.jdbc-batch-processing=false
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.camunda.bpm.database.schemaUpdate=false
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.camunda.bpm.database.type=postgres
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.camunda.bpm.historyLevel=audit
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.camunda.bpm.job-execution.enabled=false
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.com.yss.fast.task.automatic.filescan.muti.threadcount=16
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.com.yss.fast.task.automatic.filescan.single.threadcount=16
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.com.yss.fast.task.automatic.isCheckRight=true
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.com.yss.fast.task.automatic.msgpush.frequece=10
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.com.yss.fast.task.automatic.msgpush.queryBatchSize=20
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.com.yss.fast.task.automatic.msgpush.threadcount=2
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.com.yss.fast.task.automatic.partitionexecute.threadcount=4
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.com.yss.fast.task.automatic.procInstExe.threadcount=32
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.com.yss.fast.task.automatic.taskcomplete.threadcount=32
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.com.yss.fast.task.automatic.taskexecute.threadcount=32
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.com.yss.fast.task.automatic.taskexecute.threadcount_act=16
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.com.yss.fast.task.automatic.taskexecute.threadcount_act_other=16
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.com.yss.fast.task.automatic.taskexecute.threadcount_act_stock=16
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.com.yss.fast.task.automatic.taskexecute.threadcount_clr=16
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.com.yss.fast.task.automatic.taskexecute.threadcount_imp=8
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.com.yss.fast.task.automatic.timer.threadcount=4
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.com.yss.fast.task.automatic.uploadprocess.isCheckInterfaceGroupCode=false
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.com.yss.fast.workflow.AbstractCustomListener.threadcount=24
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.com.yss.fomp.mq.proxy.port=3007
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.fomp.services.alias.oldServiceIds[16]=fomp-fast-automatic
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.fomp.services.alias.oldServiceIds[17]=osgi-automatic
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.fomp.sso.client.excludeUrls=/**/sso,/**/v1/license/listExpiredLicItems,/**/login/thirtyConfig,/**/v1/auth/token/*,/**/v1/yss/crypt/enable,/**/v1/sysmanager/resourcerest/get/image,/**/v1/sysmanager/versioninfo/downloadContentImg/**,/**/login/projectLoginConfig,/**/v1/sysmanager/safesys/pwdconfig,/**/v1/right/userencrypt/**,/**/login,/**/v1/sysmanager/systemlogo/queryAllLogoInfo,/**/v1/sysmanager/syslock/ping,/**/v1/verifycode/check,/**/v1/verifycode/get,/**/v1/right/loginconfig,/**/v1/port/syncPortBaseInfo,/**/v1/port/sendMqMessage,/**/v1/token,/**/oauth2/refresh_token,/**/ws/com/yss/fast/dataintegration/controller/relay/IFastDataImpController/**,/**/ws/com/yss/deploy/controller/IFastDeployAdminController/**,/ws/com/yss/fast/runtime/restservice/IServerStatus/serverstatus/started
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.grpc.server.port=18283
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.logging.config=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/global/logConfig.xml
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.osgi.task.dispatch.url=http://***********:8083/FOMP-FAST/ws/com/yss/fast/task/client/service/ISchedulerXcRestService/schedulerXc
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.plugin.deployment.server.initDirectory=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/YSS_FILE/fomp/plugin-deploy-server/init
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.plugin.deployment.server.installDirectory=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/YSS_FILE/fomp/plugin-deploy-server/install
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.plugin.deployment.server.uploadDirectory=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/YSS_FILE/fomp/plugin-deploy-server/upload
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.rocketmq.client.localOffsetStoreDir=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/zhenghegongcheng2/rocketmq11911/FOMP-FAST/.rocketmq_offsets11911
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.spring.application.name=fomp-fast
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.spring.cloud.sentinel.enabled=false
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.yss.file.filePhysicalPath=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/YSS_FILE
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.yss.fomp.license.enable=true
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.yss.fomp.license.server.enable=true
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.yss.fomp.license.server.path=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/global
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.yss.osgi.webclient=true
FOMP-FAST.com.yss.fomp.YSSUCOBRIDGE.yss.zookeeper.server.clientPort=5181
FOMP-MQ.com.yss.fomp.YSSUCOBRIDGE.com.yss.startStatusCheck.enable=false
FOMP-MQ.com.yss.fomp.YSSUCOBRIDGE.logging.config=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/global/logConfig.xml
FOMP-MQ.com.yss.fomp.YSSUCOBRIDGE.spring.cloud.stream.default-binder=rocketmq
FOMP-MQ.com.yss.fomp.YSSUCOBRIDGE.spring.cloud.stream.rocketmq.binder.enableMsgTrace=false
FOMP-MQ.com.yss.fomp.YSSUCOBRIDGE.spring.cloud.stream.rocketmq.binder.name-server=***********:11701
FOMP-MQ.com.yss.fomp.YSSUCOBRIDGE.spring.jmx.enabled=false
FOMP-MQ.com.yss.fomp.YSSUCOBRIDGE.yss.rocketmq.isStartBroker=true
FOMP-MQ.com.yss.fomp.YSSUCOBRIDGE.yss.rocketmq.isStartNameServ=true
FOMP-MQ.com.yss.fomp.YSSUCOBRIDGE.yss.rocketmq.rocketmqHome=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/zhenghegongcheng2/FOMP-MQ/rocketmq11911
FOMP-MQ.com.yss.fomp.YSSUCOBRIDGE.yss.rocketmq.server.broker.brokerClusterName=DefaultCluster
FOMP-MQ.com.yss.fomp.YSSUCOBRIDGE.yss.rocketmq.server.broker.brokerIP1=***********
FOMP-MQ.com.yss.fomp.YSSUCOBRIDGE.yss.rocketmq.server.broker.brokerId=0
FOMP-MQ.com.yss.fomp.YSSUCOBRIDGE.yss.rocketmq.server.broker.brokerName=broker-c
FOMP-MQ.com.yss.fomp.YSSUCOBRIDGE.yss.rocketmq.server.broker.clientManageThreadPoolNums=16
FOMP-MQ.com.yss.fomp.YSSUCOBRIDGE.yss.rocketmq.server.broker.defaultTopicQueueNums=16
FOMP-MQ.com.yss.fomp.YSSUCOBRIDGE.yss.rocketmq.server.broker.diskSpaceCleanForciblyRatio=0.85
FOMP-MQ.com.yss.fomp.YSSUCOBRIDGE.yss.rocketmq.server.broker.diskSpaceWarningLevelRatio=0.90
FOMP-MQ.com.yss.fomp.YSSUCOBRIDGE.yss.rocketmq.server.broker.listenPort=10610
FOMP-MQ.com.yss.fomp.YSSUCOBRIDGE.yss.rocketmq.server.broker.maxMessageSize=5242880
FOMP-MQ.com.yss.fomp.YSSUCOBRIDGE.yss.rocketmq.server.broker.namesrvAddr=***********:11701
FOMP-MQ.com.yss.fomp.YSSUCOBRIDGE.yss.rocketmq.server.broker.osPageCacheBusyTimeOutMills=5000
FOMP-MQ.com.yss.fomp.YSSUCOBRIDGE.yss.rocketmq.server.broker.pullMessageThreadPoolNums=256
FOMP-MQ.com.yss.fomp.YSSUCOBRIDGE.yss.rocketmq.server.broker.sendMessageThreadPoolNums=256
FOMP-MQ.com.yss.fomp.YSSUCOBRIDGE.yss.rocketmq.server.broker.storePathCommitLog=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/zhenghegongcheng2/FOMP-MQ/rocketmq11911/store/commitlog
FOMP-MQ.com.yss.fomp.YSSUCOBRIDGE.yss.rocketmq.server.broker.storePathIndex=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/zhenghegongcheng2/FOMP-MQ/rocketmq11911/index
FOMP-MQ.com.yss.fomp.YSSUCOBRIDGE.yss.rocketmq.server.broker.storePathRootDir=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/zhenghegongcheng2/FOMP-MQ/rocketmq11911/store
FOMP-MQ.com.yss.fomp.YSSUCOBRIDGE.yss.rocketmq.server.broker.useReentrantLockWhenPutMessage=true
FOMP-MQ.com.yss.fomp.YSSUCOBRIDGE.yss.rocketmq.server.broker.waitTimeMillsInSendQueue=2000
FOMP-MQ.com.yss.fomp.YSSUCOBRIDGE.yss.rocketmq.server.namesrv.listenPort=11701
FOMP-MQ.com.yss.fomp.YSSUCOBRIDGE.yss.rocketmq.server.namesrv.serverSelectorThreads=32
FOMP-MQ.com.yss.fomp.YSSUCOBRIDGE.yss.rocketmq.server.namesrv.serverWorkerThreads=128
FOMP-MQ.com.yss.fomp.YSSUCOBRIDGE.yss.zookeeper.server.clientPort=5181
FOMP-PCF-BUSINESS.com.yss.fomp.YSSUCOBRIDGE.eureka.client.enabled=false
FOMP-PCF-BUSINESS.com.yss.fomp.YSSUCOBRIDGE.fomp.services.alias.enable=true
FOMP-PCF-BUSINESS.com.yss.fomp.YSSUCOBRIDGE.fomp.services.alias.oldServiceIds[0]=osgi-pcf
FOMP-PCF-BUSINESS.com.yss.fomp.YSSUCOBRIDGE.fomp.swagger.enabled=true
FOMP-PCF-BUSINESS.com.yss.fomp.YSSUCOBRIDGE.logging.config=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/global/logConfig.xml
FOMP-PCF-BUSINESS.com.yss.fomp.YSSUCOBRIDGE.plugin.deployment.client.types[0].code=pcf
FOMP-PCF-BUSINESS.com.yss.fomp.YSSUCOBRIDGE.plugin.deployment.client.types[0].name=PCF篮子任务插件
FOMP-PCF-BUSINESS.com.yss.fomp.YSSUCOBRIDGE.plugin.pluginPath=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/fomp/plugins
FOMP-PCF-BUSINESS.com.yss.fomp.YSSUCOBRIDGE.plugin.runMode=prod
FOMP-PCF-BUSINESS.com.yss.fomp.YSSUCOBRIDGE.plugin.uploadTempPath=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/fomp/temp
FOMP-PCF-BUSINESS.com.yss.fomp.YSSUCOBRIDGE.rocketmq.client.localOffsetStoreDir=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/zhenghegongcheng2/rocketmq11911/PCF/.rocketmq_offsets11911
FOMP-PCF-BUSINESS.com.yss.fomp.YSSUCOBRIDGE.spring.application.name=fomp-pcf-business
FOMP-PCF-BUSINESS.com.yss.fomp.YSSUCOBRIDGE.yss.fomp.license.enable=true
FOMP-PCF-BUSINESS.com.yss.fomp.YSSUCOBRIDGE.yss.fomp.license.server.enable=true
FOMP-PCF-BUSINESS.com.yss.fomp.YSSUCOBRIDGE.yss.fomp.license.server.path=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/global
FOMP-PCF-BUSINESS.com.yss.fomp.YSSUCOBRIDGE.yss.zookeeper.server.clientPort=5181
FOMP-TAQS.com.yss.fomp.YSSUCOBRIDGE.eureka.client.enabled=false
FOMP-TAQS.com.yss.fomp.YSSUCOBRIDGE.fomp.services.alias.oldServiceIds[0]=fomp-taqs-bridge-crust
FOMP-TAQS.com.yss.fomp.YSSUCOBRIDGE.fomp.services.alias.oldServiceIds[1]=osgi-taqs
FOMP-TAQS.com.yss.fomp.YSSUCOBRIDGE.logging.config=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/global/logConfig.xml
FOMP-TAQS.com.yss.fomp.YSSUCOBRIDGE.plugin.deployment.client.types[0].code=PLUGIN_TAQSCLEAR
FOMP-TAQS.com.yss.fomp.YSSUCOBRIDGE.plugin.deployment.client.types[0].name=TA清算生成插件
FOMP-TAQS.com.yss.fomp.YSSUCOBRIDGE.rocketmq.client.localOffsetStoreDir=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/zhenghegongcheng2/rocketmq11911/FOMP-TAQS/.rocketmq_offsets11911
FOMP-TAQS.com.yss.fomp.YSSUCOBRIDGE.spring.application.name=fomp-taqs
FOMP-TAQS.com.yss.fomp.YSSUCOBRIDGE.yss.zookeeper.server.clientPort=5181
FOMP-UNIFYPAY.com.yss.fomp.YSSUCOBRIDGE.eureka.client.enabled=false
FOMP-UNIFYPAY.com.yss.fomp.YSSUCOBRIDGE.fomp.services.alias.oldServiceIds[0]=fomp-unifypay-bridge-crust
FOMP-UNIFYPAY.com.yss.fomp.YSSUCOBRIDGE.fomp.services.alias.oldServiceIds[1]=osgi-unifypay
FOMP-UNIFYPAY.com.yss.fomp.YSSUCOBRIDGE.logging.config=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/global/logConfig.xml
FOMP-UNIFYPAY.com.yss.fomp.YSSUCOBRIDGE.plugin.deployment.client.types[0].code=PLUGIN_UNIFYPAYCLEAR
FOMP-UNIFYPAY.com.yss.fomp.YSSUCOBRIDGE.plugin.deployment.client.types[0].name=支付指令生成插件
FOMP-UNIFYPAY.com.yss.fomp.YSSUCOBRIDGE.rocketmq.client.localOffsetStoreDir=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/zhenghegongcheng2/rocketmq11911/FOMP-UNIFYPAY/.rocketmq_offsets11911
FOMP-UNIFYPAY.com.yss.fomp.YSSUCOBRIDGE.spring.application.name=fomp-unifypay
FOMP-UNIFYPAY.com.yss.fomp.YSSUCOBRIDGE.yss.zookeeper.server.clientPort=5181
FOMP-ZOOKEEPER.com.yss.fomp.YSSUCOBRIDGE.com.yss.startStatusCheck.enable=false
FOMP-ZOOKEEPER.com.yss.fomp.YSSUCOBRIDGE.logging.config=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/global/logConfig.xml
FOMP-ZOOKEEPER.com.yss.fomp.YSSUCOBRIDGE.spring.application.name=FOMP-ZOOKEEPER
FOMP-ZOOKEEPER.com.yss.fomp.YSSUCOBRIDGE.spring.jmx.enabled=false
FOMP-ZOOKEEPER.com.yss.fomp.YSSUCOBRIDGE.yss.zookeeper.isStart=true
FOMP-ZOOKEEPER.com.yss.fomp.YSSUCOBRIDGE.yss.zookeeper.server.autopurge.purgeInterval=24
FOMP-ZOOKEEPER.com.yss.fomp.YSSUCOBRIDGE.yss.zookeeper.server.autopurge.snapRetainCount=10
FOMP-ZOOKEEPER.com.yss.fomp.YSSUCOBRIDGE.yss.zookeeper.server.clientPort=5181
FOMP-ZOOKEEPER.com.yss.fomp.YSSUCOBRIDGE.yss.zookeeper.server.dataDir=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/FOMP-ZOOKEEPER/zookeeper2/data/zookeeper
FOMP-ZOOKEEPER.com.yss.fomp.YSSUCOBRIDGE.yss.zookeeper.server.dataLogDir=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/FOMP-ZOOKEEPER/zookeeper2/logs/zookeeper
FOMP-ZOOKEEPER.com.yss.fomp.YSSUCOBRIDGE.yss.zookeeper.server.initLimit=20
FOMP-ZOOKEEPER.com.yss.fomp.YSSUCOBRIDGE.yss.zookeeper.server.maxSessionTimeout=60000
FOMP-ZOOKEEPER.com.yss.fomp.YSSUCOBRIDGE.yss.zookeeper.server.minSessionTimeout=30000
FOMP-ZOOKEEPER.com.yss.fomp.YSSUCOBRIDGE.yss.zookeeper.server.snapCount=100000
FOMP-ZOOKEEPER.com.yss.fomp.YSSUCOBRIDGE.yss.zookeeper.server.syncLimit=10
FOMP-ZOOKEEPER.com.yss.fomp.YSSUCOBRIDGE.yss.zookeeper.server.tickTime=3000
#true调试模式，不进行tc、esb服务

KAFKA-SYSTEM-CLOSEMARKET=testbs
#净值披露下发接口参数配置，不同接口信息以”,“隔开
NVDisclosureInterface=INFO_PL_NAV_JYLCTA,INFO_PL_NAV_JYLCFZTA
OCP-APP.com.yss.fomp.YSSUCOBRIDGE.camunda.bpm.database.jdbc-batch-processing=false
OCP-APP.com.yss.fomp.YSSUCOBRIDGE.camunda.bpm.database.schemaUpdate=false
OCP-APP.com.yss.fomp.YSSUCOBRIDGE.camunda.bpm.enabled=false
OCP-APP.com.yss.fomp.YSSUCOBRIDGE.camunda.bpm.historyLevel=audit
OCP-APP.com.yss.fomp.YSSUCOBRIDGE.camunda.bpm.job-execution.enabled=false
OCP-APP.com.yss.fomp.YSSUCOBRIDGE.com.yss.monitor.client.executor.exeMonitorexecute.core.threadcount=100
OCP-APP.com.yss.fomp.YSSUCOBRIDGE.com.yss.monitor.client.executor.exeMonitorexecute.max.threadcount=200
OCP-APP.com.yss.fomp.YSSUCOBRIDGE.com.yss.monitor.client.executor.partitionExecutor.core.threadcount=100
OCP-APP.com.yss.fomp.YSSUCOBRIDGE.com.yss.monitor.client.executor.partitionExecutor.max.threadcount=200
OCP-APP.com.yss.fomp.YSSUCOBRIDGE.com.yss.monitor.executor.execute.core.threadcount=100
OCP-APP.com.yss.fomp.YSSUCOBRIDGE.com.yss.monitor.executor.execute.max.threadcount=200
OCP-APP.com.yss.fomp.YSSUCOBRIDGE.com.yss.osgi.custom.classloader.load-path=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/plugins/YSSMONITOR
OCP-APP.com.yss.fomp.YSSUCOBRIDGE.com.yss.osgi.custom.classloader.load-path-tmp=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/plugins/YSSMONITOR_TEMP
OCP-APP.com.yss.fomp.YSSUCOBRIDGE.fomp.services.alias.oldServiceIds[0]=osgi-basebusiness
OCP-APP.com.yss.fomp.YSSUCOBRIDGE.fomp.services.alias.oldServiceIds[1]=ocp-app
OCP-APP.com.yss.fomp.YSSUCOBRIDGE.fomp.services.alias.oldServiceIds[2]=ocp-osgi-executor
OCP-APP.com.yss.fomp.YSSUCOBRIDGE.fomp.services.alias.oldServiceIds[3]=ocp-deploy
OCP-APP.com.yss.fomp.YSSUCOBRIDGE.fomp.services.alias.oldServiceIds[4]=ocp-executor
OCP-APP.com.yss.fomp.YSSUCOBRIDGE.fomp.services.alias.oldServiceIds[5]=ocp-data
OCP-APP.com.yss.fomp.YSSUCOBRIDGE.fomp.services.alias.oldServiceIds[6]=yssmonitor-testor
OCP-APP.com.yss.fomp.YSSUCOBRIDGE.logging.config=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/global/logConfig.xml
OCP-APP.com.yss.fomp.YSSUCOBRIDGE.rocketmq.client.localOffsetStoreDir=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/rocketmq11911/OCP-APP/.rocketmq_offsets11911
OCP-APP.com.yss.fomp.YSSUCOBRIDGE.spring.application.name=ocp-app
OCP-APP.com.yss.fomp.YSSUCOBRIDGE.yss.zookeeper.server.clientPort=5181
#请求的响应超时时间(分)
RECEIVETIMEOUT=3
#STORY #74823 【深国投信托】核心ta数据与4.5估值系统实现对接 END
#STORY #93863 【深国投信托】【4.6】FOF行情、份额、交易确认信息新增webservice数据接口
#深国投ESB产品持仓情况服务编码
SGTESBCPCCQKSERVICEID=I0240000000003
#深国投ESB净值行情服务编码
SGTESBJZHQSERVICEID=I0240000000002
#深国投ESB开放申赎服务编码
SGTESBKFSSSERVICEID=I0240000000004
#深国投ESB用户密码
SGTESBPWD=123456
#深国投ESB请求系统编码 ESB为每个接入系统进行唯一编码 
SGTESBSID=I018
#STORY #74823 【深国投信托】核心ta数据与4.5估值系统实现对接 START
#深国投ESB WEBSERVICE服务URL  测试环境：http://10.132.122.135/esbservice.wsdl  生产环境：http://esb.crctrust.com/esbservice.wsdl 
SGTESBSURL=http://10.132.122.135/esbservice.wsdl
#深国投ESB用户
SGTESBUSER=esb
#深国投ESB交易申请服务编码
SGTJYSQESBSERVICEID=I0040000000017
#深国投TOS请求系统编码
SGTJYXTESBSID=I024
#深国投ESB交易确认服务编码
SGTTAJYQRESBSERVICEID=I0040000000013
#上汽报表系统一次性推送数据的最大数
SQ.pushLength=2000
TrigDropdownOnInput=true
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.fomp.cache.read-no-lock.regions=fomp-fa-cachemanager_secbase
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.fomp.services.alias.oldServiceIds[0]=fomp-bridge-crust
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.fomp.services.alias.oldServiceIds[1]=fomp-uco-crust
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.fomp.services.alias.oldServiceIds[2]=osgi-basebusiness
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.fomp.services.alias.oldServiceIds[3]=osgi-uco
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.fomp.services.alias.oldServiceIds[4]=osgi-elecreco
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.fomp.services.alias.oldServiceIds[5]=fomp-uco-crust-mic
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.fomp.services.alias.oldServiceIds[6]=fomp-fa-clear-automatic
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.fomp.services.alias.oldServiceIds[7]=fomp-fa-act-automatic
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.fomp.services.alias.oldServiceIds[8]=osgi-bami
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.fomp.services.alias.oldServiceIds[9]=osgi-datamig
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.fomp.sso.client.excludeUrls=/**/ws/**,/**/ws_shell/**
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.fomp.task.client.registry.enable=false
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.logging.config=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/global/logConfig.xml
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.osgi.task.dispatch.url=http://***********:8083/FOMP-FAST/ws/com/yss/fast/task/client/service/ISchedulerXcRestService/schedulerXc
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.plugin.deployment.client.types[0].code=OLD_PLUGIN_CLEAR
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.plugin.deployment.client.types[0].name=清算插件(4.5)
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.plugin.deployment.client.types[1].code=PLUGIN_AUTOMATIC_CLEAR
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.plugin.deployment.client.types[1].name=自动化插件-清算
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.plugin.deployment.client.types[2].code=PLUGIN_AUTOMATIC_ACT
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.plugin.deployment.client.types[2].name=自动化插件-核算
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.plugin.deployment.client.types[3].code=PLUGIN_API
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.plugin.deployment.client.types[3].name=算法API
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.plugin.deployment.client.types[4].code=automaticClear
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.plugin.deployment.client.types[4].label=automatic
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.plugin.deployment.client.types[4].name=自动化清算插件
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.plugin.deployment.client.types[5].code=automaticAct
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.plugin.deployment.client.types[5].label=automatic
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.plugin.deployment.client.types[5].name=自动化核算插件
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.rocketmq.client.localOffsetStoreDir=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/zhenghegongcheng2/rocketmq11911/YSSUCO/.rocketmq_offsets11911
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.spring.application.name=YSSUCO
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.spring.datasource.druid.maxActive=2000
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.spring.datasource.druid.minIdle=500
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.spring.liquibase.enabled=false
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.xc.data.applicationId=YSSCLEAR
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.yss.fomp.license.enable=true
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.yss.fomp.license.server.enable=true
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.yss.fomp.license.server.path=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/global
YSSUCO.com.yss.fomp.YSSUCOBRIDGE.yss.zookeeper.server.clientPort=5181
YSS_FILE=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/YSS_FILE
app_config_ip=***********
app_config_port=8083
#http类型设置 http或https，未设置则默认http
#httpType=http
#http类型设置 http或https，未设置则默认http
#httpType=http
auto.enable.kafka.broker=testsy
auto.enable.kafka.broker.ready=test01
#自动化是否开启，必须配置
#自动化是否开启，必须配置
autoEnable=true
########automatic config  end##################
#自动化第三方任务接入rest地址--配置实例
#com.yss.fast.task.automatic.outappname.testApp=http://localhost:8083/YSSUCOBRIDGE/ws/com/yss/fast/task/support/automatic/service/IAutomaticRestfulService/automatic
#自动化邮件策略SOFA报表导出任务映射
autoMailDirMap=/autoMailDir/
#禅道附件上传地址
autoTest.zenUrl=https://pm.ysstech.com:8071/index.php?m=jenkins&f=uploadFileApi&name=
#业务日志缓存的清理周期，单位分钟
bizlog_clear_cycle=120
#业务缓存刷新时是否发送通知消息的开关 false: 不启用； true: 启用，未配置时默认不启用。
business.cache.refresh.notify.enable=false
#系统是否使用集群环境
cluster=false
com.yss.fast.single.restful.shell.productLine.ignore=AUTOMATIC
######统计服务器信息频率（秒）
com.yss.fast.task.automatic.SystemInfoFrequece=60
#自动化日志是否入库标志  true 为入库  false不入库
com.yss.fast.task.automatic.autolog.indb=false
##########多实例文件扫描线程数
com.yss.fast.task.automatic.filescan.muti.threadcount=16
##########单实例文件扫描线程数
com.yss.fast.task.automatic.filescan.single.threadcount=16
##### 自动化 实例管理 流程管理 执行 是否需要判断组合权限  true 需要。 false为不需要 
com.yss.fast.task.automatic.isCheckRight=true
######消息推送 消息的刷新频率 单位（秒）
com.yss.fast.task.automatic.msgpush.frequece=4
###### 消息推送查询组合进度批次大小
com.yss.fast.task.automatic.msgpush.queryBatchSize=20
######前台消息推送线程池
com.yss.fast.task.automatic.msgpush.threadcount=2
########  automatic  partition   execute threadcount
########  automatic  partitionexecute   execute threadcount
com.yss.fast.task.automatic.partitionexecute.threadcount=4
########  automatic  procInstStartExe   execute threadcount
com.yss.fast.task.automatic.procInstExe.threadcount=32
######是否开启服务器信息统计，默认不开启
com.yss.fast.task.automatic.startSystemInfoScheduleEnable=false
######任务完成线程池
com.yss.fast.task.automatic.taskcomplete.threadcount=64
########  automatic  task  execute threadcount
com.yss.fast.task.automatic.taskexecute.threadcount=32
####### 自动化核算任务线程池
com.yss.fast.task.automatic.taskexecute.threadcount_act=16
####### 自动化核算任务线程池 - 核算其他
com.yss.fast.task.automatic.taskexecute.threadcount_act_other=16
####### 自动化核算任务线程池 - 统计分析
com.yss.fast.task.automatic.taskexecute.threadcount_act_stock=16
####### 自动化核算清算线程池
com.yss.fast.task.automatic.taskexecute.threadcount_clr=16
####### 自动化数据导入、文件检查任务执行线程池
com.yss.fast.task.automatic.taskexecute.threadcount_imp=8
########automatic config  begin ##################
######## automatic  timer thread count 
com.yss.fast.task.automatic.timer.threadcount=4
#多实例流程手工执行时应用范围是否勾选组合信息 true 标识勾选,缺省时默认勾选;false 标识不勾选
#com.yss.fast.task.automatic.multiplexecute.productchecked=true
##### 自动化 上传流程时候是否需要 自动验证接口分组，适配当前上传环境  true 需要。 false为不需要 
com.yss.fast.task.automatic.uploadprocess.isCheckInterfaceGroupCode=false
#第三方平台接入--end

###异常是否包含 异常SQL 默认 false 不包含SQL 
com.yss.fast.task.isPrintErrorSql=true
com.yss.fast.task.oldtask.exclude=dataImportImpl,taskFileCheck,ExportReport
#sso.filter.deploy.console.enable=true
#gd.webSafe.deploy.console.enable=true
#gd.webSafe.start.report.enable=true
#webSafe.deploy.ctrl.enable=true
#WEBSERVICE证书配置：外部服务证书、私钥相关配置#
com.yss.fast.webservice.authority.Fast_Server_KeyStore=Fast_Server_KeyStore.jks
com.yss.fast.webservice.authority.alias=fastserver
#加密方式： cer为证书加密  pass为密码加密
com.yss.fast.webservice.authority.encrypttype=pass
com.yss.fast.webservice.authority.storepass=fast2018
######工作流监听器执行线程数
com.yss.fast.workflow.AbstractCustomListener.threadcount=50
com.yss.fomp.YSSUCOBRIDGE.feign.client.config.default.connectTimeout=3600000
com.yss.fomp.YSSUCOBRIDGE.feign.client.config.default.readTimeout=3600000
com.yss.fomp.YSSUCOBRIDGE.fomp.biz.log.insertSuccessDetail=true
com.yss.fomp.YSSUCOBRIDGE.spring.datasource.validationQuery=select 1
com.yss.fomp.YSSUCOBRIDGE.spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
com.yss.fomp.YSSUCOBRIDGE.spring.jpa.properties.hibernate.jdbc.batch_size=0
com.yss.fomp.YSSUCOBRIDGE.spring.jpa.properties.hibernate.jdbc.batch_versioned_data=false
com.yss.fomp.YSSUCOBRIDGE.yss.restful.connectTimeout=3600000
com.yss.fomp.YSSUCOBRIDGE.yss.restful.readTimeout=3600000
#数据定时清理每日清理数据阀值
#dbsetting_driverClass=com.huawei.opengauss.jdbc.Driver
#dbsetting_password=Gz##300V45JL
#dbsetting_url=jdbc:dm://************:5243?socketTimeout=6000000
#http类型设置 http或https，未设置则默认http
#httpType=http

#数据库配置（用户、服务端口、密码、IP、实例名）
#dbsetting_user=TPPV55TEST2

#数据定时清理每日清理数据阀值
#dbsetting_driverClass=com.huawei.opengauss.jdbc.Driver
#dbsetting_password=Gz##300V45JL
#dbsetting_url=jdbc:dm://************:5243?socketTimeout=6000000
#http类型设置 http或https，未设置则默认http
#httpType=http

#数据库配置（用户、服务端口、密码、IP、实例名）
#dbsetting_user=TPPV55TEST2

com.yss.fomp.YSSUCOBRIDGE.yss.zookeeper.address=***********:5181
#com.yss.fomp.YSSUCOBRIDGE.com.yss.fomp.mq.proxy.port=5007
#前端自动连接测试周期（秒）
connInterval=60
#前端自动连接测试次数
connTimes=3
#数据导入是否按照组合过滤文件路径
dataImportPathFilterByPort=false
db.upgrade.fompMode=true
#数据定时清理每日清理数据阀值
dbCleanThresholdPerDay=10000
dbsetting_driverClass=com.huawei.gaussdb.jdbc.Driver
dbsetting_ip=**********
dbsetting_password=fmOXb9UwAMn/Ws0xFyVi0jyBs+7/vQvYkdBTFuloIh0=
dbsetting_port=8000
#dbsetting_sid=wbgzdb1
#dbsetting_url=jdbc:gaussdb://**********:8000/wbgzdb?targetServerType=master&amp;uppercaseAttributeName=true&amp;prepareThreshold=1&amp;batchMode=on&amp;fetchsize=10&amp;preparedStatementCacheQueries=1500&amp;preparedStatementCacheSizeMiB=20&amp;loggerLevel=off
dbsetting_url=***************************************************************************************************************************************************************************************************************************************************************************************************
dbsetting_user=wbgz
dbupgrade.web.enable=true
dbupgrade_startInit=false
delete_biz_log_act_item_code=true
deploy.ignore.checkjar=com.yss.fast.config.check.mq,com.yss.fast.config.check.zoo
#dbupgrade_startInit=true
# 框架配置类去除启动restful接口改造
#deploy.ignore.checkjar=com.yss.fast.config.check.server
#渠道编号，由客户方统一分配
dglc.channel.code=channelCode
#STORY #111233 【东莞银行理财】上传总账出发ESB报文,请求处理结果
#接口请求地址
dglc.esb.url=https://www.baidu.com
#请求的命名空间，由客户方提供
dglc.namespace=https://www.qq.com/
#请求间隔时间(分钟)
dglc.period=1
#请求重复次数
dglc.requestCount=3
#场景ID
dglc.scene.number=02
#场景版本号  默认10
dglc.scene.version=10
#服务ID
dglc.serve.number=S0120025
#服务版本号  默认100
dglc.service.version=100
#系统标识，由客户方统一分配
dglc.sysid=ucobridge
#任务调度-账务核对数据库连接
dispatch_financial_check_dbmark=DB2_GMS
#数据库用户密码是否启用动态密码，true启用，false不启用，默认false
dynamic_password=false
#主动屏蔽升级组件的页面，可以配置true开启该页面的查询
#dbupgrade.web.enable=true

#STORY #43148 【中国银行】估值系统和披露报表与中行网银交互需求
elec_old_mode=false
elec_result_topic=gzyy_dzzx_dzdzresult_bxwb_duanshuolian
enable.update.ciden=true
#sso配置
#sso客户端地址，默认和app_config的ip和端口一致
#sso_client_url = ***********:8083
##sso服务端地址，默认和sso_client_url一致
#sso_server_url = ***********:8083

#系统加密类型: sm 表示国密 yss 表示最老的可逆的加密算法 sha 表示用户密码使用SHA加密、WEBSERVICE的RSA加密
encrypt_type=sm
#程序bundle部署到单独的文件目录，包括主程序bundle和子程序bundle
fastPlugin=false
#系统是否使用新权限体系
fastRight=true
#重建mq文件系统时每个主题默认队列数
fastmq.defaultTopicQueueNum=1
#MQ配置
#启动mq时是否需要重建mq文件系统
fastmq.startclearmq=true
#系统配置文件（YSS_APP）根目录路径
#YSS_HOME=G:\\test
#系统配置文件(global)目录的路径
#GLOABL_PATH=G:\\test
#传真连接最大次数（发送传真连接超时，可以重连）
fax.connect.times=3
fullGoal_econfirm_rabitMqMessage_admin=guest
#若开启发送RabitMq消息，连接Mq服务器参数在下面配置
fullGoal_econfirm_rabitMqMessage_ip=************
fullGoal_econfirm_rabitMqMessage_port=5672
fullGoal_econfirm_rabitMqMessage_proExg=EXG_MsaAccessService
fullGoal_econfirm_rabitMqMessage_pwd=guest
fullGoal_econfirm_rabitMqMessage_routkey=FcupToPayService
fullGoal_econfirm_rabitMqMessage_virHost=/
#是否发送RabitMq消息的开关
fullGoal_netvalue_locked_sendMessage=false
#STORY #135342 【桂林银行理财】对接行内核心系统交互方式新增解析失败文件
#交互新增esb文件根路径
gllc.esb.path=
#交互esb重复请求次数
gllc.esb.requestCount=
#交互esb请求等待响应时间：单位（ms）
gllc.esb.timeout=
#STORY #124015 【桂林银行理财】对接行内核心系统，通过ESB传输总账数据
#交互新增esb请求路径
gllc.esb.url=
#是否按照权限过滤数据（grouptextbox数据权限过滤）
grouptextbox.data.right=false
#是否按照权限过滤数据（grouptextbox数据过滤）
#是否按照权限过滤数据（grouptextbox数据过滤）
grouptextbox.right=true
#STORY #87165 嘉实基金-估值4.5系统通过发布-订阅模式将消息直接发布到RabbitMQ队列，下游系统根据需要订阅该消息
#嘉实基金是否发送RabitMq消息的开关  false: 不启用； true: 启用。
harvestFund_netvalue_locked_sendMessage=false
#健康探测状态，默认为enable（可用），可设置为disable（不可用）
jndiStatus=enable
#许可证过期提前提醒天数
licenseExpirePreWarnDays=7
# STORY #78658 手工执行核算和净值确认发送MQ消息 
manual_complete_sendMessage=false
#管控日志清理周期，为空或小于0时不清理，大于等于0时清理，示例: 为0时清理今天之前的所有数据，为1时清理昨天之前的所有数据，为90时清理90天之前的所有数据
monitorLogClearPeriod=-1
#管控日志中清理重复的数据
monitorLogClearRepeat=false
mq_internet_address=***********:3007
#MQ消息通道端口和地址
#mq_channel_port=45566
#mq_channel_address=***********:8083
##MQ服务地址端口
mq_server_address=***********:11701
newScheduleEngineUrl=http://***********:8083/FOMP-FAST
schedulerThreadCount=140
#spring.datasource.druid.connectTimeout=1800000
#spring.datasource.druid.socketTimeout=1800000
#taskExeSpecThreadCount=dataImportImpl:80,assetclear:80,valuation:110,stockStats:120
taskExeSpecThreadCount=dataImportImpl:80,assetclear:80,valuation:230,stockStats:205
thirdParty.mq.type=kafka
#估值核算属性设置
#初始化是否开启业务检测(STORY #31867 中信证券-估值表指标项重复(业务处理数据重叠)
validateRepeateOper=true
#STORY #96580 STORY #91131基金净值行情自动发布接口（技术变更）
#估值核算、统计分析前 是否发送RabitMq消息的开关
valuation_begin_sendMessage=false
xc.deploy.host=http://***********:8083/FOMP-FAST
xc.route.host=http://***********:8083/YSSCLEAR
yss.osgi.yss-app-path=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps
zookeeper.port=5181
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.fomp.fa.clear.calc.threadPool.clearCalcCallbackExecute.corePoolSize=24
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.fomp.fa.clear.calc.threadPool.clearCalcCommExecute.corePoolSize=32
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.fomp.fa.clear.calc.threadPool.clearCalcDkfyExecute.corePoolSize=24
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.fomp.fa.clear.calc.threadPool.clearCalcExecute.corePoolSize=220
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.fomp.fa.clear.calc.threadPool.clearCalcSplitExecute.corePoolSize=24
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.fomp.fa.clear.calc.threadPool.clearCalcStorageExecute.corePoolSize=24
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.fomp.fa.clear.data.grpc.enabled=false
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.fomp.fa.clear.data.threadPool.clearBackSendExecute.corePoolSize=24
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.fomp.fa.clear.data.threadPool.clearDataCurrDelExecute.corePoolSize=32
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.fomp.fa.clear.data.threadPool.clearDataDispatchExecute.corePoolSize=64
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.fomp.fa.clear.data.threadPool.clearDataPortSignExecute.corePoolSize=220
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.fomp.fa.clear.data.threadPool.clearDataSendExecute.corePoolSize=24
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.fomp.fa.clear.data.threadPool.clearDataTransExecute.corePoolSize=220
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.fomp.fa.clear.data.threadPool.clearPluginExecute.corePoolSize=90
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.fomp.fa.clear.data.threadPool.clearTransCallbackExecute.corePoolSize=24
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.fomp.fa.clear.portDispatch.configPath=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/yssclear/config
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.fomp.fa.clear.portDispatch.enabled=true
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.fomp.fa.clear.route.dispatch.enabled=false
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.fomp.fa.clear.route.dispatch.maxParallelCount=50
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.fomp.fa.clear.route.threadPool.clearRouteCallBackExecute.corePoolSize=24
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.fomp.fa.clear.route.threadPool.clearRouteDispatchExecute.corePoolSize=24
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.fomp.fa.clear.route.threadPool.clearRouteExecute.corePoolSize=24
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.fomp.fa.clear.route.threadPool.clearRouteSendExecute.corePoolSize=24
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.fomp.fa.clear.trans.threadPool.clearTransCalcInvoke.corePoolSize=1
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.fomp.fa.clear.trans.threadPool.clearTransDataTransExecute.corePoolSize=1
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.fomp.fa.clear.trans.threadPool.clearTransPluginExecute.corePoolSize=1
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.fomp.fa.clear.trans.threadPool.clearTransSceneOperDispatchExecute.corePoolSize=1
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.fomp.fa.clear.trans.threadPool.clearTransSceneOperGroupExecute.corePoolSize=1
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.fomp.sso.client.excludeUrls=/**,**/
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.fomp.swagger.enabled=true
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.grpc.client.default.enableKeepAlive=true
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.grpc.client.default.keepAliveWithoutCalls=true
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.grpc.client.default.negotiationType=plaintext
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.grpc.client.thread-pool.coreThread=8
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.grpc.client.thread-pool.maxQueueWaitNum=400000
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.grpc.client.thread-pool.maxThread=8
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.grpc.server.maxInboundMessageSize=-1
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.grpc.server.port=18183
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.logging.config=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/global/logConfig.xml
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.plugin.deployment.client.applicationGroup=dataServices
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.plugin.deployment.client.types[0].code=PLUGIN_CLEAR
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.plugin.deployment.client.types[0].name=清算接口
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.plugin.deployment.server.initDirectory=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/fomp/plugin-deploy-server/init
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.plugin.deployment.server.installDirectory=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/fomp/plugin-deploy-server/install
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.plugin.deployment.server.uploadDirectory=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/fomp/plugin-deploy-server/upload
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.plugin.pluginPath=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/fomp/plugins
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.plugin.uploadTempPath=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/fomp/temp
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.rocketmq.client.localOffsetStoreDir=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/zhenghegongcheng2/rocketmq11911/YSSCLEAR/.rocketmq_offsets11911
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.server.servlet.context-path=/YSSCLEAR
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.spring.application.name=YSSCLEAR
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.spring.datasource.druid.maxActive=1000
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.spring.datasource.druid.maxWait=600000
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.spring.datasource.druid.minIdle=100
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.spring.datasource.hikari.connection-timeout=30000
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.spring.datasource.hikari.idle-timeout=600000
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.spring.datasource.hikari.isRegisterMbeans=true
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.spring.datasource.hikari.leakDetectionThreshold=10000
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.spring.datasource.hikari.max-lifetime=0
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.spring.datasource.hikari.maximum-pool-size=1200
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.spring.datasource.hikari.minimum-idle=600
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.spring.datasource.type=com.zaxxer.hikari.HikariDataSource
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.spring.jpa.properties.hibernate.session_factory.statement_inspector=com.yss.fomp.interceptor.JpaInterceptor
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.yss.fomp.license.enable=true
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.yss.fomp.license.server.enable=true
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.yss.fomp.license.server.path=/home/<USER>/wbgz/TongWebSingle/TongWeb7.0.4.9_M4_Enterprise_Linux/webapps/YSS_APP/global/
YSSCLEAR.com.yss.fomp.YSSUCOBRIDGE.yss.zookeeper.server.clientPort=5181
