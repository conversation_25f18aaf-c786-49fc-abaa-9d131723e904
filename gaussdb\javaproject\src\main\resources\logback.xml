<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 控制台输出 -->
    <appender name="Console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <charset>UTF-8</charset>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    
    <!-- 文件输出 -->
    <appender name="FileTest" class="ch.qos.logback.core.FileAppender">
        <file>${log.path}/${log.name}.log</file>
        <encoder>
            <charset>UTF-8</charset>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    
    <!-- JD<PERSON> Driver日志文件输出配置，支持日志回卷 -->
    <appender name="RollingFileJdbc" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>output/gsjdbc.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>output/gsjdbc-%d{yyyy-MM-dd}-%i.log</fileNamePattern>
            <maxFileSize>20MB</maxFileSize>
        </rollingPolicy>
        
        <!-- 过滤没有traceId或traceId为null的日志 -->
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator class="ch.qos.logback.classic.boolex.JaninoEventEvaluator">
                <expression>
                    return !message.matches(".*traceId[:\\s=]+[a-fA-F0-9]{32}.*");
                </expression>
            </evaluator>
            <onMatch>DENY</onMatch>
            <onMismatch>ACCEPT</onMismatch>
        </filter>
        
        <encoder>
            <charset>UTF-8</charset>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    
    <!-- 根日志器 -->
    <root level="INFO">
        <!-- <appender-ref ref="Console"/> -->
        <appender-ref ref="FileTest"/>
    </root>
    
    <!-- 指定JDBC Driver日志，级别为all，可查看所有日志，输出到gsjdbc.log文件中 -->
    <logger name="com.huawei.gaussdb.jdbc" level="INFO" additivity="false">
        <appender-ref ref="RollingFileJdbc"/>
    </logger>
</configuration>


