package com.example.entity;

import java.math.BigDecimal;

public class Product {
    private Integer productNo;
    private String name;
    private BigDecimal price;
    
    public Product() {}
    
    public Product(Integer productNo, String name, BigDecimal price) {
        this.productNo = productNo;
        this.name = name;
        this.price = price;
    }
    
    // Getters and Setters
    public Integer getProductNo() { return productNo; }
    public void setProductNo(Integer productNo) { this.productNo = productNo; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public BigDecimal getPrice() { return price; }
    public void setPrice(BigDecimal price) { this.price = price; }
    
    @Override
    public String toString() {
        return "Product{productNo=" + productNo + ", name='" + name + "', price=" + price + "}";
    }
}