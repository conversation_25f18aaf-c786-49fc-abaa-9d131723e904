@echo off
REM ============================================================================
REM 表配置验证脚本 - Windows版本
REM Table Configuration Validation Script - Windows Version
REM ============================================================================

echo.
echo ============================================================================
echo 表配置验证工具 Table Configuration Validator
echo ============================================================================
echo.

REM 检查Java环境
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Java环境，请确保已安装Java 8或更高版本
    echo Error: Java not found, please ensure Java 8+ is installed
    pause
    exit /b 1
)

REM 检查JAR文件是否存在
if not exist "target\stress-test-1.0-SNAPSHOT.jar" (
    echo 警告: 未找到编译后的JAR文件，正在编译...
    echo Warning: Compiled JAR not found, compiling...
    call mvn clean package -q
    if %errorlevel% neq 0 (
        echo 错误: 编译失败
        echo Error: Compilation failed
        pause
        exit /b 1
    )
)

echo 正在验证表配置...
echo Validating table configuration...
echo.

REM 运行验证工具
java -cp "target\stress-test-1.0-SNAPSHOT.jar" com.example.stress.util.TableConfigValidator

if %errorlevel% equ 0 (
    echo.
    echo ============================================================================
    echo ✅ 配置验证成功！可以运行压力测试程序
    echo ✅ Configuration validation successful! You can run the stress test
    echo ============================================================================
) else (
    echo.
    echo ============================================================================
    echo ❌ 配置验证失败！请检查配置和数据库连接
    echo ❌ Configuration validation failed! Please check config and database
    echo ============================================================================
    echo.
    echo 请检查以下项目 Please check the following:
    echo 1. 数据库连接配置 Database connection configuration
    echo 2. 表名配置 Table name configuration  
    echo 3. 数据库中是否存在表 Tables exist in database
    echo 4. 数据库用户权限 Database user permissions
)

echo.
pause
