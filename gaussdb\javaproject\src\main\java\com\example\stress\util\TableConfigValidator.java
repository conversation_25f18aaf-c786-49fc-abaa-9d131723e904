package com.example.stress.util;

import com.example.stress.config.TableNameConfig;
import com.example.stress.dao.TestDataDao;
import com.example.util.DatabaseUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.SQLException;

/**
 * 表配置验证工具
 * 用于验证配置的表名是否正确，以及表是否存在
 */
public class TableConfigValidator {
    private static final Logger logger = LoggerFactory.getLogger(TableConfigValidator.class);
    
    /**
     * 验证表配置
     */
    public static boolean validateConfiguration() {
        logger.info("=== Table Configuration Validation ===");
        
        // 1. 验证表名配置
        if (!TableNameConfig.validateTableNames()) {
            logger.error("Table name configuration validation failed");
            return false;
        }
        
        // 2. 验证数据库连接和表存在性
        try (Connection connection = DatabaseUtil.getConnection()) {
            TestDataDao testDataDao = new TestDataDao();
            
            if (!testDataDao.validateTablesExist(connection)) {
                logger.error("Required tables do not exist in database");
                return false;
            }
            
            logger.info("All validations passed successfully");
            return true;
            
        } catch (SQLException e) {
            logger.error("Database connection or validation failed", e);
            return false;
        }
    }
    
    /**
     * 打印配置信息
     */
    public static void printConfiguration() {
        logger.info("=== Current Table Configuration ===");
        TableNameConfig.printConfiguration();
        
        logger.info("=== Database Connection Test ===");
        try (Connection connection = DatabaseUtil.getConnection()) {
            logger.info("Database connection: SUCCESS");
            
            TestDataDao testDataDao = new TestDataDao();
            boolean tablesExist = testDataDao.validateTablesExist(connection);
            logger.info("Tables existence check: {}", tablesExist ? "SUCCESS" : "FAILED");
            
        } catch (SQLException e) {
            logger.error("Database connection failed", e);
        }
    }
    
    /**
     * 主方法，可以独立运行进行配置验证
     */
    public static void main(String[] args) {
        logger.info("Starting table configuration validation...");
        
        // 打印配置信息
        printConfiguration();
        
        // 执行验证
        boolean isValid = validateConfiguration();
        
        if (isValid) {
            logger.info("✅ Configuration validation completed successfully");
            logger.info("You can now run the stress test program");
        } else {
            logger.error("❌ Configuration validation failed");
            logger.error("Please check the following:");
            logger.error("1. Database connection configuration");
            logger.error("2. Table names in connection-pool.properties");
            logger.error("3. Tables exist in database (run create_tables.sql)");
            logger.error("4. Database user permissions");
        }
        
        System.exit(isValid ? 0 : 1);
    }
}
