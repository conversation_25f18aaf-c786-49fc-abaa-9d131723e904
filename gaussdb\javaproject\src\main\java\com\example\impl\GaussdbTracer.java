package com.example.impl;

import com.huawei.gaussdb.jdbc.log.Tracer;

public class GaussdbTracer implements Tracer {
    
    private static final ThreadLocal<String> traceIdHolder = new ThreadLocal<>();
    
    @Override
    public String getTraceId() {
        return traceIdHolder.get();
    }
    
    public void setTraceId(String traceId) {
        traceIdHolder.set(traceId);
    }
    
    public void reset() {
        traceIdHolder.remove();
    }
    
    // 静态工厂方法
    public static GaussdbTracer getInstance() {
        return new GaussdbTracer();
    }
}