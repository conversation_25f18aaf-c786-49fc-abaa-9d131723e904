#!/bin/bash

# GaussDB数据迁移工具启动脚本
# GaussDB Data Migration Tool Startup Script

# 设置Java选项
JAVA_OPTS="-Xms512m -Xmx2g -XX:+UseG1GC"

# JAR文件路径
JAR_FILE="target/gaussdb-data-migration-1.0.0-fat.jar"

# 检查JAR文件是否存在
if [ ! -f "$JAR_FILE" ]; then
    echo "Error: JAR file not found: $JAR_FILE"
    echo "Please run 'mvn clean package' first to build the project."
    exit 1
fi

# 显示帮助信息
show_help() {
    echo "GaussDB Data Migration Tool Startup Script"
    echo "==========================================="
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  help                    Show help information"
    echo "  config                  Show current configuration"
    echo "  migrate                 Run standard migration"
    echo "  date-migrate            Run date-based migration"
    echo "  examples                Show usage examples"
    echo ""
    echo "For detailed options, run: $0 help"
    echo ""
}

# 显示配置信息
show_config() {
    echo "Showing current configuration..."
    java $JAVA_OPTS -jar "$JAR_FILE" --config
}

# 显示使用示例
show_examples() {
    echo "Usage Examples"
    echo "=============="
    echo ""
    echo "1. Basic Migration:"
    echo "   $0 migrate --source-table source_table --target-table target_table --plan-code plan001"
    echo ""
    echo "2. Migration with Custom Parameters:"
    echo "   $0 migrate -st source_table -tt target_table -p plan001 -bs 5000 -tp 8"
    echo ""
    echo "3. Migration from StarRocks:"
    echo "   $0 migrate -st analytics_table -tt target_table -p plan001 -sdb starrocks"
    echo ""
    echo "4. Date-based Migration:"
    echo "   $0 date-migrate -st log_table -tt target_table -p plan001 \\"
    echo "      -dc log_timestamp -sd 2024-01-01 -ed 2024-01-31 -bd 7"
    echo ""
    echo "5. Date-based Migration from StarRocks:"
    echo "   $0 date-migrate -st analytics_fact -tt target_table -p plan001 \\"
    echo "      -sdb starrocks -dc dt -sd 2024-01-01 -ed 2024-01-31 -bd 1"
    echo ""
    echo "6. Legacy Positional Arguments (still supported):"
    echo "   java $JAVA_OPTS -jar \"$JAR_FILE\" source_table target_table plan001 2000 4"
    echo ""
}

# 运行标准迁移
run_migrate() {
    echo "Running standard migration..."
    java $JAVA_OPTS -jar "$JAR_FILE" --mode migrate "$@"
}

# 运行日期分批迁移
run_date_migrate() {
    echo "Running date-based migration..."
    java $JAVA_OPTS -jar "$JAR_FILE" --mode date-migrate "$@"
}

# 主逻辑
case "$1" in
    help|--help|-h)
        java $JAVA_OPTS -jar "$JAR_FILE" --help
        ;;
    config|--config|-c)
        show_config
        ;;
    migrate)
        shift
        run_migrate "$@"
        ;;
    date-migrate)
        shift
        run_date_migrate "$@"
        ;;
    examples)
        show_examples
        ;;
    "")
        show_help
        ;;
    *)
        # 直接传递所有参数给Java程序
        java $JAVA_OPTS -jar "$JAR_FILE" "$@"
        ;;
esac
