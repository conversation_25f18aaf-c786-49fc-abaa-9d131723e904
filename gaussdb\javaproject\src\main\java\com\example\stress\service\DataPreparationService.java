package com.example.stress.service;

import com.example.stress.dao.TestDataDao;
import com.example.stress.model.TableStructure;
import com.example.stress.model.TestData;
import com.example.stress.util.DynamicDataGenerator;
import com.example.stress.util.TableStructureManager;
import com.example.util.DatabaseUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * 数据准备服务
 * 负责初始化测试数据
 */
public class DataPreparationService {
    private static final Logger logger = LoggerFactory.getLogger(DataPreparationService.class);
    
    private final TestDataDao testDataDao;
    private final Random random;
    
    public DataPreparationService() {
        this.testDataDao = new TestDataDao();
        this.random = new Random();
    }
    
    /**
     * 初始化数据库表和测试数据
     * @param portCodeCount 不同c_port_code的数量
     * @param recordsPerPortCode 每个c_port_code对应的记录数
     */
    public void initializeData(int portCodeCount, int recordsPerPortCode,String planCode) {
        logger.info("Starting data initialization with {} port codes and {} records per port code", 
                   portCodeCount, recordsPerPortCode);
        
        try (Connection connection = DatabaseUtil.getConnection()) {
            // 验证表是否存在
            validateTables(connection);

            // 清空现有数据
            // clearExistingData(connection);
            
            // 生成测试数据
            // List<TestData> testDataList = generateTestData(portCodeCount, recordsPerPortCode);
            
            // 插入到测试数据表
            // testDataDao.batchInsertTestData(connection, testDataList);
            
            // 插入到执行数据表（相同数据）
            // testDataDao.batchInsertExecutionData(connection, testDataList);

            // 测试表、执行表数据都事先准备好
            
            // 验证数据
            verifyDataInitialization(connection,planCode);
            
            logger.info("Data initialization completed successfully");
            
        } catch (SQLException e) {
            logger.error("Failed to initialize data", e);
            throw new RuntimeException("Data initialization failed", e);
        }
    }

    /**
     * 验证表是否存在
     */
    private void validateTables(Connection connection) throws SQLException {
        logger.info("Validating tables exist...");
        boolean tablesExist = testDataDao.validateTablesExist(connection);
        if (!tablesExist) {
            throw new SQLException("Required tables do not exist. Please create them manually before running the stress test.");
        }
        logger.info("All required tables validated successfully");
    }

    /**
     * 清空现有数据
     */
    private void clearExistingData(Connection connection) throws SQLException {
        logger.info("Clearing existing data...");
        testDataDao.truncateTable(connection, TestDataDao.getTestDataTable());
        testDataDao.truncateTable(connection, TestDataDao.getExecutionTable());
        testDataDao.truncateTable(connection, TestDataDao.getTempTable());
        logger.info("Existing data cleared");
    }
    
    /**
     * 生成测试数据
     */
    private List<TestData> generateTestData(int portCodeCount, int recordsPerPortCode) {
        logger.info("Generating test data...");
        List<TestData> dataList = new ArrayList<>();
        long idCounter = 1;
        
        for (int i = 1; i <= portCodeCount; i++) {
            String portCode = String.format("PORT_%03d", i);
            
            for (int j = 1; j <= recordsPerPortCode; j++) {
                TestData data = new TestData();
                data.setId(idCounter++);
                data.setCPortCode(portCode);
                data.setDataValue(generateRandomDataValue());
                data.setDescription(generateRandomDescription(portCode, j));
                data.setStatus(random.nextInt(3) + 1); // 状态1-3
                
                Timestamp now = new Timestamp(System.currentTimeMillis());
                data.setCreateTime(now);
                data.setUpdateTime(now);
                
                dataList.add(data);
            }
        }
        
        logger.info("Generated {} test data records", dataList.size());
        return dataList;
    }
    
    /**
     * 生成随机数据值
     */
    private String generateRandomDataValue() {
        String[] prefixes = {"DATA", "VALUE", "TEST", "SAMPLE", "RECORD"};
        String prefix = prefixes[random.nextInt(prefixes.length)];
        int number = random.nextInt(10000);
        return String.format("%s_%04d", prefix, number);
    }
    
    /**
     * 生成随机描述
     */
    private String generateRandomDescription(String portCode, int sequence) {
        String[] templates = {
            "Test record for %s sequence %d with random data",
            "Sample data entry for port %s number %d",
            "Generated test data for %s item %d",
            "Stress test record %s-%d for performance testing",
            "Mock data entry for port code %s sequence %d"
        };
        
        String template = templates[random.nextInt(templates.length)];
        return String.format(template, portCode, sequence);
    }
    
    /**
     * 验证数据初始化结果
     */
    private void verifyDataInitialization(Connection connection,String planCode) throws SQLException {
        logger.info("Verifying data initialization...");
        
        long testDataCount = testDataDao.getTableCount(connection, TestDataDao.getTestDataTable());
        long executionDataCount = testDataDao.getTableCount(connection, TestDataDao.getExecutionTable());
        
        logger.info("Test data table count: {}", testDataCount);
        logger.info("Execution data table count: {}", executionDataCount);
        
        if (testDataCount != executionDataCount) {
            throw new RuntimeException("Data count mismatch between test and execution tables");
        }
        
        if (testDataCount == 0) {
            throw new RuntimeException("No data was inserted");
        }
        
        // 验证port codes
        List<String> portCodes = testDataDao.getAllPortCodes(connection,planCode);
        logger.info("Found {} distinct port codes: {}", portCodes.size(), 
                   portCodes.size() <= 10 ? portCodes : portCodes.subList(0, 10) + "...");
        
        logger.info("Data initialization verification completed successfully");
    }
    
    /**
     * 根据方案planCode获取所有port codes用于并发测试
     */
    public List<String> getAllPortCodes(String planCode) {
        try (Connection connection = DatabaseUtil.getConnection()) {
            return testDataDao.getAllPortCodes(connection,planCode);
        } catch (SQLException e) {
            logger.error("Failed to get port codes", e);
            throw new RuntimeException("Failed to get port codes", e);
        }
    }
    
    /**
     * 检查数据是否已初始化
     */
    public boolean isDataInitialized() {
        try (Connection connection = DatabaseUtil.getConnection()) {
            long testDataCount = testDataDao.getTableCount(connection, TestDataDao.getTestDataTable());
            long executionDataCount = testDataDao.getTableCount(connection, TestDataDao.getExecutionTable());
            
            return testDataCount > 0 && executionDataCount > 0;
        } catch (SQLException e) {
            logger.error("Failed to check data initialization status", e);
            return false;
        }
    }
    
    /**
     * 获取数据统计信息
     */
    public void printDataStatistics(String planCode) {
        try (Connection connection = DatabaseUtil.getConnection()) {
            long testDataCount = testDataDao.getTableCount(connection, TestDataDao.getTestDataTable());
            long executionDataCount = testDataDao.getTableCount(connection, TestDataDao.getExecutionTable());
            List<String> portCodes = testDataDao.getAllPortCodes(connection,planCode);
            
            logger.info("=== Data Statistics ===");
            logger.info("Test data table records: {}", testDataCount);
            logger.info("Execution data table records: {}", executionDataCount);
            logger.info("Distinct port codes: {}", portCodes.size());
            
            if (!portCodes.isEmpty()) {
                logger.info("Port codes sample: {}", 
                           portCodes.size() <= 5 ? portCodes : portCodes.subList(0, 5) + "...");
            }
            
        } catch (SQLException e) {
            logger.error("Failed to get data statistics", e);
        }
    }

    /**
     * 初始化测试数据（使用动态表结构）
     */
    public void initializeDataDynamic(int portCodeCount, int recordsPerPortCode) {
        logger.info("Starting dynamic data initialization with {} port codes and {} records per port code",
                   portCodeCount, recordsPerPortCode);

        try (Connection connection = DatabaseUtil.getConnection()) {
            // 验证表是否存在
            validateTables(connection);

            // 获取表结构
            TableStructure testDataStructure = TableStructureManager.getTestDataTableStructure(connection);
            TableStructure executionStructure = TableStructureManager.getExecutionTableStructure(connection);

            // 验证表结构是否适合压力测试
            if (!DynamicDataGenerator.validateTableForStressTesting(testDataStructure)) {
                throw new SQLException("Test data table structure is not suitable for stress testing");
            }
            if (!DynamicDataGenerator.validateTableForStressTesting(executionStructure)) {
                throw new SQLException("Execution table structure is not suitable for stress testing");
            }

            // 打印数据生成策略
            DynamicDataGenerator.printDataGenerationStrategy(testDataStructure);
            DynamicDataGenerator.printDataGenerationStrategy(executionStructure);

            // 清空现有数据
            clearExistingDataDynamic(connection);

            // 生成端口代码
            String[] portCodes = DynamicDataGenerator.generatePortCodes(portCodeCount);

            // 生成并插入测试数据
            logger.info("Generating test data...");
            DynamicDataGenerator.generateAndInsertTestData(connection, testDataStructure, portCodes, recordsPerPortCode);

            // 生成并插入执行数据
            logger.info("Generating execution data...");
            DynamicDataGenerator.generateAndInsertTestData(connection, executionStructure, portCodes, recordsPerPortCode);

            // 验证数据
            verifyDataInitializationDynamic(connection);

            logger.info("Dynamic data initialization completed successfully");

        } catch (SQLException e) {
            logger.error("Failed to initialize data dynamically", e);
            throw new RuntimeException("Dynamic data initialization failed", e);
        }
    }

    /**
     * 清空现有数据（动态版本）
     */
    private void clearExistingDataDynamic(Connection connection) throws SQLException {
        logger.info("Clearing existing data from all tables...");

        String testDataTable = TestDataDao.getTestDataTable();
        String executionTable = TestDataDao.getExecutionTable();
        String tempTable = TestDataDao.getTempTable();

        DynamicDataGenerator.clearTableData(connection, testDataTable);
        DynamicDataGenerator.clearTableData(connection, executionTable);
        DynamicDataGenerator.clearTableData(connection, tempTable);

        logger.info("All existing data cleared successfully");
    }

    /**
     * 验证数据初始化（动态版本）
     */
    private void verifyDataInitializationDynamic(Connection connection) throws SQLException {
        logger.info("Verifying data initialization...");

        String testDataTable = TestDataDao.getTestDataTable();
        String executionTable = TestDataDao.getExecutionTable();

        long testDataCount = DynamicDataGenerator.getTableRowCount(connection, testDataTable);
        long executionDataCount = DynamicDataGenerator.getTableRowCount(connection, executionTable);

        logger.info("Test data table '{}' contains {} records", testDataTable, testDataCount);
        logger.info("Execution table '{}' contains {} records", executionTable, executionDataCount);

        if (testDataCount == 0 || executionDataCount == 0) {
            throw new SQLException("Data initialization verification failed: some tables are empty");
        }

        if (testDataCount != executionDataCount) {
            logger.warn("Test data count ({}) does not match execution data count ({})",
                       testDataCount, executionDataCount);
        }

        logger.info("Data initialization verification completed successfully");
    }
}
