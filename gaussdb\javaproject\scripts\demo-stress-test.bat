@echo off
REM GaussDB 压力测试演示脚本
REM 展示不同场景的压力测试用法

echo ========================================
echo GaussDB 压力测试演示
echo ========================================
echo.

echo 1. 基本压力测试（默认参数）
echo    - 10个并发线程
echo    - 50个port_code
echo    - 每个port_code 100条记录
echo.
pause
call run-stress-test.bat
echo.

echo ========================================
echo 2. 小规模快速测试
echo    - 5个并发线程
echo    - 10个port_code
echo    - 每个port_code 20条记录
echo    - 批次大小 10
echo.
pause
call run-stress-test.bat -c 5 -p 10 -r 20 -b 10
echo.

echo ========================================
echo 3. 中等规模测试（流式分批次处理演示）
echo    - 20个并发线程
echo    - 30个port_code
echo    - 每个port_code 50条记录
echo    - 批次大小 15（演示流式处理：每批次立即转移）
echo    - 显示详细结果
echo.
pause
call run-stress-test.bat -c 20 -p 30 -r 50 -b 15 --detailed
echo.

echo ========================================
echo 4. 多轮持续测试
echo    - 使用现有数据（不重新初始化）
echo    - 执行3轮测试
echo    - 每轮间隔2秒
echo.
pause
call run-stress-test.bat --no-init --rounds 3 --interval 2000 --detailed
echo.

echo ========================================
echo 演示完成！
echo ========================================
pause
