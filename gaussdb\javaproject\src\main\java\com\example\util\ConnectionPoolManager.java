package com.example.util;

import com.example.config.ConnectionPoolConfig;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 数据库连接池管理器
 * 支持多数据源连接池管理，包括GaussDB和StarRocks
 */
public class ConnectionPoolManager {
    private static final Logger logger = LoggerFactory.getLogger(ConnectionPoolManager.class);
    
    // 连接池缓存，key为数据库配置的唯一标识
    private static final ConcurrentMap<String, HikariDataSource> dataSources = new ConcurrentHashMap<>();
    
    // 默认连接池配置
    private static final int DEFAULT_MAXIMUM_POOL_SIZE = 20;
    private static final int DEFAULT_MINIMUM_IDLE = 5;
    private static final long DEFAULT_CONNECTION_TIMEOUT = 30000; // 30秒
    private static final long DEFAULT_IDLE_TIMEOUT = 600000; // 10分钟
    private static final long DEFAULT_MAX_LIFETIME = 1800000; // 30分钟
    
    /**
     * 数据库类型枚举
     */
    public enum DatabaseType {
        GAUSSDB("com.huawei.gaussdb.jdbc.Driver"),
        STARROCKS("com.mysql.cj.jdbc.Driver"),
        MYSQL("com.mysql.cj.jdbc.Driver"),
        POSTGRESQL("org.postgresql.Driver"),
        ORACLE("oracle.jdbc.driver.OracleDriver");
        
        private final String driverClassName;
        
        DatabaseType(String driverClassName) {
            this.driverClassName = driverClassName;
        }
        
        public String getDriverClassName() {
            return driverClassName;
        }
    }
    
    /**
     * 连接池配置类
     */
    public static class PoolConfig {
        private int maximumPoolSize = DEFAULT_MAXIMUM_POOL_SIZE;
        private int minimumIdle = DEFAULT_MINIMUM_IDLE;
        private long connectionTimeout = DEFAULT_CONNECTION_TIMEOUT;
        private long idleTimeout = DEFAULT_IDLE_TIMEOUT;
        private long maxLifetime = DEFAULT_MAX_LIFETIME;
        private boolean autoCommit = false;
        
        // Getters and Setters
        public int getMaximumPoolSize() { return maximumPoolSize; }
        public void setMaximumPoolSize(int maximumPoolSize) { this.maximumPoolSize = maximumPoolSize; }
        
        public int getMinimumIdle() { return minimumIdle; }
        public void setMinimumIdle(int minimumIdle) { this.minimumIdle = minimumIdle; }
        
        public long getConnectionTimeout() { return connectionTimeout; }
        public void setConnectionTimeout(long connectionTimeout) { this.connectionTimeout = connectionTimeout; }
        
        public long getIdleTimeout() { return idleTimeout; }
        public void setIdleTimeout(long idleTimeout) { this.idleTimeout = idleTimeout; }
        
        public long getMaxLifetime() { return maxLifetime; }
        public void setMaxLifetime(long maxLifetime) { this.maxLifetime = maxLifetime; }
        
        public boolean isAutoCommit() { return autoCommit; }
        public void setAutoCommit(boolean autoCommit) { this.autoCommit = autoCommit; }
    }
    
    /**
     * 获取数据库连接（使用配置文件中的参数）
     * @param url 数据库URL
     * @param username 用户名
     * @param password 密码
     * @param databaseType 数据库类型
     * @return 数据库连接
     */
    public static Connection getConnection(String url, String username, String password, DatabaseType databaseType) throws SQLException {
        PoolConfig poolConfig = ConnectionPoolConfig.getPoolConfig(databaseType);
        return getConnection(url, username, password, databaseType, poolConfig);
    }
    
    /**
     * 获取数据库连接（带自定义连接池配置）
     * @param url 数据库URL
     * @param username 用户名
     * @param password 密码
     * @param databaseType 数据库类型
     * @param poolConfig 连接池配置
     * @return 数据库连接
     */
    public static Connection getConnection(String url, String username, String password, 
                                         DatabaseType databaseType, PoolConfig poolConfig) throws SQLException {
        String key = generateKey(url, username, databaseType);
        
        HikariDataSource dataSource = dataSources.computeIfAbsent(key, k -> {
            logger.info("Creating new connection pool for database: {} ({})", url, databaseType);
            return createDataSource(url, username, password, databaseType, poolConfig);
        });
        
        try {
            Connection connection = dataSource.getConnection();
            logger.debug("Retrieved connection from pool for: {}", url);
            return connection;
        } catch (SQLException e) {
            logger.error("Failed to get connection from pool for: {}", url, e);
            throw e;
        }
    }
    
    /**
     * 创建数据源
     */
    private static HikariDataSource createDataSource(String url, String username, String password, 
                                                    DatabaseType databaseType, PoolConfig poolConfig) {
        HikariConfig config = new HikariConfig();
        
        // 基本配置
        config.setJdbcUrl(url);
        config.setUsername(username);
        config.setPassword(password);
        config.setDriverClassName(databaseType.getDriverClassName());
        
        // 连接池配置
        config.setMaximumPoolSize(poolConfig.getMaximumPoolSize());
        config.setMinimumIdle(poolConfig.getMinimumIdle());
        config.setConnectionTimeout(poolConfig.getConnectionTimeout());
        config.setIdleTimeout(poolConfig.getIdleTimeout());
        config.setMaxLifetime(poolConfig.getMaxLifetime());
        config.setAutoCommit(poolConfig.isAutoCommit());
        
        // 连接池名称
        config.setPoolName("DataMigration-" + databaseType.name() + "-Pool");
        
        // 连接测试查询
        switch (databaseType) {
            case GAUSSDB:
                config.setConnectionTestQuery("SELECT 1");
                break;
            case STARROCKS:
            case MYSQL:
                config.setConnectionTestQuery("SELECT 1");
                break;
            case POSTGRESQL:
                config.setConnectionTestQuery("SELECT 1");
                break;
            case ORACLE:
                config.setConnectionTestQuery("SELECT 1 FROM DUAL");
                break;
        }
        
        // 泄漏检测配置
        config.setLeakDetectionThreshold(ConnectionPoolConfig.getLeakDetectionThreshold(databaseType));

        // 数据源特定配置
        Properties dsProps = ConnectionPoolConfig.getDataSourceProperties();
        for (String key : dsProps.stringPropertyNames()) {
            config.addDataSourceProperty(key, dsProps.getProperty(key));
        }

        return new HikariDataSource(config);
    }
    
    /**
     * 生成数据源缓存key
     */
    private static String generateKey(String url, String username, DatabaseType databaseType) {
        return String.format("%s_%s_%s", databaseType.name(), url, username);
    }
    
    /**
     * 根据URL自动检测数据库类型
     */
    public static DatabaseType detectDatabaseType(String url) {
        if (url.startsWith("jdbc:gaussdb:")) {
            return DatabaseType.GAUSSDB;
        } else if (url.contains("starrocks") || url.contains(":9030")) {
            return DatabaseType.STARROCKS;
        } else if (url.startsWith("jdbc:mysql:")) {
            return DatabaseType.MYSQL;
        } else if (url.startsWith("jdbc:postgresql:")) {
            return DatabaseType.POSTGRESQL;
        } else if (url.startsWith("jdbc:oracle:")) {
            return DatabaseType.ORACLE;
        } else {
            logger.warn("Unknown database type for URL: {}, defaulting to MYSQL", url);
            return DatabaseType.MYSQL;
        }
    }
    
    /**
     * 关闭指定的数据源
     */
    public static void closeDataSource(String url, String username, DatabaseType databaseType) {
        String key = generateKey(url, username, databaseType);
        HikariDataSource dataSource = dataSources.remove(key);
        if (dataSource != null && !dataSource.isClosed()) {
            logger.info("Closing connection pool for: {}", url);
            dataSource.close();
        }
    }
    
    /**
     * 关闭所有数据源
     */
    public static void closeAllDataSources() {
        logger.info("Closing all connection pools...");
        dataSources.values().forEach(dataSource -> {
            if (!dataSource.isClosed()) {
                dataSource.close();
            }
        });
        dataSources.clear();
        logger.info("All connection pools closed");
    }
    
    /**
     * 获取连接池状态信息
     */
    public static void printPoolStatus() {
        logger.info("=== Connection Pool Status ===");
        dataSources.forEach((key, dataSource) -> {
            logger.info("Pool: {} - Active: {}, Idle: {}, Total: {}, Waiting: {}", 
                       key,
                       dataSource.getHikariPoolMXBean().getActiveConnections(),
                       dataSource.getHikariPoolMXBean().getIdleConnections(),
                       dataSource.getHikariPoolMXBean().getTotalConnections(),
                       dataSource.getHikariPoolMXBean().getThreadsAwaitingConnection());
        });
    }
}
