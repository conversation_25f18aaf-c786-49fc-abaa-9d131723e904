#!/bin/bash
# ============================================================================
# 表配置验证脚本 - Linux/Unix版本
# Table Configuration Validation Script - Linux/Unix Version
# ============================================================================

echo
echo "============================================================================"
echo "表配置验证工具 Table Configuration Validator"
echo "============================================================================"
echo

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java环境，请确保已安装Java 8或更高版本"
    echo "Error: Java not found, please ensure Java 8+ is installed"
    exit 1
fi

# 检查JAR文件是否存在
if [ ! -f "target/stress-test-1.0-SNAPSHOT.jar" ]; then
    echo "警告: 未找到编译后的JAR文件，正在编译..."
    echo "Warning: Compiled JAR not found, compiling..."
    mvn clean package -q
    if [ $? -ne 0 ]; then
        echo "错误: 编译失败"
        echo "Error: Compilation failed"
        exit 1
    fi
fi

echo "正在验证表配置..."
echo "Validating table configuration..."
echo

# 运行验证工具
java -cp "target/stress-test-1.0-SNAPSHOT.jar" com.example.stress.util.TableConfigValidator

if [ $? -eq 0 ]; then
    echo
    echo "============================================================================"
    echo "✅ 配置验证成功！可以运行压力测试程序"
    echo "✅ Configuration validation successful! You can run the stress test"
    echo "============================================================================"
else
    echo
    echo "============================================================================"
    echo "❌ 配置验证失败！请检查配置和数据库连接"
    echo "❌ Configuration validation failed! Please check config and database"
    echo "============================================================================"
    echo
    echo "请检查以下项目 Please check the following:"
    echo "1. 数据库连接配置 Database connection configuration"
    echo "2. 表名配置 Table name configuration"
    echo "3. 数据库中是否存在表 Tables exist in database"
    echo "4. 数据库用户权限 Database user permissions"
    exit 1
fi

echo
