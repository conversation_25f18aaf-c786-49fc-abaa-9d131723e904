# GaussDB 并发压力测试程序

## 项目概述

这是一个专门为GaussDB数据库设计的并发压力测试模拟程序，能够模拟真实业务场景中的高并发数据库操作。程序实现了对数据库表的并发INSERT、UPDATE和DELETE操作，按照`c_port_code`字段进行分组，每个分组作为独立的工作单元并发执行。

## 核心特性

### 🎯 动态表结构支持
- 自动从数据库元数据视图获取表结构信息
- 支持任意表结构，无需硬编码字段定义
- 智能数据生成，根据列名和数据类型自动生成测试数据
- 支持多种数据库类型（GaussDB、Oracle、MySQL、PostgreSQL）

### 🚀 高性能并发
- 支持可配置的并发线程数
- 基于线程池的任务调度
- 每个线程处理独立的数据分组，避免锁竞争

### 📊 完整的操作流程
- **DELETE**: 从执行表中删除指定分组的数据
- **分批次INSERT**: 从测试数据表分批次查询数据并插入临时表，再从临时表批量插入执行表
- **UPDATE**: 更新执行表中数据的状态字段

### 📈 详细的性能监控
- 操作耗时统计（平均、最小、最大）
- 成功率和失败率统计
- 吞吐量计算
- 并发效率分析

### 🔧 灵活的配置选项
- 命令行参数配置
- 支持多轮测试
- 可选的数据初始化
- 详细结果输出控制

## 项目结构

```
src/main/java/com/example/stress/
├── StressTestApplication.java          # 主程序入口
├── config/
│   └── StressTestConfig.java          # 配置管理
├── dao/
│   └── TestDataDao.java               # 数据访问层
├── model/
│   └── TestData.java                  # 数据模型
├── monitor/
│   ├── PerformanceMonitor.java        # 性能监控
│   └── OperationStats.java            # 统计信息
└── service/
    ├── ConcurrentStressTestService.java  # 并发测试服务
    ├── DataPreparationService.java       # 数据准备服务
    ├── StressTestResult.java             # 测试结果
    └── StressTestWorker.java             # 工作线程
```

## 快速开始

### 1. 环境要求
- Java 8+
- Maven 3.6+
- GaussDB数据库
- 配置好的数据库连接
- **手动创建数据库表**（程序不再自动创建表）

### 2. 表结构要求

程序使用动态表结构，支持任意表设计，但需要满足以下要求：

#### 必需列
- **`c_port_code`**: 分组字段，用于并发分组（VARCHAR类型）
- **主键列**: `id` 或以 `_id` 结尾的列，用于数据标识

#### 支持的数据类型
- 整数类型：INT, BIGINT, SMALLINT, TINYINT
- 小数类型：DECIMAL, NUMERIC, FLOAT, DOUBLE
- 字符串类型：VARCHAR, CHAR, TEXT, STRING
- 日期时间类型：DATE, TIME, TIMESTAMP, DATETIME

#### 表结构一致性
三张表（测试数据表、临时表、执行表）必须具有完全相同的结构。

### 3. 创建数据库表
```bash
# 使用提供的SQL脚本创建表
# 在GaussDB中执行 create_tables.sql 脚本
# 或者根据需要自定义表名并在配置文件中配置
```

### 4. 配置表名
在 `src/main/resources/connection-pool.properties` 中配置表名：
```properties
# 测试数据表名
stress.test.table.testData=your_test_data_table
# 临时表名
stress.test.table.temp=your_temp_table
# 执行操作表名
stress.test.table.execution=your_execution_table
```

**注意**: 程序会自动验证表结构一致性和必需列的存在。

### 5. 编译项目
```bash
mvn clean package
```

### 6. 运行测试
```bash
# Windows
run-stress-test.bat

# Linux/Unix
chmod +x run-stress-test.sh
./run-stress-test.sh

# 自定义参数示例
./run-stress-test.sh -c 20 -p 100 -r 200 -b 50
```

### 7. 查看帮助
```bash
java -jar target/gaussdb-demo-1.0.0-fat.jar --help
```

## 使用示例

### 基础测试
```bash
# 默认配置：10并发，50个分组，每组100条记录
./run-stress-test.sh
```

### 高并发测试
```bash
# 50并发线程，100个分组，每组200条记录，批次大小30
./run-stress-test.sh -c 50 -p 100 -r 200 -b 30
```

### 持续压力测试
```bash
# 执行10轮测试，每轮间隔5秒
./run-stress-test.sh --rounds 10 --interval 5000
```

### 使用现有数据测试
```bash
# 跳过数据初始化，使用现有数据
./run-stress-test.sh --no-init --detailed
```

## 配置参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `-c, --concurrency` | 并发线程数 | 10 |
| `-p, --port-codes` | 分组数量 | 50 |
| `-r, --records-per-port` | 每组记录数 | 100 |
| `-b, --batch-size` | 批次大小 | 50 |
| `--rounds` | 测试轮数 | 1 |
| `--interval` | 轮次间隔(ms) | 1000 |
| `--no-init` | 跳过数据初始化 | false |
| `--detailed` | 显示详细结果 | false |

## 数据库表结构

程序自动创建两张表：

```sql
-- 测试数据表（只读）
CREATE TABLE stress_test_data (
    id BIGINT PRIMARY KEY,
    c_port_code VARCHAR(50) NOT NULL,
    data_value VARCHAR(200),
    description VARCHAR(500),
    status INTEGER DEFAULT 1,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 临时表（分批次处理中间存储）
CREATE TABLE stress_temp_data (
    -- 结构与上表相同
);

-- 执行操作表（读写）
CREATE TABLE stress_execution_data (
    -- 结构与上表相同
);
```

## 性能监控输出

```
=== Stress Test Summary ===
Total tasks: 50
Successful tasks: 50
Failed tasks: 0
Success rate: 100.00%
Total execution time: 12345 ms
Concurrency level: 10

=== Operation Statistics ===
DELETE: 50 ops (50S/0F), Avg: 45.23ms, Success: 100.00%
INSERT: 50 ops (50S/0F), Avg: 123.45ms, Success: 100.00%
UPDATE: 50 ops (50S/0F), Avg: 67.89ms, Success: 100.00%
Overall throughput: 952.34 operations/second
```

## 演示脚本

运行 `demo-stress-test.bat` 可以体验不同场景的压力测试：

1. **基本测试**: 默认参数的标准测试
2. **快速测试**: 小数据量的快速验证
3. **中等规模**: 适中的并发和数据量
4. **持续测试**: 多轮循环测试

## 技术实现

### 并发模型
- 使用 `ExecutorService` 管理线程池
- 每个工作线程处理一个独立的数据分组
- 避免了数据竞争和锁争用

### 事务管理
- 每个工作单元使用独立的数据库连接
- DELETE、INSERT、UPDATE操作在同一事务中执行
- 异常时自动回滚，确保数据一致性

### 性能监控
- 使用 `LongAdder` 实现高并发计数
- 纳秒级时间测量，确保精度
- 支持统计信息合并和聚合

### 连接池优化
- 基于HikariCP的高性能连接池
- 针对GaussDB优化的连接参数
- 支持连接泄漏检测

## 注意事项

1. **测试环境**: 请在测试环境中运行，避免影响生产数据
2. **资源消耗**: 高并发测试会消耗大量数据库连接和系统资源
3. **网络延迟**: 测试结果会受到网络延迟影响
4. **数据库性能**: 确保数据库有足够的性能处理并发请求
5. **权限要求**: 数据库用户需要有创建表和执行DML的权限

## 故障排除

### 常见问题
- **连接失败**: 检查 `connection-pool.properties` 中的数据库配置
- **内存不足**: 调整启动脚本中的 `-Xmx` 参数
- **编译失败**: 确保Java和Maven版本符合要求
- **权限错误**: 确认数据库用户权限

### 日志配置
程序使用Logback记录详细日志，可通过 `logback.xml` 调整日志级别。

## 扩展开发

程序采用模块化设计，支持以下扩展：

- 添加新的操作类型
- 自定义数据生成策略
- 实现不同的并发模式
- 集成更多性能指标
- 支持结果导出功能

## 相关文档

- [动态表结构详细指南](DYNAMIC_TABLE_STRUCTURE_GUIDE.md) - 深入了解动态表结构功能
- [分批次处理指南](BATCH_PROCESSING_GUIDE.md) - 分批次处理的详细说明
- [流式批处理流程](STREAMING_BATCH_FLOW.md) - 流式批处理的技术细节
- [压力测试指南](STRESS_TEST_GUIDE.md) - 完整的使用指南

## 许可证

本项目基于现有的GaussDB项目开发，遵循相同的许可证条款。
