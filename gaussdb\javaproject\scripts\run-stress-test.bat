@echo off
REM GaussDB 压力测试启动脚本 (Windows)
REM 使用方法: run-stress-test.bat [参数]

echo Starting GaussDB Stress Test...

REM 设置Java内存参数
set JAVA_OPTS=-Xms512m -Xmx2g -XX:+UseG1GC

REM 检查是否存在编译后的jar文件
if exist "target\gaussdb-demo-1.0.0-fat.jar" (
    echo Using compiled jar file...
    java %JAVA_OPTS% -jar target\gaussdb-demo-1.0.0-fat.jar %*
) else (
    echo Jar file not found. Please compile first with: mvn clean package
    echo.
    echo Compiling now...
    call mvn clean package -DskipTests
    
    if exist "target\gaussdb-demo-1.0.0-fat.jar" (
        echo Compilation successful. Starting stress test...
        java %JAVA_OPTS% -jar target\gaussdb-demo-1.0.0-fat.jar %*
    ) else (
        echo Compilation failed. Please check the build output.
        exit /b 1
    )
)

echo.
echo Stress test completed.
pause
