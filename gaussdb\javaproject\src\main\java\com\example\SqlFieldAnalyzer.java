package com.example;

import java.sql.*;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.io.*;
import com.example.util.DatabaseUtil;

public class SqlFieldAnalyzer {
    
    private static final String DB_URL = "**********************************************************";
    private static final String DB_USER = "root";
    private static final String DB_PASSWORD = "Tcdn@2007";
    
    // WHERE条件字段提取的正则表达式
    private static final Pattern WHERE_FIELD_PATTERN = Pattern.compile(
        "\\b([a-zA-Z_][a-zA-Z0-9_]*|\\$\\d+)\\b\\s*(?==|!=|<>|>|<|>=|<=|LIKE\\b|IN\\s*\\(|IS\\b|BETWEEN\\b)",
        Pattern.CASE_INSENSITIVE
    );
    
    // 定义一个通用的正则表达式，用于匹配 EXISTS 和 NOT EXISTS 子查询
    private static final Pattern EXISTS_PATTERN = Pattern.compile(
        "(?:NOT\\s+)?EXISTS\\s*\\([^)]*(?:\\([^)]*\\)[^)]*)*\\)",
        Pattern.CASE_INSENSITIVE
    );

    static class SqlInfo {
        String uniqueSqlId;
        String sql;
        int callCount;
        double totalElapseTime;
        
        SqlInfo(String uniqueSqlId, String sql, int callCount, double totalElapseTime) {
            this.uniqueSqlId = uniqueSqlId;
            this.sql = sql;
            this.callCount = callCount;
            this.totalElapseTime = totalElapseTime;
        }
    }
    
    public static void main(String[] args) {
        if (args.length < 2) {
            System.err.println("Usage: java SqlFieldAnalyzer <tableName> <uniqueSqlIds>");
            System.err.println("Example: java SqlFieldAnalyzer summary_statement_20250728 \"1879167559,2623855430,3068820426\"");
            System.exit(1);
        }
        
        SqlFieldAnalyzer analyzer = new SqlFieldAnalyzer();
        
        String tableName = args[0];
        String uniqueSqlIdsStr = args[1];
        String[] uniqueSqlIds = uniqueSqlIdsStr.split(",");
        
        // 去除空格
        for (int i = 0; i < uniqueSqlIds.length; i++) {
            uniqueSqlIds[i] = uniqueSqlIds[i].trim();
        }
        
        analyzer.analyzeFromDatabase(tableName, uniqueSqlIds);
    }
    
    public void analyzeFromDatabase(String tableName, String[] uniqueSqlIds) {
        List<SqlInfo> sqlList = queryFromDatabase(tableName, uniqueSqlIds);
        
        if (sqlList.isEmpty()) {
            System.out.println("未查询到任何SQL数据");
            return;
        }
        
        analyzeAndGroup(sqlList, tableName);
    }
    
    private List<SqlInfo> queryFromDatabase(String tableName, String[] uniqueSqlIds) {
        List<SqlInfo> sqlList = new ArrayList<>();
        
        // 构建IN条件的占位符
        String inClause = String.join(",", Collections.nCopies(uniqueSqlIds.length, "?"));
        String sql = "SELECT unique_sql_id, query, n_calls, total_elapse_time, " +
                    "n_returned_rows, n_tuples_fetched, n_tuples_returned " +
                    "FROM " + tableName + " " +
                    "WHERE unique_sql_id IN (" + inClause + ") " +
                    "ORDER BY total_elapse_time";
        
        try (Connection conn = DatabaseUtil.getConnection(DB_URL, DB_USER, DB_PASSWORD);
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            // 设置参数
            for (int i = 0; i < uniqueSqlIds.length; i++) {
                stmt.setString(i + 1, uniqueSqlIds[i]);
            }
            
            System.out.println("正在查询数据库...");
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    String uniqueSqlId = rs.getString("unique_sql_id");
                    String query = rs.getString("query");
                    int nCalls = rs.getInt("n_calls");
                    double totalElapseTime = rs.getDouble("total_elapse_time");
                    
                    sqlList.add(new SqlInfo(uniqueSqlId, query, nCalls, totalElapseTime));
                }
            }
            
            System.out.printf("成功查询到 %d 条SQL记录%n", sqlList.size());
            
        } catch (SQLException e) {
            System.err.println("数据库查询失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return sqlList;
    }
    
    public void analyzeAndGroup(List<SqlInfo> sqlList, String tableName) {
        Map<String, GroupStats> fieldGroupStats = new HashMap<>();
        
        System.out.println("=== SQL字段分析结果 ===");
        System.out.println();
        
        for (SqlInfo sqlInfo : sqlList) {
            // 提取WHERE条件和JOIN条件中的字段和对应的表名
            Set<String> extractedTables = extractTableNames(sqlInfo.sql);
            Map<String, Set<String>> tableFieldsMap = extractTableFields(sqlInfo.sql);
            
            System.out.printf("SQL ID: %s%n", sqlInfo.uniqueSqlId);
            System.out.printf("SQL: %s%n", sqlInfo.sql);
            System.out.printf("解析出的表: %s%n", extractedTables);
            System.out.printf("WHERE字段: %s%n", extractWhereFields(sqlInfo.sql));
            System.out.printf("JOIN字段: %s%n", extractJoinFields(sqlInfo.sql));
            System.out.printf("表字段映射: %s%n", tableFieldsMap);
            
            // 为每个表的字段组合创建分组
            for (Map.Entry<String, Set<String>> entry : tableFieldsMap.entrySet()) {
                String table = entry.getKey();
                Set<String> fields = entry.getValue();
                
                // 转小写并排序拼接字段
                String fieldKey = fields.stream()
                    .map(String::toLowerCase)
                    .sorted()
                    .reduce((a, b) -> a + "," + b)
                    .orElse("");
                
                // 分组键：表名 + 字段组合
                String groupKey = table + "|" + fieldKey;
                
                System.out.printf("  表: %s, 字段: %s, 分组键: %s%n", table, fieldKey, groupKey);
                
                // 累计相同字段组合的统计信息
                fieldGroupStats.computeIfAbsent(groupKey, k -> new GroupStats(table, fieldKey))
                    .addSql(sqlInfo);
            }
            
            System.out.printf("调用次数: %d%n", sqlInfo.callCount);
            String formattedTime = String.format("%.2f", sqlInfo.totalElapseTime);
            System.out.printf("总耗时: %s ms%n", formattedTime);
            System.out.println();
        }
        
        // 输出到CSV文件
        exportToCsv(fieldGroupStats, tableName);
        
        // 控制台输出总体统计
        int totalGroups = fieldGroupStats.size();
        int totalCalls = fieldGroupStats.values().stream().mapToInt(s -> s.totalCalls).sum();
        double totalTime = fieldGroupStats.values().stream().mapToDouble(s -> s.totalTime).sum();
        String formattedTotalTime = String.format("%.2f", totalTime);
        
        System.out.println("=== 总体统计 ===");
        System.out.printf("总分组数: %d%n", totalGroups);
        System.out.printf("总调用次数: %d%n", totalCalls);
        System.out.printf("总耗时: %s ms%n", formattedTotalTime);
    }
    
    private void exportToCsv(Map<String, GroupStats> fieldGroupStats, String tableName) {
        // 创建输出目录
        File outputDir = new File("output");
        if (!outputDir.exists()) {
            outputDir.mkdirs();
        }
        
        String csvFileName = "output/sql_field_analysis_" + tableName + ".csv";
        
        try (PrintWriter writer = new PrintWriter(new FileWriter(csvFileName))) {
            // 写入CSV头部
            writer.println("Table_Name,Field_Combination,Total_Calls,SQL_Count,Total_Time_ms,Average_Time_ms,Unique_SQL_IDs");
            
            // 按总耗时降序排序并写入数据
            fieldGroupStats.entrySet().stream()
                .sorted(Map.Entry.<String, GroupStats>comparingByValue(
                    (a, b) -> Double.compare(b.totalTime, a.totalTime)))
                .forEach(entry -> {
                    GroupStats stats = entry.getValue();
                    String formattedTotalTime = String.format("%.2f", stats.totalTime);
                    String formattedAvgTime = String.format("%.2f", stats.getAverageTime());
                    
                    writer.printf("%s,%s,%d,%d,%s,%s,%s%n",
                        escapeCsvField(stats.tableName),
                        escapeCsvField(stats.fieldCombination),
                        stats.totalCalls,
                        stats.sqlCount,
                        formattedTotalTime,
                        formattedAvgTime,
                        escapeCsvField(stats.getUniqueSqlIdsString()));
                });
            
            System.out.println("分析结果已导出到: " + csvFileName);
            
        } catch (IOException e) {
            System.err.println("导出CSV文件失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    // CSV字段转义方法
    private String escapeCsvField(String field) {
        if (field == null) {
            return "";
        }
        
        // 如果包含逗号、双引号或换行符，需要用双引号包围并转义内部双引号
        if (field.contains(",") || field.contains("\"") || field.contains("\n") || field.contains("\r")) {
            // 将双引号转义为两个双引号
            String escaped = field.replace("\"", "\"\"");
            return "\"" + escaped + "\"";
        }
        
        return field;
    }
    
    // 分组统计信息类
    static class GroupStats {
        int totalCalls = 0;
        int sqlCount = 0;
        double totalTime = 0.0;
        Set<String> uniqueSqlIds = new HashSet<>();
        String tableName; // 单个表名
        String fieldCombination; // 字段组合
        
        GroupStats(String tableName, String fieldCombination) {
            this.tableName = tableName;
            this.fieldCombination = fieldCombination;
        }
        
        void addSql(SqlInfo sqlInfo) {
            this.totalCalls += sqlInfo.callCount;
            this.sqlCount++;
            this.totalTime += sqlInfo.totalElapseTime;
            this.uniqueSqlIds.add(sqlInfo.uniqueSqlId);
        }
        
        double getAverageTime() {
            return totalCalls > 0 ? totalTime / totalCalls : 0;
        }
        
        String getUniqueSqlIdsString() {
            return uniqueSqlIds.stream().sorted().collect(java.util.stream.Collectors.joining(","));
        }
    }
    
    private Set<String> extractWhereFields(String sql) {
        Set<String> fields = new HashSet<>();
        // 找到WHERE子句
        String upperSql = sql.toUpperCase();
        int whereIndex = upperSql.indexOf(" WHERE ");
        if (whereIndex == -1) {
            return fields; // 没有WHERE条件
        }
        // 提取WHERE子句部分
        String whereClause = sql.substring(whereIndex + 7); // 7 = " WHERE ".length()
        // 去掉可能的ORDER BY, GROUP BY, HAVING等后续子句
        whereClause = removeSubsequentClauses(whereClause);
        // 分别处理主WHERE条件和EXISTS子查询
        String mainWhereClause = extractMainWhereConditions(whereClause);
        // 1. 使用正则表达式提取主WHERE条件中的字段名
        Matcher matcher = WHERE_FIELD_PATTERN.matcher(mainWhereClause);
        while (matcher.find()) {
            String field = matcher.group(1);
            // 过滤掉SQL关键字和函数名
            if (!isReservedWord(field)) {
                fields.add(field);
            }
        }
        // 2. 额外提取BETWEEN ... AND ...结构的两个字段
        Pattern betweenPattern = Pattern.compile(
            // 支持表前缀、$参数、字段名
            "BETWEEN\\s+((?:[a-zA-Z_][a-zA-Z0-9_]*\\.)?[a-zA-Z_][a-zA-Z0-9_]*|\\$\\d+)\\s+AND\\s+((?:[a-zA-Z_][a-zA-Z0-9_]*\\.)?[a-zA-Z_][a-zA-Z0-9_]*|\\$\\d+)",
            Pattern.CASE_INSENSITIVE
        );
        Matcher betweenMatcher = betweenPattern.matcher(mainWhereClause);
        while (betweenMatcher.find()) {
            String field1 = betweenMatcher.group(1);
            String field2 = betweenMatcher.group(2);
            // 去掉表前缀，只保留字段名
            if (field1.contains(".")) field1 = field1.substring(field1.lastIndexOf(".") + 1);
            if (field2.contains(".")) field2 = field2.substring(field2.lastIndexOf(".") + 1);
            if (!isReservedWord(field1)) fields.add(field1);
            if (!isReservedWord(field2)) fields.add(field2);
        }
        return fields;
    }
    
    private String extractMainWhereConditions(String whereClause) {
        StringBuilder result = new StringBuilder();
        
        // 使用正则表达式找到所有EXISTS子查询
        Pattern existsPattern = Pattern.compile(
            "((?:NOT\\s+)?EXISTS\\s*\\([^)]*(?:\\([^)]*\\)[^)]*)*\\))",
            Pattern.CASE_INSENSITIVE
        );
        
        Matcher matcher = existsPattern.matcher(whereClause);
        int lastEnd = 0;
        
        while (matcher.find()) {
            // 添加EXISTS之前的部分
            result.append(whereClause.substring(lastEnd, matcher.start()));
            
            // 处理EXISTS子查询，提取与主表相关的条件
            String existsClause = matcher.group(1);
            String mainTableConditions = extractMainTableConditionsFromExists(existsClause);
            result.append(mainTableConditions);
            
            lastEnd = matcher.end();
        }
        
        // 添加最后剩余的部分
        result.append(whereClause.substring(lastEnd));
        
        return result.toString();
    }

    private String extractMainTableConditionsFromExists(String existsClause) {
        // 提取EXISTS子查询中引用主表的条件
        // 例如：EXISTS (SELECT 1 FROM B WHERE B.id = A.id AND B.status = 'active')
        // 应该提取出对A表字段的引用
        
        StringBuilder mainConditions = new StringBuilder();
        
        // 简单的启发式方法：查找形如 "A.field" 或 "$n" 的模式
        Pattern mainTableRefPattern = Pattern.compile(
            "\\b([A-Z]\\.[a-zA-Z_][a-zA-Z0-9_]*|\\$\\d+)\\s*(?:=|!=|<>|>|<|>=|<=)",
            Pattern.CASE_INSENSITIVE
        );
        
        Matcher matcher = mainTableRefPattern.matcher(existsClause);
        while (matcher.find()) {
            String ref = matcher.group(1);
            if (ref.startsWith("$")) {
                // 参数化查询，保留
                mainConditions.append(" ").append(ref).append(" = ? ");
            } else if (ref.contains(".")) {
                // 表别名引用，提取字段名
                String field = ref.substring(ref.indexOf(".") + 1);
                mainConditions.append(" ").append(field).append(" = ? ");
            }
        }
        
        return mainConditions.toString();
    }
    
    private String removeSubsequentClauses(String whereClause) {
        String upperClause = whereClause.toUpperCase();
        String[] subsequentKeywords = {" ORDER BY", " GROUP BY", " HAVING", " LIMIT", " OFFSET"};
        
        int minIndex = whereClause.length();
        for (String keyword : subsequentKeywords) {
            int index = upperClause.indexOf(keyword);
            if (index != -1 && index < minIndex) {
                minIndex = index;
            }
        }
        
        return whereClause.substring(0, minIndex);
    }
    
    private boolean isReservedWord(String word) {
        Set<String> reservedWords = new HashSet<>(Arrays.asList(
            "AND", "OR", "NOT", "NULL", "TRUE", "FALSE", "CASE", "WHEN", "THEN", "ELSE", "END",
            "COUNT", "SUM", "AVG", "MAX", "MIN", "DISTINCT", "EXISTS", "SELECT", "FROM", "WHERE"
        ));
        return reservedWords.contains(word.toUpperCase());
    }
    
    private Map<String, Set<String>> extractTableFields(String sql) {
        Map<String, Set<String>> tableFieldsMap = new HashMap<>();
        
        // 首先提取所有表名
        Set<String> tableNames = extractTableNames(sql);
        
        // 提取WHERE条件中的字段
        Set<String> whereFields = extractWhereFields(sql);
        
        // 提取JOIN条件中的字段
        Set<String> joinFields = extractJoinFields(sql);
        
        // 提取EXISTS子查询中的字段
        Map<String, Set<String>> existsFields = extractExistsFields(sql);
        
        // 合并WHERE和JOIN字段
        Set<String> allFields = new HashSet<>();
        allFields.addAll(whereFields);
        allFields.addAll(joinFields);
        
        // 为每个字段尝试匹配表名
        for (String field : allFields) {
            String matchedTable = findTableForField(field, tableNames, sql);
            tableFieldsMap.computeIfAbsent(matchedTable, k -> new HashSet<>()).add(field);
        }
        
        // 添加EXISTS子查询中的字段
        for (Map.Entry<String, Set<String>> entry : existsFields.entrySet()) {
            tableFieldsMap.computeIfAbsent(entry.getKey(), k -> new HashSet<>()).addAll(entry.getValue());
        }
        
        return tableFieldsMap;
    }
    
    private Set<String> extractTableNames(String sql) {
        Set<String> tables = new HashSet<>();
        
        // 提取FROM子句中的表名
        Pattern fromPattern = Pattern.compile("FROM\\s+([\\w\\.]+)(?:\\s+(?:AS\\s+)?([\\w]+))?", Pattern.CASE_INSENSITIVE);
        Matcher matcher = fromPattern.matcher(sql);
        while (matcher.find()) {
            String tableName = matcher.group(1).toLowerCase();
            // 去掉schema前缀
            if (tableName.contains(".")) {
                tableName = tableName.substring(tableName.lastIndexOf(".") + 1);
            }
            tables.add(tableName);
        }
        
        // 提取JOIN子句中的表名
        Pattern joinPattern = Pattern.compile("JOIN\\s+([\\w\\.]+)(?:\\s+(?:AS\\s+)?([\\w]+))?", Pattern.CASE_INSENSITIVE);
        matcher = joinPattern.matcher(sql);
        while (matcher.find()) {
            String tableName = matcher.group(1).toLowerCase();
            if (tableName.contains(".")) {
                tableName = tableName.substring(tableName.lastIndexOf(".") + 1);
            }
            tables.add(tableName);
        }
        
        return tables;
    }
    
    private String findTableForField(String field, Set<String> tableNames, String sql) {
        // 检查字段是否有表前缀 (如 t.field_name 或 table.field_name)
        Pattern prefixPattern = Pattern.compile("\\b([\\w]+)\\." + Pattern.quote(field) + "\\b", Pattern.CASE_INSENSITIVE);
        Matcher matcher = prefixPattern.matcher(sql);
        if (matcher.find()) {
            String prefix = matcher.group(1).toLowerCase();
            // 如果前缀是表名，直接返回
            if (tableNames.contains(prefix)) {
                return prefix;
            }
            // 如果前缀是别名，需要找到对应的表名
            Pattern aliasPattern = Pattern.compile("\\b(" + String.join("|", tableNames) + ")\\s+(?:AS\\s+)?" + Pattern.quote(prefix) + "\\b", Pattern.CASE_INSENSITIVE);
            Matcher aliasMatcher = aliasPattern.matcher(sql);
            if (aliasMatcher.find()) {
                return aliasMatcher.group(1).toLowerCase();
            }
        }
        
        // 如果没有找到明确的表前缀，返回第一个表名或"unknown"
        return tableNames.isEmpty() ? "unknown" : tableNames.iterator().next();
    }
    
    private Set<String> extractJoinFields(String sql) {
        Set<String> fields = new HashSet<>();
        
        // 匹配JOIN...ON条件中的字段
        Pattern joinOnPattern = Pattern.compile(
            "JOIN\\s+[\\w\\.]+(?:\\s+(?:AS\\s+)?[\\w]+)?\\s+ON\\s+([^\\s]+(?:\\s*(?:=|!=|<>|>|<|>=|<=)\\s*[^\\s]+)?(?:\\s+AND\\s+[^\\s]+(?:\\s*(?:=|!=|<>|>|<|>=|<=)\\s*[^\\s]+)?)*)",
            Pattern.CASE_INSENSITIVE
        );
        
        Matcher matcher = joinOnPattern.matcher(sql);
        while (matcher.find()) {
            String onClause = matcher.group(1);
            
            // 从ON条件中提取字段名
            Pattern fieldPattern = Pattern.compile(
                "\\b([a-zA-Z_][a-zA-Z0-9_]*)\\s*(?:=|!=|<>|>|<|>=|<=)",
                Pattern.CASE_INSENSITIVE
            );
            
            Matcher fieldMatcher = fieldPattern.matcher(onClause);
            while (fieldMatcher.find()) {
                String field = fieldMatcher.group(1);
                // 过滤掉SQL关键字和函数名
                if (!isReservedWord(field)) {
                    // 去掉表前缀，只保留字段名
                    if (field.contains(".")) {
                        field = field.substring(field.lastIndexOf(".") + 1);
                    }
                    fields.add(field);
                }
            }
        }
        
        return fields;
    }

    private String removeExistsSubqueries(String whereClause) {
        // 移除EXISTS和NOT EXISTS子查询
        return EXISTS_PATTERN.matcher(whereClause).replaceAll("");
    }

    private Map<String, Set<String>> extractExistsFields(String sql) {
        Map<String, Set<String>> existsFieldsMap = new HashMap<>();
        
        // 找到所有EXISTS子查询
        Matcher matcher = EXISTS_PATTERN.matcher(sql);
        while (matcher.find()) {
            String subquery = matcher.group(0); // 获取完整的 EXISTS 子查询
            
            // 提取子查询中的表名
            Set<String> subqueryTables = extractTableNames("SELECT * FROM " + subquery);
            
            // 提取子查询中的WHERE字段
            Set<String> subqueryFields = extractWhereFieldsFromSubquery(subquery);
            
            // 为子查询字段匹配表名
            for (String field : subqueryFields) {
                String matchedTable = findTableForField(field, subqueryTables, subquery);
                existsFieldsMap.computeIfAbsent(matchedTable, k -> new HashSet<>()).add(field);
            }
        }
        
        return existsFieldsMap;
    }

    private Set<String> extractWhereFieldsFromSubquery(String subquery) {
        Set<String> fields = new HashSet<>();
        
        // 在子查询中查找WHERE条件
        String upperSubquery = subquery.toUpperCase();
        int whereIndex = upperSubquery.indexOf(" WHERE ");
        if (whereIndex == -1) {
            return fields;
        }
        
        String whereClause = subquery.substring(whereIndex + 7);
        whereClause = removeSubsequentClauses(whereClause);
        
        // 使用正则表达式提取字段名
        Matcher matcher = WHERE_FIELD_PATTERN.matcher(whereClause);
        while (matcher.find()) {
            String field = matcher.group(1);
            if (!isReservedWord(field)) {
                // 去掉表前缀
                if (field.contains(".")) {
                    field = field.substring(field.lastIndexOf(".") + 1);
                }
                fields.add(field);
            }
        }
        
        return fields;
    }
}









