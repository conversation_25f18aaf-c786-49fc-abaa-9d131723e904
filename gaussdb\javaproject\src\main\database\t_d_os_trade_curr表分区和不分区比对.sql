--造一张功能测试用的源表，后续压力测试用正式表
CREATE TABLE t_d_os_trade_curr_test_src (
    id_d_os_trade nvarchar2(20),
    c_port_code nvarchar2(20) NOT NULL,
    c_cfg_code nvarchar2(30) NOT NULL,
    d_date timestamp(0) without time zone NOT NULL,
    d_trade timestamp(0) without time zone NOT NULL,
    c_dt_type nvarchar2(20),
    c_sh_acc_code nvarchar2(20) NOT NULL,
    c_sec_mkt_code nvarchar2(50),
    c_sec_code nvarchar2(200),
    c_dc_code nvarchar2(20) NOT NULL,
    c_sec_code_old nvarchar2(200),
    c_mkt_code nvarchar2(20) NOT NULL,
    c_td_type nvarchar2(20) NOT NULL,
    c_td_chan_code nvarchar2(50),
    n_td_amount numeric(18,4),
    n_td_price numeric(18,4),
    n_td_money numeric(18,4),
    c_app_no nvarchar2(50),
    c_sec_var_code nvarchar2(20) NOT NULL,
    c_dta_code nvarchar2(20),
    c_dv_issue_mode nvarchar2(20),
    c_dv_invest_cls nvarchar2(20) NOT NULL,
    c_aux_sign nvarchar2(40),
    d_sett timestamp(0) without time zone,
    n_sett_money_t numeric(18,4),
    c_dc_code_sett nvarchar2(20),
    n_sett_money_s numeric(18,4),
    n_rate numeric(30,15),
    c_td_no nvarchar2(50) NOT NULL,
    n_jyfy numeric(30,15),
    n_yj numeric(30,15),
    n_lx numeric(30,15),
    c_path nvarchar2(200),
    c_dv_qut_mod nvarchar2(20),
    c_dv_plat nvarchar2(20),
    c_da_code nvarchar2(30),
    c_dv_type_sub nvarchar2(20),
    d_sett_fact timestamp(0) without time zone,
    c_busi_type nvarchar2(50),
    n_yhs numeric(30,15),
    n_ghf numeric(30,15),
    n_jsof numeric(30,15),
    n_jsuf numeric(30,15),
    n_fxj numeric(30,15),
    n_zgf numeric(30,15),
    n_qtf numeric(30,15),
    n_dc_rate numeric(30,15),
    n_jyj numeric(30,15),
    id_d_ac_td_ivt nvarchar2(30),
    c_isenable nvarchar2(30),
    c_day_year nvarchar2(20),
    c_time nvarchar2(20),
    c_iden nvarchar2(30) NOT NULL
)
WITH (orientation=row, fillfactor=90, compression=no);
insert into t_d_os_trade_curr_test_src;
select * from t_d_os_trade_curr t where t.c_port_code in (select t.c_port_code from t_p_ab_group_rela t where t.c_group_code = 'jk1000Fa42');
SET maintenance_work_mem = '20GB';
ALTER TABLE t_d_os_trade_curr_test_src SET (parallel_workers=32);
\pararllel on 5
CREATE INDEX idx_t_d_os_trade_curr_test_src_01 ON t_d_os_trade_curr_test_src USING btree (id_d_os_trade) with(fillfactor = 90) TABLESPACE pg_default;
CREATE INDEX idx_t_d_os_trade_curr_test_src_02 ON t_d_os_trade_curr_test_src USING btree (c_port_code, d_trade, c_dt_type, c_dv_type_sub, c_cfg_code) with(fillfactor = 90) TABLESPACE pg_default;
CREATE INDEX idx_t_d_os_trade_curr_test_src_03 ON t_d_os_trade_curr_test_src USING btree (id_d_ac_td_ivt) with(fillfactor = 90) TABLESPACE pg_default;
CREATE INDEX idx_t_d_os_trade_curr_test_src_04 ON t_d_os_trade_curr_test_src USING btree (c_port_code, d_trade, c_cfg_code) with(fillfactor = 90) TABLESPACE pg_default;
CREATE INDEX idx_t_d_os_trade_curr_test_src_05 ON t_d_os_trade_curr_test_src USING btree (c_port_code, d_trade, c_dt_type, c_dv_type_sub, c_app_no) with(fillfactor = 90) TABLESPACE pg_default; 
\parallel off
ALTER TABLE t_d_os_trade_curr_test_src ADD CONSTRAINT pk_t_d_os_trade_curr_test_src PRIMARY KEY (c_iden) with(fillfactor = 90);
ALTER TABLE t_d_os_trade_curr_test_src RESET (parallel_workers);

--不分区
CREATE TABLE t_d_os_trade_curr_test_dest1 (
    id_d_os_trade nvarchar2(20),
    c_port_code nvarchar2(20) NOT NULL,
    c_cfg_code nvarchar2(30) NOT NULL,
    d_date timestamp(0) without time zone NOT NULL,
    d_trade timestamp(0) without time zone NOT NULL,
    c_dt_type nvarchar2(20),
    c_sh_acc_code nvarchar2(20) NOT NULL,
    c_sec_mkt_code nvarchar2(50),
    c_sec_code nvarchar2(200),
    c_dc_code nvarchar2(20) NOT NULL,
    c_sec_code_old nvarchar2(200),
    c_mkt_code nvarchar2(20) NOT NULL,
    c_td_type nvarchar2(20) NOT NULL,
    c_td_chan_code nvarchar2(50),
    n_td_amount numeric(18,4),
    n_td_price numeric(18,4),
    n_td_money numeric(18,4),
    c_app_no nvarchar2(50),
    c_sec_var_code nvarchar2(20) NOT NULL,
    c_dta_code nvarchar2(20),
    c_dv_issue_mode nvarchar2(20),
    c_dv_invest_cls nvarchar2(20) NOT NULL,
    c_aux_sign nvarchar2(40),
    d_sett timestamp(0) without time zone,
    n_sett_money_t numeric(18,4),
    c_dc_code_sett nvarchar2(20),
    n_sett_money_s numeric(18,4),
    n_rate numeric(30,15),
    c_td_no nvarchar2(50) NOT NULL,
    n_jyfy numeric(30,15),
    n_yj numeric(30,15),
    n_lx numeric(30,15),
    c_path nvarchar2(200),
    c_dv_qut_mod nvarchar2(20),
    c_dv_plat nvarchar2(20),
    c_da_code nvarchar2(30),
    c_dv_type_sub nvarchar2(20),
    d_sett_fact timestamp(0) without time zone,
    c_busi_type nvarchar2(50),
    n_yhs numeric(30,15),
    n_ghf numeric(30,15),
    n_jsof numeric(30,15),
    n_jsuf numeric(30,15),
    n_fxj numeric(30,15),
    n_zgf numeric(30,15),
    n_qtf numeric(30,15),
    n_dc_rate numeric(30,15),
    n_jyj numeric(30,15),
    id_d_ac_td_ivt nvarchar2(30),
    c_isenable nvarchar2(30),
    c_day_year nvarchar2(20),
    c_time nvarchar2(20),
    c_iden nvarchar2(30) NOT NULL
)
WITH (orientation=row, fillfactor=90, compression=no);
CREATE INDEX idx_t_d_os_trade_curr_test_dest1_01 ON t_d_os_trade_curr_test_dest1 USING btree (id_d_os_trade) with(fillfactor = 90) TABLESPACE pg_default;
CREATE INDEX idx_t_d_os_trade_curr_test_dest1_02 ON t_d_os_trade_curr_test_dest1 USING btree (c_port_code, d_trade, c_dt_type, c_dv_type_sub, c_cfg_code) with(fillfactor = 90) TABLESPACE pg_default;
CREATE INDEX idx_t_d_os_trade_curr_test_dest1_03 ON t_d_os_trade_curr_test_dest1 USING btree (id_d_ac_td_ivt) with(fillfactor = 90) TABLESPACE pg_default;
CREATE INDEX idx_t_d_os_trade_curr_test_dest1_04 ON t_d_os_trade_curr_test_dest1 USING btree (c_port_code, d_trade, c_cfg_code) with(fillfactor = 90) TABLESPACE pg_default;
CREATE INDEX idx_t_d_os_trade_curr_test_dest1_05 ON t_d_os_trade_curr_test_dest1 USING btree (c_port_code, d_trade, c_dt_type, c_dv_type_sub, c_app_no) with(fillfactor = 90) TABLESPACE pg_default; 
ALTER TABLE t_d_os_trade_curr_test_dest1 ADD CONSTRAINT pk_t_d_os_trade_curr_test_dest1 PRIMARY KEY (c_iden) with(fillfactor = 90);
--分区表
CREATE TABLE t_d_os_trade_curr_test_dest2 (
    id_d_os_trade nvarchar2(20),
    c_port_code nvarchar2(20) NOT NULL,
    c_cfg_code nvarchar2(30) NOT NULL,
    d_date timestamp(0) without time zone NOT NULL,
    d_trade timestamp(0) without time zone NOT NULL,
    c_dt_type nvarchar2(20),
    c_sh_acc_code nvarchar2(20) NOT NULL,
    c_sec_mkt_code nvarchar2(50),
    c_sec_code nvarchar2(200),
    c_dc_code nvarchar2(20) NOT NULL,
    c_sec_code_old nvarchar2(200),
    c_mkt_code nvarchar2(20) NOT NULL,
    c_td_type nvarchar2(20) NOT NULL,
    c_td_chan_code nvarchar2(50),
    n_td_amount numeric(18,4),
    n_td_price numeric(18,4),
    n_td_money numeric(18,4),
    c_app_no nvarchar2(50),
    c_sec_var_code nvarchar2(20) NOT NULL,
    c_dta_code nvarchar2(20),
    c_dv_issue_mode nvarchar2(20),
    c_dv_invest_cls nvarchar2(20) NOT NULL,
    c_aux_sign nvarchar2(40),
    d_sett timestamp(0) without time zone,
    n_sett_money_t numeric(18,4),
    c_dc_code_sett nvarchar2(20),
    n_sett_money_s numeric(18,4),
    n_rate numeric(30,15),
    c_td_no nvarchar2(50) NOT NULL,
    n_jyfy numeric(30,15),
    n_yj numeric(30,15),
    n_lx numeric(30,15),
    c_path nvarchar2(200),
    c_dv_qut_mod nvarchar2(20),
    c_dv_plat nvarchar2(20),
    c_da_code nvarchar2(30),
    c_dv_type_sub nvarchar2(20),
    d_sett_fact timestamp(0) without time zone,
    c_busi_type nvarchar2(50),
    n_yhs numeric(30,15),
    n_ghf numeric(30,15),
    n_jsof numeric(30,15),
    n_jsuf numeric(30,15),
    n_fxj numeric(30,15),
    n_zgf numeric(30,15),
    n_qtf numeric(30,15),
    n_dc_rate numeric(30,15),
    n_jyj numeric(30,15),
    id_d_ac_td_ivt nvarchar2(30),
    c_isenable nvarchar2(30),
    c_day_year nvarchar2(20),
    c_time nvarchar2(20),
    c_iden nvarchar2(30) NOT NULL
)
WITH (orientation=row, fillfactor=90, compression=no)
PARTITION BY hash (c_port_code)
( 
PARTITION part_001 TABLESPACE pg_default,
PARTITION part_002 TABLESPACE pg_default,
PARTITION part_003 TABLESPACE pg_default,
PARTITION part_004 TABLESPACE pg_default,
PARTITION part_005 TABLESPACE pg_default,
PARTITION part_006 TABLESPACE pg_default,
PARTITION part_007 TABLESPACE pg_default,
PARTITION part_008 TABLESPACE pg_default,
PARTITION part_009 TABLESPACE pg_default,
PARTITION part_010 TABLESPACE pg_default,
PARTITION part_011 TABLESPACE pg_default,
PARTITION part_012 TABLESPACE pg_default,
PARTITION part_013 TABLESPACE pg_default,
PARTITION part_014 TABLESPACE pg_default,
PARTITION part_015 TABLESPACE pg_default,
PARTITION part_016 TABLESPACE pg_default,
PARTITION part_017 TABLESPACE pg_default,
PARTITION part_018 TABLESPACE pg_default,
PARTITION part_019 TABLESPACE pg_default,
PARTITION part_020 TABLESPACE pg_default,
PARTITION part_021 TABLESPACE pg_default,
PARTITION part_022 TABLESPACE pg_default,
PARTITION part_023 TABLESPACE pg_default,
PARTITION part_024 TABLESPACE pg_default,
PARTITION part_025 TABLESPACE pg_default,
PARTITION part_026 TABLESPACE pg_default,
PARTITION part_027 TABLESPACE pg_default,
PARTITION part_028 TABLESPACE pg_default,
PARTITION part_029 TABLESPACE pg_default,
PARTITION part_030 TABLESPACE pg_default,
PARTITION part_031 TABLESPACE pg_default,
PARTITION part_032 TABLESPACE pg_default
);
CREATE INDEX idx_t_d_os_trade_curr_test_dest2_01 ON t_d_os_trade_curr_test_dest2 USING btree (id_d_os_trade) local with(fillfactor = 90) TABLESPACE pg_default;
CREATE INDEX idx_t_d_os_trade_curr_test_dest2_02 ON t_d_os_trade_curr_test_dest2 USING btree (c_port_code, d_trade, c_dt_type, c_dv_type_sub, c_cfg_code) local with(fillfactor = 90) TABLESPACE pg_default;
CREATE INDEX idx_t_d_os_trade_curr_test_dest2_03 ON t_d_os_trade_curr_test_dest2 USING btree (id_d_ac_td_ivt) local with(fillfactor = 90) TABLESPACE pg_default;
CREATE INDEX idx_t_d_os_trade_curr_test_dest2_04 ON t_d_os_trade_curr_test_dest2 USING btree (c_port_code, d_trade, c_cfg_code) local with(fillfactor = 90) TABLESPACE pg_default;
CREATE INDEX idx_t_d_os_trade_curr_test_dest2_05 ON t_d_os_trade_curr_test_dest2 USING btree (c_port_code, d_trade, c_dt_type, c_dv_type_sub, c_app_no) local with(fillfactor = 90) TABLESPACE pg_default; 
ALTER TABLE t_d_os_trade_curr_test_dest2 ADD CONSTRAINT pk_t_d_os_trade_curr_test_dest2 PRIMARY KEY (c_iden) with(fillfactor = 90);