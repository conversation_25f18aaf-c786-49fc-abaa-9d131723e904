package com.example.stress.monitor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * 性能监控器
 * 记录操作的性能指标
 */
public class PerformanceMonitor {
    private static final Logger logger = LoggerFactory.getLogger(PerformanceMonitor.class);
    
    // Delete操作统计
    private final LongAdder deleteSuccessCount = new LongAdder();
    private final LongAdder deleteFailureCount = new LongAdder();
    private final LongAdder deleteTotalTime = new LongAdder(); // 纳秒
    private final AtomicLong deleteMinTime = new AtomicLong(Long.MAX_VALUE);
    private final AtomicLong deleteMaxTime = new AtomicLong(0);
    
    // Insert操作统计
    private final LongAdder insertSuccessCount = new LongAdder();
    private final LongAdder insertFailureCount = new LongAdder();
    private final LongAdder insertTotalTime = new LongAdder(); // 纳秒
    private final AtomicLong insertMinTime = new AtomicLong(Long.MAX_VALUE);
    private final AtomicLong insertMaxTime = new AtomicLong(0);
    
    // Update操作统计
    private final LongAdder updateSuccessCount = new LongAdder();
    private final LongAdder updateFailureCount = new LongAdder();
    private final LongAdder updateTotalTime = new LongAdder(); // 纳秒
    private final AtomicLong updateMinTime = new AtomicLong(Long.MAX_VALUE);
    private final AtomicLong updateMaxTime = new AtomicLong(0);
    
    /**
     * 记录Delete操作
     */
    public void recordDeleteOperation(long durationNanos, boolean success) {
        if (success) {
            deleteSuccessCount.increment();
        } else {
            deleteFailureCount.increment();
        }
        
        deleteTotalTime.add(durationNanos);
        updateMinMax(deleteMinTime, deleteMaxTime, durationNanos);
    }
    
    /**
     * 记录Insert操作
     */
    public void recordInsertOperation(long durationNanos, boolean success) {
        if (success) {
            insertSuccessCount.increment();
        } else {
            insertFailureCount.increment();
        }
        
        insertTotalTime.add(durationNanos);
        updateMinMax(insertMinTime, insertMaxTime, durationNanos);
    }
    
    /**
     * 记录Update操作
     */
    public void recordUpdateOperation(long durationNanos, boolean success) {
        if (success) {
            updateSuccessCount.increment();
        } else {
            updateFailureCount.increment();
        }
        
        updateTotalTime.add(durationNanos);
        updateMinMax(updateMinTime, updateMaxTime, durationNanos);
    }
    
    /**
     * 更新最小最大时间
     */
    private void updateMinMax(AtomicLong minTime, AtomicLong maxTime, long duration) {
        minTime.updateAndGet(current -> Math.min(current, duration));
        maxTime.updateAndGet(current -> Math.max(current, duration));
    }
    
    /**
     * 获取Delete操作统计
     */
    public OperationStats getDeleteStats() {
        long successCount = deleteSuccessCount.sum();
        long failureCount = deleteFailureCount.sum();
        long totalTime = deleteTotalTime.sum();
        long minTime = deleteMinTime.get() == Long.MAX_VALUE ? 0 : deleteMinTime.get();
        long maxTime = deleteMaxTime.get();
        
        return new OperationStats("DELETE", successCount, failureCount, totalTime, minTime, maxTime);
    }
    
    /**
     * 获取Insert操作统计
     */
    public OperationStats getInsertStats() {
        long successCount = insertSuccessCount.sum();
        long failureCount = insertFailureCount.sum();
        long totalTime = insertTotalTime.sum();
        long minTime = insertMinTime.get() == Long.MAX_VALUE ? 0 : insertMinTime.get();
        long maxTime = insertMaxTime.get();
        
        return new OperationStats("INSERT", successCount, failureCount, totalTime, minTime, maxTime);
    }
    
    /**
     * 获取Update操作统计
     */
    public OperationStats getUpdateStats() {
        long successCount = updateSuccessCount.sum();
        long failureCount = updateFailureCount.sum();
        long totalTime = updateTotalTime.sum();
        long minTime = updateMinTime.get() == Long.MAX_VALUE ? 0 : updateMinTime.get();
        long maxTime = updateMaxTime.get();
        
        return new OperationStats("UPDATE", successCount, failureCount, totalTime, minTime, maxTime);
    }
    
    /**
     * 打印性能统计
     */
    public void printStats() {
        logger.info("=== Performance Statistics ===");
        
        OperationStats deleteStats = getDeleteStats();
        OperationStats insertStats = getInsertStats();
        OperationStats updateStats = getUpdateStats();
        
        printOperationStats(deleteStats);
        printOperationStats(insertStats);
        printOperationStats(updateStats);
        
        // 总体统计
        long totalOperations = deleteStats.getTotalOperations() + 
                              insertStats.getTotalOperations() + 
                              updateStats.getTotalOperations();
        long totalSuccessful = deleteStats.getSuccessCount() + 
                              insertStats.getSuccessCount() + 
                              updateStats.getSuccessCount();
        
        if (totalOperations > 0) {
            double overallSuccessRate = (double) totalSuccessful / totalOperations * 100;
            logger.info("Overall: {} operations, {}% success rate",
                       totalOperations, String.format("%.2f", overallSuccessRate));
        }
    }
    
    /**
     * 打印单个操作的统计信息
     */
    private void printOperationStats(OperationStats stats) {
        if (stats.getTotalOperations() == 0) {
            logger.info("{}: No operations recorded", stats.getOperationType());
            return;
        }
        
        logger.info("{}: {} ops ({}S/{}F), Avg: {}ms, Min: {}ms, Max: {}ms, Success: {}%",
                   stats.getOperationType(),
                   stats.getTotalOperations(),
                   stats.getSuccessCount(),
                   stats.getFailureCount(),
                   String.format("%.2f", stats.getAverageTimeMs()),
                   String.format("%.2f", stats.getMinTimeMs()),
                   String.format("%.2f", stats.getMaxTimeMs()),
                   String.format("%.2f", stats.getSuccessRate()));
    }
    
    /**
     * 重置所有统计信息
     */
    public void reset() {
        deleteSuccessCount.reset();
        deleteFailureCount.reset();
        deleteTotalTime.reset();
        deleteMinTime.set(Long.MAX_VALUE);
        deleteMaxTime.set(0);
        
        insertSuccessCount.reset();
        insertFailureCount.reset();
        insertTotalTime.reset();
        insertMinTime.set(Long.MAX_VALUE);
        insertMaxTime.set(0);
        
        updateSuccessCount.reset();
        updateFailureCount.reset();
        updateTotalTime.reset();
        updateMinTime.set(Long.MAX_VALUE);
        updateMaxTime.set(0);
        
        logger.info("Performance monitor statistics reset");
    }
    
    /**
     * 合并另一个监控器的统计信息
     */
    public void merge(PerformanceMonitor other) {
        // 合并Delete统计
        OperationStats otherDeleteStats = other.getDeleteStats();
        if (otherDeleteStats.getTotalOperations() > 0) {
            deleteSuccessCount.add(otherDeleteStats.getSuccessCount());
            deleteFailureCount.add(otherDeleteStats.getFailureCount());
            deleteTotalTime.add(otherDeleteStats.getTotalTimeNanos());
            updateMinMax(deleteMinTime, deleteMaxTime, otherDeleteStats.getMinTimeNanos());
            updateMinMax(deleteMinTime, deleteMaxTime, otherDeleteStats.getMaxTimeNanos());
        }
        
        // 合并Insert统计
        OperationStats otherInsertStats = other.getInsertStats();
        if (otherInsertStats.getTotalOperations() > 0) {
            insertSuccessCount.add(otherInsertStats.getSuccessCount());
            insertFailureCount.add(otherInsertStats.getFailureCount());
            insertTotalTime.add(otherInsertStats.getTotalTimeNanos());
            updateMinMax(insertMinTime, insertMaxTime, otherInsertStats.getMinTimeNanos());
            updateMinMax(insertMinTime, insertMaxTime, otherInsertStats.getMaxTimeNanos());
        }
        
        // 合并Update统计
        OperationStats otherUpdateStats = other.getUpdateStats();
        if (otherUpdateStats.getTotalOperations() > 0) {
            updateSuccessCount.add(otherUpdateStats.getSuccessCount());
            updateFailureCount.add(otherUpdateStats.getFailureCount());
            updateTotalTime.add(otherUpdateStats.getTotalTimeNanos());
            updateMinMax(updateMinTime, updateMaxTime, otherUpdateStats.getMinTimeNanos());
            updateMinMax(updateMinTime, updateMaxTime, otherUpdateStats.getMaxTimeNanos());
        }
    }
}
