package com.example.stress.model;

import java.util.List;
import java.util.Objects;

/**
 * 表结构信息
 * 用于动态获取和管理表的字段结构
 */
public class TableStructure {
    private final String tableName;
    private final List<ColumnInfo> columns;
    
    public TableStructure(String tableName, List<ColumnInfo> columns) {
        this.tableName = tableName;
        this.columns = columns;
    }
    
    public String getTableName() {
        return tableName;
    }
    
    public List<ColumnInfo> getColumns() {
        return columns;
    }
    
    /**
     * 根据列名查找列信息
     */
    public ColumnInfo getColumnByName(String columnName) {
        return columns.stream()
                .filter(col -> col.getColumnName().equalsIgnoreCase(columnName))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 检查是否包含指定列
     */
    public boolean hasColumn(String columnName) {
        return getColumnByName(columnName) != null;
    }
    
    /**
     * 获取列数量
     */
    public int getColumnCount() {
        return columns.size();
    }
    
    /**
     * 生成INSERT语句的列名部分
     */
    public String getInsertColumnList() {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < columns.size(); i++) {
            if (i > 0) {
                sb.append(", ");
            }
            sb.append(columns.get(i).getColumnName());
        }
        return sb.toString();
    }
    
    /**
     * 生成INSERT语句的VALUES部分
     */
    public String getInsertValuesList() {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < columns.size(); i++) {
            if (i > 0) {
                sb.append(", ");
            }
            sb.append("?");
        }
        return sb.toString();
    }
    
    /**
     * 生成SELECT语句的列名部分
     */
    public String getSelectColumnList() {
        return getInsertColumnList(); // 与INSERT的列名部分相同
    }
    
    @Override
    public String toString() {
        return String.format("TableStructure{tableName='%s', columnCount=%d}", 
                           tableName, columns.size());
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TableStructure that = (TableStructure) o;
        return Objects.equals(tableName, that.tableName) &&
               Objects.equals(columns, that.columns);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(tableName, columns);
    }
    
    /**
     * 列信息类
     */
    public static class ColumnInfo {
        private final String columnName;
        private final String dataType;
        private final int columnOrder;
        
        public ColumnInfo(String columnName, String dataType, int columnOrder) {
            this.columnName = columnName;
            this.dataType = dataType;
            this.columnOrder = columnOrder;
        }
        
        public String getColumnName() { 
            return columnName; 
        }
        
        public String getDataType() { 
            return dataType; 
        }
        
        public int getColumnOrder() { 
            return columnOrder; 
        }
        
        /**
         * 判断是否为日期时间类型
         */
        public boolean isDateTimeType() {
            String type = dataType.toUpperCase();
            return type.contains("DATE") || type.contains("TIME") || type.contains("TIMESTAMP");
        }
        
        /**
         * 判断是否为字符串类型
         */
        public boolean isStringType() {
            String type = dataType.toUpperCase();
            return type.contains("CHAR") || type.contains("TEXT") || type.contains("STRING");
        }
        
        /**
         * 判断是否为数值类型
         */
        public boolean isNumericType() {
            String type = dataType.toUpperCase();
            return type.contains("INT") || type.contains("DECIMAL") || type.contains("NUMERIC") ||
                   type.contains("FLOAT") || type.contains("DOUBLE") || type.contains("NUMBER");
        }
        
        /**
         * 判断是否为大整数类型
         */
        public boolean isBigIntType() {
            String type = dataType.toUpperCase();
            return type.contains("BIGINT") || type.contains("LONG");
        }
        
        /**
         * 判断是否为小数类型
         */
        public boolean isDecimalType() {
            String type = dataType.toUpperCase();
            return type.contains("DECIMAL") || type.contains("NUMERIC");
        }
        
        /**
         * 判断是否为浮点数类型
         */
        public boolean isFloatType() {
            String type = dataType.toUpperCase();
            return type.contains("FLOAT") || type.contains("DOUBLE");
        }
        
        @Override
        public String toString() {
            return String.format("ColumnInfo{name='%s', type='%s', order=%d}",
                               columnName, dataType, columnOrder);
        }
        
        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            ColumnInfo that = (ColumnInfo) o;
            return columnOrder == that.columnOrder &&
                   Objects.equals(columnName, that.columnName) &&
                   Objects.equals(dataType, that.dataType);
        }
        
        @Override
        public int hashCode() {
            return Objects.hash(columnName, dataType, columnOrder);
        }
    }
}
