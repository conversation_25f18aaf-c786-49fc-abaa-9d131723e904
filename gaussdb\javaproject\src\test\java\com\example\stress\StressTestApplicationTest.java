package com.example.stress;

import com.example.stress.config.StressTestConfig;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 压力测试应用程序的单元测试
 */
public class StressTestApplicationTest {
    
    private StressTestConfig config;
    
    @BeforeEach
    void setUp() {
        config = new StressTestConfig();
    }
    
    @Test
    void testDefaultConfiguration() {
        assertEquals(10, config.getConcurrency());
        assertEquals(50, config.getPortCodeCount());
        assertEquals(100, config.getRecordsPerPortCode());
        assertEquals(50, config.getBatchSize());
        assertEquals(1, config.getTestRounds());
        assertEquals(1000, config.getRoundIntervalMs());
        assertTrue(config.isInitData());
        assertFalse(config.isPrintDetailedResults());
    }
    
    @Test
    void testConfigurationFromArgs() {
        String[] args = {"-c", "20", "-p", "30", "-r", "50", "-b", "25", "--rounds", "3", "--detailed"};
        StressTestConfig config = StressTestConfig.fromArgs(args);

        assertEquals(20, config.getConcurrency());
        assertEquals(30, config.getPortCodeCount());
        assertEquals(50, config.getRecordsPerPortCode());
        assertEquals(25, config.getBatchSize());
        assertEquals(3, config.getTestRounds());
        assertTrue(config.isPrintDetailedResults());
    }
    
    @Test
    void testConfigurationValidation() {
        // 测试无效的并发度
        assertThrows(IllegalArgumentException.class, () -> {
            String[] args = {"-c", "0"};
            StressTestConfig.fromArgs(args);
        });

        // 测试无效的port code数量
        assertThrows(IllegalArgumentException.class, () -> {
            String[] args = {"-p", "-1"};
            StressTestConfig.fromArgs(args);
        });
    }
    
    @Test
    void testTotalRecordsCalculation() {
        config.setPortCodeCount(10);
        config.setRecordsPerPortCode(100);
        assertEquals(1000, config.getTotalRecords());
    }
    
    @Test
    void testNoInitArgument() {
        // 测试--no-init参数
        String[] args = {"--no-init"};
        StressTestConfig config = StressTestConfig.fromArgs(args);
        assertFalse(config.isInitData());
    }
}
