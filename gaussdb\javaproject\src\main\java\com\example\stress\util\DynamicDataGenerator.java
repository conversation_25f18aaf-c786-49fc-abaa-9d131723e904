package com.example.stress.util;

import com.example.stress.model.TableStructure;
import com.example.stress.model.TableStructure.ColumnInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.*;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 动态数据生成器
 * 根据表结构动态生成测试数据
 */
public class DynamicDataGenerator {
    private static final Logger logger = LoggerFactory.getLogger(DynamicDataGenerator.class);
    
    private static final String[] SAMPLE_STRINGS = {
        "测试数据", "压力测试", "性能测试", "数据库测试", "并发测试",
        "Test Data", "Performance Test", "Stress Test", "Database Test", "Concurrent Test"
    };
    
    private static final String[] SAMPLE_DESCRIPTIONS = {
        "这是一条测试数据", "用于压力测试的数据", "性能测试数据记录",
        "数据库并发测试数据", "系统压力测试样本数据"
    };
    
    /**
     * 生成测试数据并插入到指定表
     */
    public static void generateAndInsertTestData(Connection connection, TableStructure tableStructure, 
                                               String[] portCodes, int recordsPerPort) throws SQLException {
        String insertSql = DynamicSqlBuilder.buildInsertSql(tableStructure);
        logger.info("Generating test data with SQL: {}", insertSql);
        
        try (PreparedStatement stmt = connection.prepareStatement(insertSql)) {
            connection.setAutoCommit(false);
            
            long idCounter = 1;
            for (String portCode : portCodes) {
                for (int i = 0; i < recordsPerPort; i++) {
                    setDynamicParameters(stmt, tableStructure, idCounter++, portCode);
                    stmt.addBatch();
                    
                    // 每1000条执行一次批处理
                    if (idCounter % 1000 == 0) {
                        stmt.executeBatch();
                        connection.commit();
                        logger.debug("Generated {} test records", idCounter - 1);
                    }
                }
            }
            
            // 执行剩余的批处理
            stmt.executeBatch();
            connection.commit();
            
            logger.info("Successfully generated {} test data records for {} port codes", 
                       idCounter - 1, portCodes.length);
        }
    }
    
    /**
     * 根据表结构动态设置参数
     */
    private static void setDynamicParameters(PreparedStatement stmt, TableStructure tableStructure, 
                                           long id, String portCode) throws SQLException {
        for (int i = 0; i < tableStructure.getColumns().size(); i++) {
            ColumnInfo column = tableStructure.getColumns().get(i);
            int paramIndex = i + 1;
            
            setParameterByColumnName(stmt, column, paramIndex, id, portCode);
        }
    }
    
    /**
     * 根据列名和类型设置参数值
     */
    private static void setParameterByColumnName(PreparedStatement stmt, ColumnInfo column, 
                                               int paramIndex, long id, String portCode) throws SQLException {
        String columnName = column.getColumnName().toLowerCase();
        
        try {
            // 根据列名设置特定值
            if (columnName.equals("id") || columnName.endsWith("_id")) {
                stmt.setLong(paramIndex, id);
            } else if (columnName.equals("c_port_code") || columnName.contains("port_code")) {
                stmt.setString(paramIndex, portCode);
            } else if (columnName.contains("status")) {
                stmt.setInt(paramIndex, ThreadLocalRandom.current().nextInt(0, 3)); // 0, 1, 2
            } else if (columnName.contains("create_time") || columnName.contains("createtime")) {
                stmt.setTimestamp(paramIndex, new Timestamp(System.currentTimeMillis()));
            } else if (columnName.contains("update_time") || columnName.contains("updatetime")) {
                stmt.setTimestamp(paramIndex, new Timestamp(System.currentTimeMillis()));
            } else if (columnName.contains("description") || columnName.contains("desc")) {
                stmt.setString(paramIndex, getRandomDescription());
            } else if (columnName.contains("data_value") || columnName.contains("value")) {
                stmt.setString(paramIndex, getRandomDataValue());
            } else {
                // 根据数据类型设置默认值
                setParameterByDataType(stmt, column, paramIndex, id);
            }
        } catch (SQLException e) {
            logger.warn("Failed to set parameter {} ({}) with specific logic, using type-based approach: {}", 
                       paramIndex, columnName, e.getMessage());
            setParameterByDataType(stmt, column, paramIndex, id);
        }
    }
    
    /**
     * 根据数据类型设置参数值
     */
    private static void setParameterByDataType(PreparedStatement stmt, ColumnInfo column, 
                                             int paramIndex, long id) throws SQLException {
        if (column.isDateTimeType()) {
            stmt.setTimestamp(paramIndex, new Timestamp(System.currentTimeMillis()));
        } else if (column.isBigIntType()) {
            stmt.setLong(paramIndex, id);
        } else if (column.isDecimalType()) {
            stmt.setBigDecimal(paramIndex, new BigDecimal(ThreadLocalRandom.current().nextDouble(1.0, 1000.0)));
        } else if (column.isFloatType()) {
            if (column.getDataType().toUpperCase().contains("DOUBLE")) {
                stmt.setDouble(paramIndex, ThreadLocalRandom.current().nextDouble(1.0, 1000.0));
            } else {
                stmt.setFloat(paramIndex, ThreadLocalRandom.current().nextFloat() * 1000);
            }
        } else if (column.isNumericType()) {
            if (column.getDataType().toUpperCase().contains("INT")) {
                stmt.setInt(paramIndex, ThreadLocalRandom.current().nextInt(1, 1000));
            } else {
                stmt.setLong(paramIndex, ThreadLocalRandom.current().nextLong(1, 10000));
            }
        } else {
            // 默认作为字符串处理
            stmt.setString(paramIndex, getRandomString());
        }
    }
    
    /**
     * 生成随机字符串
     */
    private static String getRandomString() {
        return SAMPLE_STRINGS[ThreadLocalRandom.current().nextInt(SAMPLE_STRINGS.length)] + 
               "_" + ThreadLocalRandom.current().nextInt(1000);
    }
    
    /**
     * 生成随机描述
     */
    private static String getRandomDescription() {
        return SAMPLE_DESCRIPTIONS[ThreadLocalRandom.current().nextInt(SAMPLE_DESCRIPTIONS.length)] + 
               " - " + System.currentTimeMillis();
    }
    
    /**
     * 生成随机数据值
     */
    private static String getRandomDataValue() {
        return "DATA_" + ThreadLocalRandom.current().nextInt(10000) + "_" + 
               Long.toHexString(System.currentTimeMillis()).toUpperCase();
    }
    
    /**
     * 生成端口代码数组
     */
    public static String[] generatePortCodes(int count) {
        String[] portCodes = new String[count];
        for (int i = 0; i < count; i++) {
            portCodes[i] = String.format("PORT_%03d", i + 1);
        }
        return portCodes;
    }
    
    /**
     * 验证表是否包含必要的列
     */
    public static boolean validateTableForStressTesting(TableStructure tableStructure) {
        boolean hasPortCode = tableStructure.hasColumn("c_port_code");
        boolean hasId = false;
        
        // 检查是否有ID列
        for (ColumnInfo column : tableStructure.getColumns()) {
            String columnName = column.getColumnName().toLowerCase();
            if (columnName.equals("id") || columnName.endsWith("_id")) {
                hasId = true;
                break;
            }
        }
        
        if (!hasPortCode) {
            logger.error("Table {} does not contain required column 'c_port_code'", tableStructure.getTableName());
        }
        if (!hasId) {
            logger.error("Table {} does not contain an ID column", tableStructure.getTableName());
        }
        
        return hasPortCode && hasId;
    }
    
    /**
     * 打印表结构和数据生成策略
     */
    public static void printDataGenerationStrategy(TableStructure tableStructure) {
        logger.info("=== Data Generation Strategy for {} ===", tableStructure.getTableName());
        
        for (ColumnInfo column : tableStructure.getColumns()) {
            String strategy = getGenerationStrategy(column);
            logger.info("  {}: {} -> {}", column.getColumnName(), column.getDataType(), strategy);
        }
    }
    
    /**
     * 获取列的数据生成策略描述
     */
    private static String getGenerationStrategy(ColumnInfo column) {
        String columnName = column.getColumnName().toLowerCase();
        
        if (columnName.equals("id") || columnName.endsWith("_id")) {
            return "Sequential ID";
        } else if (columnName.equals("c_port_code") || columnName.contains("port_code")) {
            return "Port code from parameter";
        } else if (columnName.contains("status")) {
            return "Random integer (0-2)";
        } else if (columnName.contains("create_time") || columnName.contains("update_time")) {
            return "Current timestamp";
        } else if (columnName.contains("description")) {
            return "Random description";
        } else if (columnName.contains("data_value") || columnName.contains("value")) {
            return "Random data value";
        } else if (column.isDateTimeType()) {
            return "Current timestamp";
        } else if (column.isNumericType()) {
            return "Random number";
        } else {
            return "Random string";
        }
    }
    
    /**
     * 清空表数据
     */
    public static void clearTableData(Connection connection, String tableName) throws SQLException {
        String sql = "DELETE FROM " + tableName;
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            int deletedRows = stmt.executeUpdate();
            logger.info("Cleared {} rows from table {}", deletedRows, tableName);
        }
    }
    
    /**
     * 获取表的记录数
     */
    public static long getTableRowCount(Connection connection, String tableName) throws SQLException {
        String sql = "SELECT COUNT(*) FROM " + tableName;
        try (PreparedStatement stmt = connection.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next()) {
                return rs.getLong(1);
            }
        }
        return 0;
    }
}
