package com.example.stress.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * 表名配置管理类
 * 从配置文件中读取压力测试相关的表名
 */
public class TableNameConfig {
    private static final Logger logger = LoggerFactory.getLogger(TableNameConfig.class);
    
    private static final String CONFIG_FILE = "connection-pool.properties";
    private static Properties properties;
    private static boolean initialized = false;
    
    // 默认表名
    private static final String DEFAULT_TEST_DATA_TABLE = "stress_test_data";
    private static final String DEFAULT_TEMP_TABLE = "stress_temp_data";
    private static final String DEFAULT_EXECUTION_TABLE = "stress_execution_data";
    private static final String DEFAULT_PLAN_TABLE = "stress_execution_plan";
    
    // 配置键名
    private static final String TEST_DATA_TABLE_KEY = "stress.test.table.testData";
    private static final String TEMP_TABLE_KEY = "stress.test.table.temp";
    private static final String EXECUTION_TABLE_KEY = "stress.test.table.execution";
    private static final String PLAN_TABLE_KEY = "stress.test.table.plan";
    
    /**
     * 初始化配置
     */
    private static synchronized void initialize() {
        if (initialized) {
            return;
        }
        
        properties = new Properties();
        
        try (InputStream inputStream = TableNameConfig.class.getClassLoader().getResourceAsStream(CONFIG_FILE)) {
            if (inputStream == null) {
                logger.warn("Configuration file {} not found, using default table names", CONFIG_FILE);
            } else {
                properties.load(inputStream);
                logger.info("Table name configuration loaded from {}", CONFIG_FILE);
            }
        } catch (IOException e) {
            logger.error("Failed to load configuration file {}, using default table names", CONFIG_FILE, e);
        }
        
        initialized = true;
        logTableNames();
    }
    
    /**
     * 获取测试数据表名
     */
    public static String getTestDataTable() {
        initialize();
        return getStringProperty(TEST_DATA_TABLE_KEY, DEFAULT_TEST_DATA_TABLE);
    }
    
    /**
     * 获取临时表名
     */
    public static String getTempTable() {
        initialize();
        return getStringProperty(TEMP_TABLE_KEY, DEFAULT_TEMP_TABLE);
    }
    
    /**
     * 获取执行表名
     */
    public static String getExecutionTable() {
        initialize();
        return getStringProperty(EXECUTION_TABLE_KEY, DEFAULT_EXECUTION_TABLE);
    }

    /**
     * 获取方案表名
     */
    public static String getPlanTable() {
        initialize();
        return getStringProperty(PLAN_TABLE_KEY, DEFAULT_PLAN_TABLE);
    }
    
    /**
     * 获取字符串属性
     */
    private static String getStringProperty(String key, String defaultValue) {
        String value = properties.getProperty(key);
        if (value == null || value.trim().isEmpty()) {
            logger.debug("Property {} not found or empty, using default value: {}", key, defaultValue);
            return defaultValue;
        }
        return value.trim();
    }
    
    /**
     * 记录表名配置
     */
    private static void logTableNames() {
        logger.info("=== Table Name Configuration ===");
        logger.info("Test Data Table: {}", getTestDataTable());
        logger.info("Temporary Table: {}", getTempTable());
        logger.info("Execution Table: {}", getExecutionTable());
        logger.info("Plan Table: {}", getPlanTable());
    }
    
    /**
     * 验证表名配置
     */
    public static boolean validateTableNames() {
        initialize();
        
        String testDataTable = getTestDataTable();
        String tempTable = getTempTable();
        String executionTable = getExecutionTable();
        String planTable = getPlanTable();
        
        // 检查表名是否为空
        if (testDataTable.isEmpty() || tempTable.isEmpty() || executionTable.isEmpty() || planTable.isEmpty()) {
            logger.error("One or more table names are empty");
            return false;
        }
        
        // 检查表名是否重复
        if (testDataTable.equals(tempTable) || testDataTable.equals(executionTable) || tempTable.equals(executionTable)) {
            logger.error("Table names must be unique. TestData: {}, Temp: {}, Execution: {}", 
                        testDataTable, tempTable, executionTable);
            return false;
        }
        
        // 检查表名格式（简单验证）
        if (!isValidTableName(testDataTable) || !isValidTableName(tempTable) || !isValidTableName(executionTable) || !isValidTableName(planTable)) {
            logger.error("Invalid table name format detected");
            return false;
        }
        
        logger.info("Table name configuration validation passed");
        return true;
    }
    
    /**
     * 验证表名格式
     */
    private static boolean isValidTableName(String tableName) {
        // 简单的表名格式验证：只允许字母、数字和下划线，且不能以数字开头
        return tableName.matches("^[a-zA-Z_][a-zA-Z0-9_]*$");
    }
    
    /**
     * 重新加载配置
     */
    public static synchronized void reload() {
        initialized = false;
        initialize();
    }
    
    /**
     * 获取所有表名的数组
     */
    public static String[] getAllTableNames() {
        initialize();
        return new String[]{
            getTestDataTable(),
            getTempTable(),
            getExecutionTable(),
            getPlanTable()
        };
    }
    
    /**
     * 打印配置信息
     */
    public static void printConfiguration() {
        initialize();
        logger.info("=== Current Table Name Configuration ===");
        logger.info("Configuration file: {}", CONFIG_FILE);
        logger.info("Test Data Table: {} (key: {})", getTestDataTable(), TEST_DATA_TABLE_KEY);
        logger.info("Temporary Table: {} (key: {})", getTempTable(), TEMP_TABLE_KEY);
        logger.info("Execution Table: {} (key: {})", getExecutionTable(), EXECUTION_TABLE_KEY);
        logger.info("Plan Table: {} (key: {})", getPlanTable(), PLAN_TABLE_KEY);
        logger.info("Validation result: {}", validateTableNames() ? "PASSED" : "FAILED");
    }
}
