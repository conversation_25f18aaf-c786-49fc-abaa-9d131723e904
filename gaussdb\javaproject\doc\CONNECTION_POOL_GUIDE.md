# 连接池和多数据库支持指南

## 概述

数据迁移服务现在支持高性能的连接池管理和多种数据库类型，包括GaussDB、StarRocks、MySQL、PostgreSQL和Oracle。

## 连接池功能

### 1. HikariCP连接池

使用业界最快的连接池HikariCP，提供以下特性：
- **高性能**: 零开销的连接池实现
- **可靠性**: 连接泄漏检测和自动恢复
- **监控**: 详细的连接池状态监控
- **配置灵活**: 支持自定义连接池参数

### 2. 多数据源管理

- **自动管理**: 根据数据库配置自动创建和管理连接池
- **连接复用**: 相同配置的连接会复用同一个连接池
- **资源清理**: 程序结束时自动关闭所有连接池

## 支持的数据库类型

| 数据库类型 | JDBC驱动 | 连接URL示例 |
|-----------|---------|------------|
| **GaussDB** | com.huawei.gaussdb.jdbc.Driver | ********************************* |
| **StarRocks** | com.mysql.cj.jdbc.Driver | ******************************* |
| **MySQL** | com.mysql.cj.jdbc.Driver | ******************************* |
| **PostgreSQL** | org.postgresql.Driver | ************************************ |
| **Oracle** | oracle.jdbc.driver.OracleDriver | ************************************ |

## 使用方法

### 1. 基本用法（自动检测数据库类型）

```java
// 自动检测数据库类型并使用连接池
Connection conn = DatabaseUtil.getConnection(url, username, password);
```

### 2. 指定数据库类型

```java
import com.example.util.ConnectionPoolManager.DatabaseType;

// 明确指定数据库类型
Connection conn = DatabaseUtil.getConnection(url, username, password, DatabaseType.STARROCKS);
```

### 3. 创建数据库配置

```java
// 创建StarRocks配置
DataMigrationService.DatabaseConfig starRocksConfig = 
    DataMigrationService.DatabaseConfig.createStarRocksConfig(
        "***********", 9030, "analytics_db", "user", "pass");

// 创建GaussDB配置
DataMigrationService.DatabaseConfig gaussdbConfig = 
    DataMigrationService.DatabaseConfig.createGaussDBConfig(
        "**********", 8000, "wbgzdb", "user", "pass");
```

### 4. 自定义连接池配置

```java
// 创建自定义连接池配置
ConnectionPoolManager.PoolConfig poolConfig = new ConnectionPoolManager.PoolConfig();
poolConfig.setMaximumPoolSize(50);      // 最大连接数
poolConfig.setMinimumIdle(10);          // 最小空闲连接数
poolConfig.setConnectionTimeout(60000); // 连接超时时间(毫秒)
poolConfig.setIdleTimeout(300000);      // 空闲超时时间(毫秒)
poolConfig.setMaxLifetime(1800000);     // 连接最大生存时间(毫秒)

// 使用自定义配置获取连接
Connection conn = ConnectionPoolManager.getConnection(
    url, username, password, DatabaseType.STARROCKS, poolConfig);
```

## 连接池管理

### 1. 启用/禁用连接池

```java
// 启用连接池（默认启用）
DatabaseUtil.setUseConnectionPool(true);

// 禁用连接池（使用直接连接）
DatabaseUtil.setUseConnectionPool(false);

// 检查连接池状态
boolean usingPool = DatabaseUtil.isUsingConnectionPool();
```

### 2. 监控连接池状态

```java
// 打印所有连接池的状态信息
DatabaseUtil.printPoolStatus();

// 输出示例：
// Pool: STARROCKS_*********************************************** - Active: 2, Idle: 3, Total: 5, Waiting: 0
// Pool: GAUSSDB_****************************************** - Active: 1, Idle: 4, Total: 5, Waiting: 0
```

### 3. 资源清理

```java
// 关闭指定数据源的连接池
ConnectionPoolManager.closeDataSource(url, username, DatabaseType.STARROCKS);

// 关闭所有连接池（程序结束时调用）
DatabaseUtil.shutdown();
```

## StarRocks特殊配置

### 1. 连接URL格式

StarRocks使用MySQL协议，连接URL格式为：
```
*********************************************
```

### 2. 推荐配置

```java
// StarRocks推荐的连接池配置
ConnectionPoolManager.PoolConfig starRocksPoolConfig = new ConnectionPoolManager.PoolConfig();
starRocksPoolConfig.setMaximumPoolSize(20);     // StarRocks建议不要太大
starRocksPoolConfig.setMinimumIdle(5);
starRocksPoolConfig.setConnectionTimeout(30000); // 30秒
starRocksPoolConfig.setIdleTimeout(600000);      // 10分钟
```

### 3. 元数据视图

StarRocks使用标准的information_schema：
```sql
SELECT column_name, data_type, ordinal_position 
FROM information_schema.columns 
WHERE table_name = 'your_table_name' 
ORDER BY ordinal_position;
```

## 性能优化建议

### 1. 连接池大小

- **CPU密集型**: 连接池大小 = CPU核心数 + 1
- **IO密集型**: 连接池大小 = CPU核心数 × 2
- **混合型**: 根据实际测试调整，通常在10-50之间

### 2. 超时配置

- **connectionTimeout**: 30秒（获取连接的超时时间）
- **idleTimeout**: 10分钟（空闲连接的超时时间）
- **maxLifetime**: 30分钟（连接的最大生存时间）

### 3. 监控和调优

```java
// 定期打印连接池状态
ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
scheduler.scheduleAtFixedRate(() -> {
    DatabaseUtil.printPoolStatus();
}, 0, 60, TimeUnit.SECONDS); // 每分钟打印一次
```

## 故障排除

### 1. 连接池耗尽

```
HikariPool-1 - Connection is not available, request timed out after 30000ms.
```

**解决方案**:
- 检查是否有连接泄漏（未正确关闭连接）
- 增加连接池大小
- 减少连接超时时间

### 2. 数据库驱动未找到

```
java.lang.ClassNotFoundException: com.mysql.cj.jdbc.Driver
```

**解决方案**:
- 确保相应的JDBC驱动已添加到classpath
- 检查Maven依赖配置

### 3. StarRocks连接失败

```
Communications link failure
```

**解决方案**:
- 确认StarRocks FE节点地址和端口正确
- 检查网络连通性
- 验证用户名和密码
- 确认数据库名称存在

## 最佳实践

1. **使用try-with-resources**: 确保连接自动关闭
2. **合理配置连接池**: 根据应用负载调整参数
3. **监控连接池状态**: 定期检查连接池健康状况
4. **程序退出时清理**: 调用shutdown()方法关闭所有连接池
5. **异构数据库迁移**: 为不同数据库类型使用合适的配置
