package com.example;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.lang.reflect.Method;
import java.util.Map;
import java.util.Set;
import java.util.HashSet;
import java.util.Arrays;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class SqlFieldAnalyzerTest {
    
    private SqlFieldAnalyzer analyzer;
    
    @BeforeEach
    void setUp() {
        analyzer = new SqlFieldAnalyzer();
    }
    
    @Test
    void testExtractWhereFields_BasicConditions() throws Exception {
        String sql = "SELECT * FROM users WHERE id = 1 AND name = 'test' AND age > 18";
        Set<String> fields = invokeExtractWhereFields(sql);
        
        assertEquals(3, fields.size());
        assertTrue(fields.contains("id"));
        assertTrue(fields.contains("name"));
        assertTrue(fields.contains("age"));
    }
    
    @Test
    void testExtractWhereFields_NoWhereClause() throws Exception {
        String sql = "SELECT * FROM users ORDER BY id";
        Set<String> fields = invokeExtractWhereFields(sql);
        
        assertTrue(fields.isEmpty());
    }
    
    @Test
    void testExtractWhereFields_ComplexOperators() throws Exception {
        String sql = "SELECT * FROM products WHERE price >= 100 AND category LIKE 'electronics%' AND stock != 0 AND created_date BETWEEN '2023-01-01' AND '2023-12-31'";
        Set<String> fields = invokeExtractWhereFields(sql);
        
        assertEquals(4, fields.size());
        assertTrue(fields.contains("price"));
        assertTrue(fields.contains("category"));
        assertTrue(fields.contains("stock"));
        assertTrue(fields.contains("created_date"));
    }
    
    @Test
    void testExtractWhereFields_WithOrderBy() throws Exception {
        String sql = "SELECT * FROM users WHERE status = 'active' ORDER BY created_date DESC";
        Set<String> fields = invokeExtractWhereFields(sql);
        
        assertEquals(1, fields.size());
        assertTrue(fields.contains("status"));
    }
    
    @Test
    void testExtractWhereFields_FilterReservedWords() throws Exception {
        String sql = "SELECT * FROM users WHERE user_id = 1 AND COUNT > 0 AND name IS NOT NULL";
        Set<String> fields = invokeExtractWhereFields(sql);
        
        assertEquals(2, fields.size());
        assertTrue(fields.contains("user_id"));
        assertTrue(fields.contains("name"));
        assertFalse(fields.contains("COUNT")); // Should be filtered as reserved word
    }
    
    @Test
    void testExtractWhereFields_InCondition() throws Exception {
        String sql = "SELECT * FROM orders WHERE status IN ('pending', 'processing') AND customer_id = 123";
        Set<String> fields = invokeExtractWhereFields(sql);
        
        System.out.println("SQL: " + sql);
        System.out.println("Extracted fields: " + fields);
        
        assertEquals(2, fields.size(), "Expected 2 fields but got: " + fields);
        assertTrue(fields.contains("status"), "Should contain 'status' field");
        assertTrue(fields.contains("customer_id"), "Should contain 'customer_id' field");
    }

    @Test
    void testExtractWhereFields_Exists() throws Exception {
        String sql = "SELECT sec.c_dv_pi_mod as C_DV_PI_MOD, A.C_IDEN AS ID, A.C_SEC_CODE, NVL(SEC.C_SEC_VAR_CODE,?) AS C_SEC_VAR_CODE, A.C_DC_CODE, NVL(SEC.C_MKT_CODE,?) AS C_MKT_CODE,C_YCDZ_STATE, A.C_DV_INVEST_CLS, A.C_DV_ISSUE_MODE, A.C_DTA_CODE, A.C_SETT_MODE,  A.C_TD_CHAN_CODE, A.C_DV_VAR_DUR, A.C_DT_CODE, A.N_TD_AMOUNT, A.N_TD_MONEY, A.N_TD_PRICE, A.N_INCOME,  A.N_SETT_MONEY_FIRST, A.N_SETT_MONEY_DUE,SEC.C_DV_PI_MOD,A.C_DESC, CASE WHEN NVL(A.N_SETT_MONEY_FACT,?) = ? AND NVL(A.N_PAWN_MONEY,?) = ? THEN A.N_SETT_MONEY_DUE ELSE A.N_SETT_MONEY_FACT END AS N_SETT_MONEY_FACT,  A.C_CA_CODE_SETT_DUE AS C_CA_CODE,A.D_TRADE,  A.D_SETT_FIRST, A.D_SETT_DUE,A.D_SETT_FACT,A.N_INTEREST,A.C_TD_NO,A.N_RATE, A.N_TD_MONEY_PORT, STOCK.C_DV_ACCOUNT_CODE,  A.N_PAWN_MONEY, CASE WHEN NVL(SEC.C_SEC_VAR_CODE,?) LIKE ?  THEN NVL(trim(SEC.C_DV_AI_MOD),?)  ELSE ? END AS C_SEC_CODE_TAG,  CASE WHEN A.C_DT_CODE IN (?,?,?)  THEN A.C_DT_CODE ELSE ? END AS C_DV_TYPE_SUB, A.D_EXCHANGE_RATE,NVL(A.C_DEVALUE_STAGE,?) AS C_DEVALUE_STAGE, NVL(A.C_DEVALUE_STAGE,?) AS C_FEE_CODE,  A.C_DV_PLAT,  ? as C_CASH_FLOW_CODE,  CASE WHEN NVL(SEC.C_SEC_VAR_CODE,?) LIKE ? AND NVL(SEC.C_FINANCE_TOOL,?) =?  THEN ?  WHEN (NVL(SEC.C_SEC_VAR_CODE,?) LIKE ? OR NVL(SEC.C_SEC_VAR_CODE,?) LIKE ? OR NVL(SEC.C_SEC_VAR_CODE,?) LIKE ?) AND NVL(SEC.C_FINANCE_TOOL,?) =?  THEN ?  ELSE NVL(SEC.C_FINANCE_TOOL,?) END AS C_FINANCE_TOOL  ,A.N_PAWN_MONEY  ,A.C_SH_ACC_CODE  ,A.C_ORG_CODE  ,A.C_SETT_WAY,A.C_PORT_CODE  ,A.N_MONEY_GZ  ,A.C_PAY_PAT_CODE  ,A.C_SETT_WAY  ,A.N_SETT_MONEY_DUE_FACT  ,A.C_BUSI_TYPE  ,A.N_UNPAID_INT  ,A.C_DV_BCK  ,A.N_COMP_AMT  FROM T_D_AC_TRADE_IVT A LEFT JOIN T_P_SV_SEC_BASE SEC    ON A.C_SEC_CODE = SEC.C_SEC_CODE AND SEC.N_CHECK_STATE = ?  LEFT JOIN T_D_MP_PRE_STOCK STOCK  ON A.C_SEC_CODE = STOCK.C_SEC_CODE and $1 between STOCK.d_ai_begin and STOCK.d_ai_end AND STOCK.N_CHECK_STATE = ?  WHERE A.C_PORT_CODE = $2  AND (A.D_TRADE = $3   ) AND NOT EXISTS (SELECT ? FROM T_D_AC_TRADE_IVT B  WHERE B.C_IDEN_SUB = A.C_IDEN AND B.C_PORT_CODE = $4 AND B.N_CHECK_STATE = ? AND B.C_DT_CODE = ? AND B.C_DATA_IDF LIKE ?)  AND A.C_DT_CODE = $5 AND A.C_TD_TYPE = $6    AND A.C_DV_INVEST_CLS = $7 AND A.N_CHECK_STATE = ? ORDER BY D_TRADE,D_SETT_FACT,C_TD_NO,C_TD_CHAN_CODE ASC";
        Set<String> fields = invokeExtractWhereFields(sql);
        
        System.out.println("SQL: " + sql);
        System.out.println("Extracted fields: " + fields);
        
        // 现在应该包含EXISTS子查询中的C_DATA_IDF字段
        assertEquals(9, fields.size(), "Expected 9 fields but got: " + fields);
        assertTrue(fields.contains("C_PORT_CODE"), "Should contain 'C_PORT_CODE' field");
        assertTrue(fields.contains("D_TRADE"), "Should contain 'D_TRADE' field");
        assertTrue(fields.contains("C_DT_CODE"), "Should contain 'C_DT_CODE' field");
        assertTrue(fields.contains("C_TD_TYPE"), "Should contain 'C_TD_TYPE' field");
        assertTrue(fields.contains("C_DV_INVEST_CLS"), "Should contain 'C_DV_INVEST_CLS' field");
        assertTrue(fields.contains("N_CHECK_STATE"), "Should contain 'N_CHECK_STATE' field");
        assertTrue(fields.contains("C_DATA_IDF"), "Should contain 'C_DATA_IDF' field from EXISTS subquery");
        assertTrue(fields.contains("C_IDEN_SUB"), "Should contain 'C_IDEN_SUB' field from EXISTS subquery");
    }
    
    @Test
    void testExtractWhereFields_LikeOperator() throws Exception {
        String sql = "SELECT * FROM products WHERE category LIKE 'electronics%' AND name LIKE '%phone%' AND description NOT LIKE '%old%'";
        Set<String> fields = invokeExtractWhereFields(sql);
        
        System.out.println("SQL: " + sql);
        System.out.println("Extracted fields: " + fields);
        
        assertEquals(3, fields.size(), "Expected 3 fields but got: " + fields);
        assertTrue(fields.contains("category"), "Should contain 'category' field");
        assertTrue(fields.contains("name"), "Should contain 'name' field");
        assertTrue(fields.contains("description"), "Should contain 'description' field");
    }
    
    @Test
    void testExtractWhereFields_MixedOperators() throws Exception {
        String sql = "SELECT * FROM orders WHERE status = 'active' AND customer_name LIKE 'John%' AND amount > 100 AND created_date BETWEEN '2023-01-01' AND '2023-12-31'";
        Set<String> fields = invokeExtractWhereFields(sql);
        
        System.out.println("SQL: " + sql);
        System.out.println("Extracted fields: " + fields);
        
        assertEquals(4, fields.size(), "Expected 4 fields but got: " + fields);
        assertTrue(fields.contains("status"), "Should contain 'status' field");
        assertTrue(fields.contains("customer_name"), "Should contain 'customer_name' field");
        assertTrue(fields.contains("amount"), "Should contain 'amount' field");
        assertTrue(fields.contains("created_date"), "Should contain 'created_date' field");
    }
    
    @Test
    void testExtractWhereFields_ExistsWithLike() throws Exception {
        String sql = "SELECT * FROM orders o WHERE o.status = 'active' AND EXISTS (SELECT 1 FROM customers c WHERE c.name LIKE 'John%' AND c.id = o.customer_id AND c.category NOT LIKE '%vip%')";
        Set<String> fields = invokeExtractWhereFields(sql);
        
        System.out.println("SQL: " + sql);
        System.out.println("Extracted fields: " + fields);
        
        // 主查询应该提取 status 字段，EXISTS中与主表关联的部分可能提取 customer_id
        assertTrue(fields.contains("status"), "Should contain 'status' field from main query");
        // 注意：EXISTS子查询中的 name 和 category 字段应该通过 extractTableFields 方法归类到 customers 表
    }
    
    @Test
    void testExtractTableFields_ExistsWithLike() throws Exception {
        String sql = "SELECT * FROM orders o WHERE o.status = 'active' AND EXISTS (SELECT 1 FROM customers c WHERE c.name LIKE 'John%' AND c.id = o.customer_id AND c.category NOT LIKE '%vip%')";
        Map<String, Set<String>> tableFields = invokeExtractTableFields(sql);
        
        System.out.println("SQL: " + sql);
        System.out.println("Table fields: " + tableFields);
        
        // 验证 customers 表的字段包含 name 和 category
        assertTrue(tableFields.containsKey("customers"), "Should contain customers table");
        Set<String> customerFields = tableFields.get("customers");
        assertTrue(customerFields.contains("name"), "Should contain 'name' field for customers table");
        assertTrue(customerFields.contains("category"), "Should contain 'category' field for customers table");
        assertTrue(customerFields.contains("id"), "Should contain 'id' field for customers table");
    }
    
    @Test
    void testExtractTableNames_ComplexJoins() throws Exception {
        String sql = "SELECT sec.c_dv_pi_mod as C_DV_PI_MOD, A.C_IDEN AS ID FROM T_D_AC_TRADE_IVT A LEFT JOIN T_P_SV_SEC_BASE SEC ON A.C_SEC_CODE = SEC.C_SEC_CODE AND SEC.N_CHECK_STATE = ? LEFT JOIN T_D_MP_PRE_STOCK STOCK ON A.C_SEC_CODE = STOCK.C_SEC_CODE and $1 between STOCK.d_ai_begin and STOCK.d_ai_end AND STOCK.N_CHECK_STATE = ? WHERE A.C_PORT_CODE = $2";
        
        Set<String> tables = invokeExtractTableNames(sql);
        
        System.out.println("SQL: " + sql);
        System.out.println("Extracted tables: " + tables);
        
        assertEquals(3, tables.size(), "Expected 3 tables but got: " + tables);
        assertTrue(tables.contains("t_d_ac_trade_ivt"), "Should contain 't_d_ac_trade_ivt' table");
        assertTrue(tables.contains("t_p_sv_sec_base"), "Should contain 't_p_sv_sec_base' table");
        assertTrue(tables.contains("t_d_mp_pre_stock"), "Should contain 't_d_mp_pre_stock' table");
    }
    
    @Test
    public void testExtractWhereFields() throws Exception {
        SqlFieldAnalyzer analyzer = new SqlFieldAnalyzer();

        // 给定的 SQL
        String sql = "SELECT SUM(N_AMOUNT) AS N_AMOUNT, SUM(N_ORIG_MONEY) AS N_ORIG_MONEY, " +
                     "SUM(N_PORT_MONEY) AS N_PORT_MONEY, A.C_SEC_CODE, A.C_SEC_VAR_CODE, " +
                     "A.C_DTA_CODE, A.C_DV_INVEST_CLS, A.C_DV_ISSUE_MODE, A.C_MKT_CODE " +
                     "FROM (SELECT A.N_AMOUNT * B.N_FUND_WAY AS N_AMOUNT, " +
                     "A.N_ORIG_MONEY * B.N_FUND_WAY AS N_ORIG_MONEY, " +
                     "A.N_PORT_MONEY * B.N_FUND_WAY AS N_PORT_MONEY, A.C_SEC_CODE, " +
                     "A.C_SEC_VAR_CODE, A.C_DTA_CODE, A.C_DV_INVEST_CLS, A.C_DV_ISSUE_MODE, " +
                     "A.C_MKT_CODE FROM T_D_AI_STOCK A, T_S_DAI_ITEM B " +
                     "WHERE A.C_YEAR_MONTH = $1 AND A.C_DAI_CODE = B.C_DAI_CODE " +
                     "AND A.C_PORT_CODE = $2 AND A.D_STOCK = $3 AND A.C_DAI_CODE <> ? " +
                     "AND A.C_PORT_CLS_CODE = $4 AND A.C_MKT_CODE NOT IN " +
                     "(SELECT C_MKT_CODE FROM T_S_VAT_MKTFILTER) AND A.C_DAI_CODE = ?) " +
                     "GROUP BY A.C_SEC_CODE, A.C_SEC_VAR_CODE, A.C_DTA_CODE, " +
                     "A.C_DV_INVEST_CLS, A.C_DV_ISSUE_MODE, A.C_MKT_CODE";

        // 预期解析出的字段
        Set<String> expectedFields = new HashSet<>(Arrays.asList(
            "C_YEAR_MONTH", "C_DAI_CODE", "C_PORT_CODE", "D_STOCK", "C_PORT_CLS_CODE", "C_MKT_CODE"
        ));

        // 调用方法解析 WHERE 字段
        Set<String> actualFields = invokeExtractWhereFields(sql);

        // 验证解析结果
        assertEquals(expectedFields.size(), actualFields.size(), "解析出的字段数量不正确");
        assertTrue(actualFields.containsAll(expectedFields), "解析出的字段不完整或不正确");
    }
    
    // Helper method to invoke private method using reflection
    private Set<String> invokeExtractWhereFields(String sql) throws Exception {
        Method method = SqlFieldAnalyzer.class.getDeclaredMethod("extractWhereFields", String.class);
        method.setAccessible(true); // 允许访问 private 方法
        return (Set<String>) method.invoke(analyzer, sql);
    }
    
    // Helper method to invoke private method using reflection
    private Map<String, Set<String>> invokeExtractTableFields(String sql) throws Exception {
        Method method = SqlFieldAnalyzer.class.getDeclaredMethod("extractTableFields", String.class);
        method.setAccessible(true);
        return (Map<String, Set<String>>) method.invoke(analyzer, sql);
    }
    
    // Helper method to test private extractTableNames method
    private Set<String> invokeExtractTableNames(String sql) throws Exception {
        Method method = SqlFieldAnalyzer.class.getDeclaredMethod("extractTableNames", String.class);
        method.setAccessible(true);
        return (Set<String>) method.invoke(analyzer, sql);
    }
}

